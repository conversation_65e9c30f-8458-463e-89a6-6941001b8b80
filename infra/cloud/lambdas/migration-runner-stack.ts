import * as cdk from 'aws-cdk-lib';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import { Construct } from 'constructs';
import * as path from 'path';
import { AppConfig } from '../../cloud-config/config';
import { SharedStacks } from '../shared/shared-config';

export interface MigrationRunnerStackProps extends cdk.StackProps {
    sharedStacks: SharedStacks;
    config: AppConfig;
}

export class MigrationRunnerStack extends cdk.Stack {
    constructor(scope: Construct, id: string, props: MigrationRunnerStackProps) {
        super(scope, id, props);

        const { sharedStacks } = props;

        // Use the shared VPC
        const vpc = sharedStacks.vpc.vpc;

        const dbSecret = sharedStacks.dbStack.dbSecret;
        if (!dbSecret) {
            throw new Error('DB secret not found');
        }

        const securityGroup = new ec2.SecurityGroup(this, 'MigrationRunnerLambdaSecurityGroup', {
            vpc,
            description: 'Security group for Migration Runner Lambda in VPC',
            allowAllOutbound: true
        });

        // Migration Runner Lambda
        const migrationRunnerLambda = new lambda.Function(this, 'MigrationRunnerLambda', {
            functionName: 'MigrationRunnerLambda',
            runtime: lambda.Runtime.PROVIDED_AL2023,
            handler: 'main',
            code: lambda.Code.fromAsset(path.join(__dirname, './migration-runner/bootstrap.zip')),
            vpc,
            vpcSubnets: { subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS },
            securityGroups: [securityGroup],
            timeout: cdk.Duration.minutes(5),
            environment: {
                DB_SECRET_NAME: dbSecret.secretName,
                AWS_USE_FIPS_ENDPOINT: 'true',
            },
        });

        // Grant the migration runner lambda permission to read the secret
        dbSecret.grantRead(migrationRunnerLambda);
    }
} 