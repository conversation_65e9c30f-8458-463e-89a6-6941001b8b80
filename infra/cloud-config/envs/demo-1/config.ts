import * as cdk from 'aws-cdk-lib';
import * as s3 from 'aws-cdk-lib/aws-s3';
import { AppConfig } from '../../config';
import { defaultConfig } from '../../default/config';
import { mergeDeep } from '../../merge';

const demo1Overrides: Partial<AppConfig> = {
    vpc: {
        cidr: '10.1.0.0/16',
    },
    environment: {
        accountId: '************',
        envName: 'demo-1',
        domain: 'demo-1.gethero.com',
        primaryRegion: 'us-west-2',
    },
    sharedSecrets: {
        tlsCert: {
            arn: 'arn:aws:secretsmanager:us-west-2:************:secret:server-tls-cert-M8GZh8',
        },
    },
    enableUsernameLogin: true,
    oktaProviders: [
        {
            name: 'Okta',
            metadataUrl: 'https://gethero.okta.com/app/exk1wx7pr3rQqDomN1d8/sso/saml/metadata',
            issuer: 'http://www.okta.com/exk1wx7pr3rQqDomN1d8',
            orgId: '1'
        },
        {
            name: 'OktaSandbox',
            metadataUrl: 'https://gethero.okta.com/app/exk1x3o89uiJlGDNx1d8/sso/saml/metadata',
            issuer: 'http://www.okta.com/exk1x3o89uiJlGDNx1d8',
            orgId: '2'
        }
    ],
    s3buckets: {
        fileRepository: {
            bucketName: 'hero-file-repository',
            description: 'Main file repository for Hero application',
            bucketKeyEnabled: true,
            versioned: true,
            blockPublicAccess: s3.BlockPublicAccess.BLOCK_ALL,
            cors: [
                {
                    allowedHeaders: ['*'],
                    allowedMethods: [
                        s3.HttpMethods.GET,
                        s3.HttpMethods.POST,
                        s3.HttpMethods.PUT,
                        s3.HttpMethods.DELETE,
                        s3.HttpMethods.HEAD,
                    ],
                    allowedOrigins: ['*'],
                    exposedHeaders: ['ETag'],
                    maxAge: 3000,
                },
            ],
            removalPolicy: cdk.RemovalPolicy.RETAIN,
            autoDeleteObjects: false,
            tags: {
                Service: 'hero-core',
                Component: 'file-repository'
            }
        },
        fileRepositoryDev: {
            bucketName: 'hero-file-repository-dev',
            description: 'Dev file repository for Hero application, this will be used in the local docker-compose.yml',
            bucketKeyEnabled: true,
            versioned: true,
            blockPublicAccess: s3.BlockPublicAccess.BLOCK_ALL,
            cors: [
                {
                    allowedHeaders: ['*'],
                    allowedMethods: [
                        s3.HttpMethods.GET,
                        s3.HttpMethods.POST,
                        s3.HttpMethods.PUT,
                        s3.HttpMethods.DELETE,
                        s3.HttpMethods.HEAD,
                    ],
                    allowedOrigins: ['*'],
                    exposedHeaders: ['ETag'],
                    maxAge: 3000,
                },
            ],
            removalPolicy: cdk.RemovalPolicy.RETAIN,
            autoDeleteObjects: false,
            tags: {
                Environment: 'development',
                Service: 'hero-core',
                Component: 'file-repository'
            }
        },
        cameraStreamStorage: {
            bucketName: 'camera-stream-storage',
            description: 'Storage for camera stream clips',
            bucketKeyEnabled: true,
            versioned: false,
            blockPublicAccess: s3.BlockPublicAccess.BLOCK_ALL,
            removalPolicy: cdk.RemovalPolicy.RETAIN,
            autoDeleteObjects: false,
            tags: {
                Service: 'hero-core',
                Component: 'camera-streaming'
            }
        }
    },
    servers: {
        perms: {
            secrets: [
                {
                    secretName: "bots/bot-post-confirmation-lambda-secret",
                    arn: "arn:aws:secretsmanager:us-west-2:************:secret:bots/bot-post-confirmation-lambda-secret-rbnPUs",
                    envVarName: "BOT_POST_CONFIRMATION_LAMBDA_SECRET",
                },
                {
                    secretName: "bots/bot-pre-signup-lambda-secret",
                    arn: "arn:aws:secretsmanager:us-west-2:************:secret:bots/bot-pre-signup-lambda-secret-N0iVNn",
                    envVarName: "BOT_PRE_SIGNUP_LAMBDA_SECRET",
                },
                {
                    secretName: "bots/bot-basic-auth-lambda-secret",
                    arn: "arn:aws:secretsmanager:us-west-2:************:secret:bots/bot-basic-auth-lambda-secret/Communications-mIPo5l",
                    envVarName: "BOT_BASIC_AUTH_LAMBDA_SECRET",
                },
                {
                    secretName: "bots/bot-camera-listener-lambda-secret",
                    arn: "arn:aws:secretsmanager:us-west-2:************:secret:bots/bot-camera-listener-lambda-secret-6D9N6H",
                    envVarName: "BOT_CAMERA_LISTENER_LAMBDA_SECRET",
                }
            ]
        },
        communications: {
            secrets: [
                {
                    secretName: "agora/chatOrgName",
                    arn: "arn:aws:secretsmanager:us-west-2:************:secret:agora/chatOrgName-jUcYoN",
                    envVarName: "AGORA_CHAT_ORG_NAME",
                },
                {
                    secretName: "agora/chatAppName",
                    arn: "arn:aws:secretsmanager:us-west-2:************:secret:agora/chatAppName-o0FVZf",
                    envVarName: "AGORA_CHAT_APP_NAME",
                },
                {
                    secretName: "agora/chatHostURL",
                    arn: "arn:aws:secretsmanager:us-west-2:************:secret:agora/chatHostURL-QKnH3r",
                    envVarName: "AGORA_CHAT_HOST_URL",
                },
                {
                    secretName: "agora/chatAppId",
                    arn: "arn:aws:secretsmanager:us-west-2:************:secret:agora/chatAppId-6rTCWn",
                    envVarName: "AGORA_CHAT_APP_ID",
                },
                {
                    secretName: "agora/appCertificate",
                    arn: "arn:aws:secretsmanager:us-west-2:************:secret:agora/appCertificate-VKw25q",
                    envVarName: "AGORA_APP_CERTIFICATE",
                },
                {
                    secretName: "agora/appId",
                    arn: "arn:aws:secretsmanager:us-west-2:************:secret:agora/appId-4AnyJe",
                    envVarName: "AGORA_APP_ID",
                },
                {
                    secretName: "twilio/account_sid",
                    arn: "arn:aws:secretsmanager:us-west-2:************:secret:twilio/account_sid-SXcJ5k",
                    envVarName: "TWILIO_ACCOUNT_SID",
                },
                {
                    secretName: "twilio/auth_token",
                    arn: "arn:aws:secretsmanager:us-west-2:************:secret:twilio/auth_token-jng1pb",
                    envVarName: "TWILIO_AUTH_TOKEN",
                },
                {
                    secretName: "twilio/api_key_sid",
                    arn: "arn:aws:secretsmanager:us-west-2:************:secret:twilio/api_key_sid-IcdoKa",
                    envVarName: "TWILIO_API_KEY_SID",
                },
                {
                    secretName: "twilio/api_key_secret",
                    arn: "arn:aws:secretsmanager:us-west-2:************:secret:twilio/api_key_secret-J4sCrU",
                    envVarName: "TWILIO_API_KEY_SECRET",
                },
                {
                    secretName: "zello/admin_user",
                    arn: "arn:aws:secretsmanager:us-west-2:************:secret:zello/admin_user-kI9j9m",
                    envVarName: "ZELLO_ADMIN_USER",
                },
                {
                    secretName: "zello/admin_user_password",
                    arn: "arn:aws:secretsmanager:us-west-2:************:secret:zello/admin_user_password-3m6qla",
                    envVarName: "ZELLO_ADMIN_USER_PASSWORD",
                },
                {
                    secretName: "zello/api_key",
                    arn: "arn:aws:secretsmanager:us-west-2:************:secret:zello/api_key-CENjSW",
                    envVarName: "ZELLO_API_KEY",
                },
            ],
        },
        workflow: {
            secrets: [
                {
                    secretName: "bots/bot-post-confirmation-lambda-secret",
                    arn: "arn:aws:secretsmanager:us-west-2:************:secret:bots/bot-post-confirmation-lambda-secret-rbnPUs",
                    envVarName: "BOT_POST_CONFIRMATION_LAMBDA_SECRET",
                },
                {
                    secretName: "bots/bot-camera-listener-lambda-secret",
                    arn: "arn:aws:secretsmanager:us-west-2:************:secret:bots/bot-camera-listener-lambda-secret-6D9N6H",
                    envVarName: "BOT_CAMERA_LISTENER_LAMBDA_SECRET",
                },
                {
                    secretName: "zello/admin_user",
                    arn: "arn:aws:secretsmanager:us-west-2:************:secret:zello/admin_user-kI9j9m",
                    envVarName: "ZELLO_ADMIN_USER",
                },
                {
                    secretName: "zello/admin_user_password",
                    arn: "arn:aws:secretsmanager:us-west-2:************:secret:zello/admin_user_password-3m6qla",
                    envVarName: "ZELLO_ADMIN_USER_PASSWORD",
                },
                {
                    secretName: "zello/api_key",
                    arn: "arn:aws:secretsmanager:us-west-2:************:secret:zello/api_key-CENjSW",
                    envVarName: "ZELLO_API_KEY",
                },
            ]
        },
        orgs: {
            secrets: [
                {
                    secretName: "bots/bot-post-confirmation-lambda-secret",
                    arn: "arn:aws:secretsmanager:us-west-2:************:secret:bots/bot-post-confirmation-lambda-secret-rbnPUs",
                    envVarName: "BOT_POST_CONFIRMATION_LAMBDA_SECRET",
                },
                {
                    secretName: "bots/bot-pre-signup-lambda-secret",
                    arn: "arn:aws:secretsmanager:us-west-2:************:secret:bots/bot-pre-signup-lambda-secret-N0iVNn",
                    envVarName: "BOT_PRE_SIGNUP_LAMBDA_SECRET",
                },
                {
                    secretName: "bots/bot-basic-auth-lambda-secret",
                    arn: "arn:aws:secretsmanager:us-west-2:************:secret:bots/bot-basic-auth-lambda-secret/Communications-mIPo5l",
                    envVarName: "BOT_BASIC_AUTH_LAMBDA_SECRET",
                },
                {
                    secretName: "zello/admin_user",
                    arn: "arn:aws:secretsmanager:us-west-2:************:secret:zello/admin_user-kI9j9m",
                    envVarName: "ZELLO_ADMIN_USER",
                },
                {
                    secretName: "zello/admin_user_password",
                    arn: "arn:aws:secretsmanager:us-west-2:************:secret:zello/admin_user_password-3m6qla",
                    envVarName: "ZELLO_ADMIN_USER_PASSWORD",
                },
                {
                    secretName: "zello/api_key",
                    arn: "arn:aws:secretsmanager:us-west-2:************:secret:zello/api_key-CENjSW",
                    envVarName: "ZELLO_API_KEY",
                },
                {
                    secretName: "twilio/account_sid",
                    arn: "arn:aws:secretsmanager:us-west-2:************:secret:twilio/account_sid-SXcJ5k",
                    envVarName: "TWILIO_ACCOUNT_SID",
                },
                {
                    secretName: "twilio/auth_token",
                    arn: "arn:aws:secretsmanager:us-west-2:************:secret:twilio/auth_token-jng1pb",
                    envVarName: "TWILIO_AUTH_TOKEN",
                },
                {
                    secretName: "twilio/api_key_sid",
                    arn: "arn:aws:secretsmanager:us-west-2:************:secret:twilio/api_key_sid-IcdoKa",
                    envVarName: "TWILIO_API_KEY_SID",
                },
                {
                    secretName: "twilio/api_key_secret",
                    arn: "arn:aws:secretsmanager:us-west-2:************:secret:twilio/api_key_secret-J4sCrU",
                    envVarName: "TWILIO_API_KEY_SECRET",
                },
            ]
        }
    },
    twilioAccountSid: 'AC98c2b9d596b1bc6c0a80e53e7cee8832',
    tags: {
        env: 'demo',
    },
};

export default mergeDeep(defaultConfig, demo1Overrides); 