import * as cdk from 'aws-cdk-lib';
import * as s3 from 'aws-cdk-lib/aws-s3';
import { AppConfig } from '../config';
import { servers } from './servers';

export const defaultConfig: AppConfig = {
    // environment must be set in the env config
    vpc: {
        cidr: '',
    },
    environment: {
        accountId: '',
        envName: '',
        domain: '',
        primaryRegion: '',
    },
    sharedSecrets: {
        tlsCert: {
            arn: '',
        },
    },
    enableUsernameLogin: false,
    sentryDsn: 'https://<EMAIL>/****************',
    twilioAccountSid: '',
    oktaProviders: [],
    sharedEnvironment: {
        accountId: '************',
        ecrRegion: 'us-west-2', // for now, we don't worry about replicating to other regions
    },
    s3buckets: {
        fileRepository: {
            bucketName: 'hero-file-repository',
            description: 'Main file repository for Hero application',
            bucketKeyEnabled: true,
            versioned: true,
            blockPublicAccess: s3.BlockPublicAccess.BLOCK_ALL,
            cors: [
                {
                    allowedHeaders: ['*'],
                    allowedMethods: [
                        s3.HttpMethods.GET,
                        s3.HttpMethods.POST,
                        s3.HttpMethods.PUT,
                        s3.HttpMethods.DELETE,
                        s3.HttpMethods.HEAD,
                    ],
                    allowedOrigins: ['*'],
                    exposedHeaders: ['ETag'],
                    maxAge: 3000,
                },
            ],
            removalPolicy: cdk.RemovalPolicy.RETAIN,
            autoDeleteObjects: false,
            tags: {
                Service: 'hero-core',
                Component: 'file-repository'
            }
        },
        cameraStreamStorage: {
            bucketName: 'camera-stream-storage',
            description: 'Storage for camera stream clips',
            bucketKeyEnabled: true,
            versioned: false,
            blockPublicAccess: s3.BlockPublicAccess.BLOCK_ALL,
            removalPolicy: cdk.RemovalPolicy.RETAIN,
            autoDeleteObjects: false,
            tags: {
                Service: 'hero-core',
                Component: 'camera-streaming'
            }
        }
    },
    snsTopics: {
        cameraStreamObjectDetectionAlerts: {
            topicName: 'CameraStreamObjectDetectionAlerts',
            displayName: 'Camera Stream Object Detection Alerts'
        }
    },
    servers: servers,
    bastion: {
        instanceClass: 't3',
        instanceSize: 'nano',
    },
    db: {
        hero: {
            instanceClass: 't3',
            instanceSize: 'micro',
            multiAz: false,
        },
        perms: {
            instanceClass: 't3',
            instanceSize: 'micro',
            multiAz: false,
        },
    },
    openfga: {
        dockerImage: 'openfga/openfga:latest',
        cpu: 512,
        memoryLimitMiB: 1024,
        desiredCount: 1,
        minCapacity: 1,
        maxCapacity: 20
    },
    tags: {
        env: 'default',
    },
}; 

export default defaultConfig;