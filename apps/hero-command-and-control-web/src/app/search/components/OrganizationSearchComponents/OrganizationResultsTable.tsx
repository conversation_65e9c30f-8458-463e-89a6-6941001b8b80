import { create } from "@bufbuild/protobuf";
import CheckIcon from "@mui/icons-material/Check";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import KeyboardArrowLeftIcon from "@mui/icons-material/KeyboardArrowLeft";
import KeyboardArrowRightIcon from "@mui/icons-material/KeyboardArrowRight";
import SwapVertIcon from "@mui/icons-material/SwapVert";
import {
  Box,
  Checkbox,
  Menu,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from "@mui/material";
import { useRouter } from "next/navigation";
import {
  Entity,
  EntityType,
  SearchEntitiesRequestSchema,
  SearchOrderBy,
} from "proto/hero/entity/v1/entity_pb";
import { useEffect, useState } from "react";
import { Button } from "../../../../design-system/components/Button";
import { Typography } from "../../../../design-system/components/Typography";
import { colors } from "../../../../design-system/tokens";
import { searchEntities } from "../../../apis/services/workflow/entity/endpoints";
import {
  useBatchGetLatestEntities,
  useGetEntitySchemaByVersion,
} from "../../../apis/services/workflow/entity/hooks";
import { ExportModal } from "../../../components/ExportModal";
import { getEntityStatusDisplay } from "../../utils/entityUtils";
import {
  SearchState,
  saveSearchStateForNavigation,
} from "../../utils/searchStateManager";
import { TableSkeleton } from "../TableSkeleton";

const ENTITY_SORT_OPTIONS = [
  { value: SearchOrderBy.RELEVANCE, label: "Relevance" },
  { value: SearchOrderBy.CREATED_AT, label: "Created Date" },
  { value: SearchOrderBy.UPDATED_AT, label: "Updated Date" },
  { value: SearchOrderBy.STATUS, label: "Status" },
];

interface OrganizationResultsTableProps {
  data: any;
  isLoading: boolean;
  isError: boolean;
  error: any;
  page: number;
  rowsPerPage: number;
  totalPages: number;
  totalResults: number;
  nextPageToken?: string;
  handleChangePage: (pageIndex: number) => void;
  onChangeRowsPerPage?: (newRowsPerPage: number) => void;
  selectedSort: SearchOrderBy;
  onSortChange: (sortValue: SearchOrderBy) => void;
  getCurrentSearchState: () => SearchState;
}

export const OrganizationResultsTable = ({
  data,
  isLoading,
  isError,
  error,
  page,
  rowsPerPage,
  totalPages,
  totalResults,
  nextPageToken,
  handleChangePage,
  onChangeRowsPerPage,
  selectedSort,
  onSortChange,
  getCurrentSearchState,
}: OrganizationResultsTableProps) => {
  const router = useRouter();
  const [sortMenuAnchor, setSortMenuAnchor] = useState<null | HTMLElement>(
    null
  );
  const [isExportModalOpen, setIsExportModalOpen] = useState(false);
  const [isExportLoading, setIsExportLoading] = useState(false);
  const [selectedEntities, setSelectedEntities] = useState<Set<string>>(
    new Set()
  );
  const [exportMenuAnchor, setExportMenuAnchor] = useState<null | HTMLElement>(
    null
  );

  // State to track all fetched results
  const [allFetchedResults, setAllFetchedResults] = useState<Entity[]>([]);
  const [isFetchingAll, setIsFetchingAll] = useState(false);

  // Get the current search state for export
  const currentSearchState = getCurrentSearchState();

  // Function to fetch all pages of results
  const fetchAllResults = async () => {
    setIsFetchingAll(true);
    let allEntities: Entity[] = [];
    let currentPageToken: string | undefined = undefined; // Start from the beginning
    const maxPages = 200; // Safety limit to prevent infinite loops
    let pageCount = 0;

    try {
      while (pageCount < maxPages) {
        const response = await searchEntities(
          create(SearchEntitiesRequestSchema, {
            query: currentSearchState.organizationSearchParams?.query,
            pageSize: 100, // Cap to 100 as requested
            orderBy: currentSearchState.organizationSearchParams
              ?.orderBy as any,
            ascending: currentSearchState.organizationSearchParams?.ascending,
            pageToken: currentPageToken,
            entityTypes: [EntityType.ORGANIZATION],
          })
        );

        // Break if no entities returned
        if (!response.entities || response.entities.length === 0) {
          break;
        }

        // Add entities to our collection
        allEntities = [...allEntities, ...response.entities];

        // Break if no more pages
        if (!response.nextPageToken) {
          break;
        }

        // Update page token for next iteration
        currentPageToken = response.nextPageToken;
        pageCount++;
      }

      setAllFetchedResults(allEntities);
    } catch (error) {
      console.error("Error fetching all results:", error);
    } finally {
      setIsFetchingAll(false);
    }
  };

  // Effect to fetch all results when export modal opens
  useEffect(() => {
    if (isExportModalOpen && !selectedEntities.size) {
      fetchAllResults();
    }
  }, [isExportModalOpen, selectedEntities.size]);

  // Fetch complete entity data for export
  const { data: exportData, isLoading: isExportDataLoading } =
    useBatchGetLatestEntities(
      selectedEntities.size > 0
        ? Array.from(selectedEntities)
        : allFetchedResults.map((entity: Entity) => entity.id),
      {
        enabled:
          isExportModalOpen &&
          (selectedEntities.size > 0 || allFetchedResults.length > 0),
        queryKey: [
          "batchGetLatestEntities",
          "export",
          selectedEntities.size || allFetchedResults.length,
        ],
      }
    );

  // Get schema for the first entity (assuming all entities use the same schema)
  const firstEntity = data?.entities?.[0];
  const { data: schema } = useGetEntitySchemaByVersion(
    firstEntity?.schemaId || "",
    firstEntity?.schemaVersion || 0,
    {
      enabled: isExportModalOpen && !!firstEntity?.schemaId,
      queryKey: [
        "entitySchema",
        firstEntity?.schemaId,
        firstEntity?.schemaVersion,
      ],
    }
  );

  const handleDownloadCsv = () => {
    if (!exportData?.entities || exportData.entities.length === 0) {
      console.warn("No data to export.");
      return { data: [], headers: [] };
    }

    // Filter entities based on selection if any are selected
    const entitiesToExport =
      selectedEntities.size > 0
        ? exportData.entities.filter((entity) =>
            selectedEntities.has(entity.id)
          )
        : exportData.entities;

    // Filter out entities with incomplete data
    const completeEntities = entitiesToExport.filter((entity) => {
      try {
        if (!entity.data) return false;
        const data =
          typeof entity.data === "string"
            ? JSON.parse(entity.data)
            : entity.data;
        const organizationInfo = data?.informationSection;
        const hasAddress =
          organizationInfo?.address &&
          Array.isArray(organizationInfo.address) &&
          organizationInfo.address.length > 0 &&
          (organizationInfo.address[0].city ||
            organizationInfo.address[0].state);
        return (
          organizationInfo &&
          (organizationInfo.name ||
            organizationInfo.type ||
            organizationInfo.status ||
            organizationInfo.phone ||
            organizationInfo.email ||
            hasAddress)
        );
      } catch (error) {
        return false;
      }
    });

    // Get fields from schema
    const schemaDefinition = schema?.schemaDefinition as {
      sections?: Array<{
        id: string;
        fields: Array<{ id: string; title?: string }>;
      }>;
    };

    // Get fields from the information section
    const informationSection = schemaDefinition?.sections?.find(
      (section) => section.id === "informationSection"
    );
    const schemaFields = informationSection?.fields || [];

    const csvHeaders = schemaFields.map((field) => ({
      label: field.title || field.id,
      key: field.id,
    }));

    // Add standard entity fields
    csvHeaders.push(
      { label: "ID", key: "id" },
      { label: "Status", key: "status" },
      { label: "Created", key: "created" },
      { label: "Last Updated", key: "lastUpdated" }
    );

    const csvData = completeEntities.map((entity: Entity) => {
      const data =
        typeof entity.data === "string" ? JSON.parse(entity.data) : entity.data;
      const organizationInfo = data?.informationSection || {};

      // Map schema fields
      const mappedData = schemaFields.reduce(
        (acc: Record<string, string>, field) => {
          acc[field.id] = organizationInfo[field.id] || "---";
          return acc;
        },
        {}
      );

      // Add standard entity fields
      return {
        ...mappedData,
        id: entity.id,
        status: entity.status,
        created: formatDate(entity.createTime),
        lastUpdated: formatDate(entity.updateTime),
      };
    });

    return { data: csvData, headers: csvHeaders };
  };

  const isSortMenuOpen = Boolean(sortMenuAnchor);

  const handleSortMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setSortMenuAnchor(event.currentTarget);
  };

  const handleSortMenuClose = () => {
    setSortMenuAnchor(null);
  };

  const handleSortSelect = (sortValue: SearchOrderBy) => {
    onSortChange(sortValue);
    handleSortMenuClose();
  };

  // Handle entity click to navigate to details
  const handleEntityClick = (entity: Entity) => {
    // Save current search state before navigating
    const currentSearchState = getCurrentSearchState();
    saveSearchStateForNavigation(currentSearchState);

    // Navigate to entity details
    router.push(`/entity?entityId=${entity.id}`);
  };

  // Format date
  const formatDate = (dateString: string) => {
    if (!dateString) return "---";
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  // Extract organization name from entity data
  const getOrganizationName = (entity: Entity) => {
    try {
      if (entity.data) {
        const data =
          typeof entity.data === "string"
            ? JSON.parse(entity.data)
            : entity.data;
        const informationSection = data?.informationSection;
        if (informationSection) {
          return informationSection.name || "---";
        }
        return "---";
      }
    } catch (error) {
      console.warn("Failed to parse entity data:", error);
    }
    return "---";
  };

  // Extract organization type from entity data
  const getOrganizationType = (entity: Entity) => {
    try {
      if (entity.data) {
        const data =
          typeof entity.data === "string"
            ? JSON.parse(entity.data)
            : entity.data;
        const informationSection = data?.informationSection;
        if (informationSection) {
          return informationSection.type || "---";
        }
        return "---";
      }
    } catch (error) {
      console.warn("Failed to parse entity data:", error);
    }
    return "---";
  };


  // Extract organization address (street 1, city, state) from entity data
  const getOrganizationAddress = (entity: Entity) => {
    try {
      if (entity.data) {
        const data =
          typeof entity.data === "string"
            ? JSON.parse(entity.data)
            : entity.data;
        const informationSection = data?.informationSection;
        const addressArray = informationSection?.address;

        if (
          addressArray &&
          Array.isArray(addressArray) &&
          addressArray.length > 0
        ) {
          const addressData = addressArray[0];
          if (!addressData) return "---";

          const streetAddress1 = addressData.streetAddress1 || "";
          const city = addressData.city || "";
          const state = addressData.state || "";

          const parts = [streetAddress1, city, state].filter(Boolean);
          return parts.length > 0 ? parts.join(", ") : "---";
        }
        return "---";
      }
    } catch (error) {
      console.warn("Failed to parse entity data:", error);
    }
    return "---";
  };

  // Extract last updated date from entity data
  const getLastUpdated = (entity: Entity) => {
    try {
      if (entity.updateTime) {
        const date = new Date(entity.updateTime);
        return date.toLocaleDateString();
      }
      return "---";
    } catch (error) {
      console.warn("Failed to parse update time:", error);
      return "---";
    }
  };

  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      const newSelected = new Set<string>(
        data?.entities?.map((entity: Entity) => entity.id) || []
      );
      setSelectedEntities(newSelected);
    } else {
      setSelectedEntities(new Set<string>());
    }
  };

  const handleSelectEntity = (entityId: string) => {
    const newSelected = new Set(selectedEntities);
    if (newSelected.has(entityId)) {
      newSelected.delete(entityId);
    } else {
      newSelected.add(entityId);
    }
    setSelectedEntities(newSelected);
  };

  // Custom pagination component
  const CustomPagination = () => {
    const getPageNumbers = () => {
      if (totalPages <= 1) {
        return [0];
      }

      const pageNumbers = [];

      if (totalPages <= 7) {
        for (let i = 0; i < totalPages; i++) {
          pageNumbers.push(i);
        }
      } else {
        if (page <= 3) {
          // Near beginning: 1 2 3 4 5 ... 10
          for (let i = 0; i < 5; i++) {
            pageNumbers.push(i);
          }
          pageNumbers.push(-1);
          pageNumbers.push(totalPages - 1);
        } else if (page >= totalPages - 4) {
          // Near end: 1 ... 6 7 8 9 10
          pageNumbers.push(0);
          pageNumbers.push(-1);
          for (let i = totalPages - 5; i < totalPages; i++) {
            pageNumbers.push(i);
          }
        } else {
          // Middle: 1 ... 3 4 5 ... 10
          pageNumbers.push(0);
          pageNumbers.push(-1);
          pageNumbers.push(page - 1);
          pageNumbers.push(page);
          pageNumbers.push(page + 1);
          pageNumbers.push(-2);
          pageNumbers.push(totalPages - 1);
        }
      }

      return pageNumbers;
    };

    const pageNumbers = getPageNumbers();

    if (totalResults === 0) {
      return null;
    }

    return (
      <Box sx={{ display: "flex", justifyContent: "center", mt: 2, mb: 2 }}>
        <Button
          label="Previous"
          style="ghost"
          color="grey"
          onClick={() => {
            if (page > 0 && !isLoading) {
              handleChangePage(page - 1);
            }
          }}
          disabled={page === 0 || isLoading}
          leftIcon={<KeyboardArrowLeftIcon />}
        />

        {pageNumbers.map((pageNum, index) => {
          if (pageNum === -1 || pageNum === -2) {
            return (
              <Box
                key={`ellipsis-${index}`}
                sx={{
                  display: "flex",
                  alignItems: "center",
                  mx: 1,
                }}
              >
                <Typography style="body2" color={colors.grey[500]}>
                  ...
                </Typography>
              </Box>
            );
          }

          return (
            <Box key={pageNum} sx={{ mx: 0.5 }}>
              <Button
                label={(pageNum + 1).toString()}
                style={page === pageNum ? "filled" : "ghost"}
                color="grey"
                prominence={false}
                onClick={() => !isLoading && handleChangePage(pageNum)}
                disabled={isLoading}
              />
            </Box>
          );
        })}

        <Button
          label="Next"
          style="ghost"
          color="grey"
          onClick={() => {
            if (page < totalPages - 1 && !isLoading) {
              handleChangePage(page + 1);
            }
          }}
          disabled={page >= totalPages - 1 || !nextPageToken || isLoading}
          rightIcon={<KeyboardArrowRightIcon />}
        />
      </Box>
    );
  };

  return (
    <Box
      sx={{
        flex: 1,
        display: "flex",
        flexDirection: "column",
        bgcolor: "white",
        borderRadius: 2,
        boxShadow: "none",
        overflow: "hidden",
        border: `1px solid ${colors.grey[200]}`,
      }}
    >
      <Box
        sx={{
          px: "32px",
          py: "24px",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Typography style="caps1" color={colors.grey[900]}>
          RESULTS
        </Typography>
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <Box sx={{ mr: 3, display: "flex", alignItems: "center" }}>
            <Button
              label="Table Sort"
              size="small"
              style={isSortMenuOpen ? "filled" : "ghost"}
              color="grey"
              prominence={isSortMenuOpen ? true : false}
              leftIcon={<SwapVertIcon />}
              rightIcon={<KeyboardArrowDownIcon />}
              onClick={handleSortMenuOpen}
            />
          </Box>
          <Box sx={{ display: "flex", alignItems: "center" }}>
            <Button
              label="Export to CSV"
              size="small"
              style={selectedEntities.size > 0 ? "filled" : "ghost"}
              color="grey"
              prominence={selectedEntities.size > 0}
              rightIcon={<KeyboardArrowDownIcon />}
              onClick={(e) => {
                const menuAnchor = e.currentTarget;
                setExportMenuAnchor(menuAnchor);
              }}
            />
            <Menu
              anchorEl={exportMenuAnchor}
              open={Boolean(exportMenuAnchor)}
              onClose={() => setExportMenuAnchor(null)}
              PaperProps={{
                sx: {
                  mt: 1,
                  minWidth: 200,
                  borderRadius: 2,
                  border: `1px solid ${colors.grey[200]}`,
                  boxShadow: "0px 4px 16px rgba(0, 0, 0, 0.1)",
                },
              }}
            >
              <MenuItem
                onClick={() => {
                  setExportMenuAnchor(null);
                  setIsExportModalOpen(true);
                }}
                sx={{
                  px: 3,
                  py: 2,
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  "&:hover": {
                    backgroundColor: colors.grey[50],
                  },
                }}
              >
                <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="22"
                    height="22"
                    viewBox="0 0 22 22"
                    fill="none"
                  >
                    <path
                      d="M20.1673 4.74825L9.70815 15.2166L5.82148 11.3299L7.11398 10.0374L9.70815 12.6316L18.8748 3.46492L20.1673 4.74825ZM18.1415 9.36825C18.2607 9.89075 18.334 10.4408 18.334 10.9999C18.334 15.0516 15.0523 18.3333 11.0007 18.3333C6.94898 18.3333 3.66732 15.0516 3.66732 10.9999C3.66732 6.94825 6.94898 3.66659 11.0007 3.66659C12.449 3.66659 13.7873 4.08825 14.924 4.81242L16.244 3.49242C14.759 2.44742 12.9532 1.83325 11.0007 1.83325C5.94065 1.83325 1.83398 5.93992 1.83398 10.9999C1.83398 16.0599 5.94065 20.1666 11.0007 20.1666C16.0607 20.1666 20.1673 16.0599 20.1673 10.9999C20.1673 9.90909 19.9657 8.86408 19.6173 7.89242L18.1415 9.36825Z"
                      fill="#364153"
                    />
                  </svg>
                  <Typography style="body2" color={colors.grey[900]}>
                    Selected Results ({selectedEntities.size})
                  </Typography>
                </Box>
              </MenuItem>
              <MenuItem
                onClick={() => {
                  setExportMenuAnchor(null);
                  setIsExportModalOpen(true);
                  setSelectedEntities(new Set());
                }}
                sx={{
                  px: 3,
                  py: 2,
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  "&:hover": {
                    backgroundColor: colors.grey[50],
                  },
                }}
              >
                <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="22"
                    height="22"
                    viewBox="0 0 22 22"
                    fill="none"
                  >
                    <path
                      d="M6.42183 5.50008H15.5885L10.996 11.2751L6.42183 5.50008ZM3.901 5.14258C5.75267 7.51675 9.17183 11.9167 9.17183 11.9167V17.4167C9.17183 17.9209 9.58433 18.3334 10.0885 18.3334H11.9218C12.426 18.3334 12.8385 17.9209 12.8385 17.4167V11.9167C12.8385 11.9167 16.2485 7.51675 18.1002 5.14258C18.5677 4.53758 18.1368 3.66675 17.376 3.66675H4.62517C3.86433 3.66675 3.4335 4.53758 3.901 5.14258Z"
                      fill="#4A5565"
                    />
                  </svg>
                  <Typography style="body2" color={colors.grey[900]}>
                    {currentSearchState.organizationSearchParams?.query
                      ? "All Search Results"
                      : "All Results"}
                  </Typography>
                </Box>
              </MenuItem>
            </Menu>
          </Box>
        </Box>
      </Box>

      <TableContainer
        component={Box}
        sx={{
          flexGrow: 1,
          width: "100%",
          overflowY: "auto",
          overflowX: "auto",
          px: "24px",
          height: "calc(100vh - 300px)",
        }}
      >
        {isLoading ? (
          <TableSkeleton tableType="Organizations" />
        ) : isError ? (
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              height: "100%",
              p: 4,
            }}
          >
            <Box
              sx={{
                bgcolor: colors.rose[50],
                color: colors.rose[700],
                p: 4,
                borderRadius: 1,
              }}
            >
              Error:{" "}
              {error?.message ||
                "An error occurred while fetching organizations"}
            </Box>
          </Box>
        ) : (
          <Table
            stickyHeader
            aria-label="organizations table"
            sx={{
              tableLayout: "fixed",
              width: "100%",
            }}
          >
            <TableHead>
              <TableRow>
                <TableCell padding="checkbox" sx={{ width: "48px" }} />
                <TableCell sx={{ width: "25%" }}>
                  <Typography style="caps3" color={colors.grey[800]}>
                    NAME
                  </Typography>
                </TableCell>
                <TableCell sx={{ width: "15%" }}>
                  <Typography style="caps3" color={colors.grey[800]}>
                    TYPE
                  </Typography>
                </TableCell>
                <TableCell sx={{ width: "30%" }}>
                  <Typography style="caps3" color={colors.grey[800]}>
                    ADDRESS
                  </Typography>
                </TableCell>
                <TableCell sx={{ width: "15%" }}>
                  <Typography style="caps3" color={colors.grey[800]}>
                    LAST UPDATED
                  </Typography>
                </TableCell>
                <TableCell sx={{ width: "15%" }}>
                  <Typography style="caps3" color={colors.grey[800]}>
                    STATUS
                  </Typography>
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {data?.entities && data.entities.length > 0 ? (
                data.entities.map((entity: Entity) => (
                  <TableRow
                    hover
                    key={entity.id}
                    onClick={() => handleEntityClick(entity)}
                    sx={{
                      cursor: "pointer",
                      height: "54px",
                      "&:last-child td, &:last-child th": { border: 0 },
                      "&:hover": {
                        backgroundColor: colors.grey[50],
                      },
                    }}
                  >
                    <TableCell
                      padding="checkbox"
                      onClick={(e) => e.stopPropagation()}
                    >
                      <Checkbox
                        checked={selectedEntities.has(entity.id)}
                        onChange={() => handleSelectEntity(entity.id)}
                      />
                    </TableCell>
                    <TableCell>
                      <Box
                        sx={{
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          whiteSpace: "nowrap",
                        }}
                      >
                        {getOrganizationName(entity)}
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box
                        sx={{
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          whiteSpace: "nowrap",
                        }}
                      >
                        {getOrganizationType(entity)}
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box
                        sx={{
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          whiteSpace: "nowrap",
                        }}
                      >
                        {getOrganizationAddress(entity)}
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box
                        sx={{
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          whiteSpace: "nowrap",
                        }}
                      >
                        {getLastUpdated(entity)}
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box
                        sx={{
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          whiteSpace: "nowrap",
                        }}
                      >
                        {getEntityStatusDisplay(entity.status)}
                      </Box>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow sx={{ height: 300 }}>
                  <TableCell
                    colSpan={6}
                    align="center"
                    sx={{
                      borderTop: "none",
                      borderLeft: "none",
                      borderRight: "none",
                    }}
                  >
                    <Typography style="body1" color={colors.grey[600]}>
                      {data
                        ? "No organizations found. Try adjusting your filters."
                        : "Use the search and filter options to find organizations."}
                    </Typography>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        )}
      </TableContainer>

      {/* Show pagination if we have pages to navigate (persists during page navigation) */}
      {(totalPages > 1 || totalResults > 0) && <CustomPagination />}

      <Menu
        anchorEl={sortMenuAnchor}
        open={isSortMenuOpen}
        onClose={handleSortMenuClose}
        PaperProps={{
          sx: {
            mt: 1,
            minWidth: 200,
            borderRadius: 2,
            border: `1px solid ${colors.grey[200]}`,
            boxShadow: "0px 4px 16px rgba(0, 0, 0, 0.1)",
          },
        }}
      >
        {ENTITY_SORT_OPTIONS.map((option) => (
          <MenuItem
            key={option.value}
            onClick={() => handleSortSelect(option.value)}
            sx={{
              px: 3,
              py: 2,
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              "&:hover": {
                backgroundColor: colors.grey[50],
              },
            }}
          >
            <Typography
              style={selectedSort === option.value ? "body1" : "body2"}
              color={
                selectedSort === option.value
                  ? colors.grey[900]
                  : colors.grey[500]
              }
            >
              {option.label}
            </Typography>
            {selectedSort === option.value && (
              <CheckIcon
                sx={{
                  color: colors.blue[600],
                  fontSize: 20,
                  ml: 2,
                }}
              />
            )}
          </MenuItem>
        ))}
      </Menu>

      <ExportModal
        open={isExportModalOpen}
        onClose={() => setIsExportModalOpen(false)}
        columns={
          (
            schema?.schemaDefinition as {
              sections?: Array<{ fields: Array<{ id: string }> }>;
            }
          )?.sections?.flatMap((section) => section.fields).length || 0
        }
        rows={
          selectedEntities.size > 0
            ? selectedEntities.size
            : allFetchedResults.length || 0
        }
        fileSize={`${Math.round(
          (selectedEntities.size > 0
            ? selectedEntities.size
            : allFetchedResults.length || 0) * 0.5
        )} KB`}
        csvData={handleDownloadCsv().data}
        csvHeaders={handleDownloadCsv().headers}
        isLoading={isExportDataLoading || isFetchingAll}
        filename="organizations_export.csv"
      />
    </Box>
  );
};
