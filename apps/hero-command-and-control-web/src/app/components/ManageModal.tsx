"use client";

import {
  caseRolePermissionKeys,
  situationRolePermissionKeys,
  useListCaseRolePermissions,
  useListSituationRolePermissions,
  useUpdateCaseRolePermission,
  useUpdateSituationRolePermission,
} from "@/app/apis/services/perms/hooks";
import { stringToAssetType } from "@/app/apis/services/workflow/assets/enumConverters";
import { useListAssets } from "@/app/apis/services/workflow/assets/hooks";
import { stringToCaseAssetAssociationType } from "@/app/apis/services/workflow/cases/enumConverters";
import {
  useAssociateAssetToCase,
  useDisassociateAssetFromCase,
  useListAssetAssociationsForCase,
  useUpdateAssetAssociation,
} from "@/app/apis/services/workflow/cases/hooks";
import { Button } from "@/design-system/components/Button";
import { Dropdown } from "@/design-system/components/Dropdown";
import {
  InputMask,
  InputType,
  TextInput,
} from "@/design-system/components/TextInput";
import { Typography as TypographyDS } from "@/design-system/components/Typography";
import { colors } from "@/design-system/tokens";
import AddIcon from "@mui/icons-material/Add";
import CheckIcon from "@mui/icons-material/Check";
import CloseIcon from "@mui/icons-material/Close";
import DeleteIcon from "@mui/icons-material/Delete";
import ImportExportIcon from "@mui/icons-material/ImportExport";
import {
  Autocomplete,
  Box,
  Paper,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tabs,
  TextField,
} from "@mui/material";
import { useQueryClient } from "@tanstack/react-query";
import {
  Asset,
  AssetType,
  ListAssetsRequest,
} from "proto/hero/assets/v2/assets_pb";
import { CaseAssetAssociationType } from "proto/hero/cases/v1/cases_pb";
import {
  ListCaseRolePermissionsRequest,
  ListSituationRolePermissionsRequest,
  ObjectViewer,
  UpdateCaseRolePermissionRequest,
  UpdateSituationRolePermissionRequest,
} from "proto/hero/permissions/v1/permissions_pb";
import React, { useMemo, useState } from "react";

const PERMISSION_DISPLAY_MAP: Record<string, string> = {
  CAN_MANAGE: "Can Manage",
  CAN_EDIT: "Can Edit",
  CAN_VIEW: "Can View",
  CAN_FIND: "Can Find",
  BLOCKED: "Blocked",
};

const toDisplay = (permission: string) =>
  PERMISSION_DISPLAY_MAP[permission] || permission;

const toRaw = (permission: string) => {
  return (
    Object.keys(PERMISSION_DISPLAY_MAP).find(
      (key) => PERMISSION_DISPLAY_MAP[key] === permission
    ) || permission
  );
};

const PERMISSION_OPTIONS = Object.values(PERMISSION_DISPLAY_MAP);

interface RolePermission {
  id: string;
  name: string;
  permission: string;
}

interface UserPermission {
  id: string;
  name: string;
  permission: string;
}

interface ManageModalProps {
  onClose: () => void;
  objectId: string;
  objectType: "situation" | "case";
  showAssignTab: boolean;
  showDetailsTab: boolean;
  objectName: string;
  isForcedAssignment?: boolean;
  onAssignmentComplete?: () => void;
  disableAssignment?: boolean;
}

const ManageModal: React.FC<ManageModalProps> = ({
  onClose,
  objectId,
  objectType,
  showAssignTab,
  showDetailsTab,
  objectName,
  isForcedAssignment = false,
  onAssignmentComplete,
  disableAssignment = false,
}) => {
  const queryClient = useQueryClient();
  // Tab indices based on which tabs are shown
  const assignTabIndex = showAssignTab ? 0 : -1; // -1 if not shown
  const permissionTabIndex = showAssignTab ? 1 : 0;
  const detailsTabIndex = showDetailsTab ? (showAssignTab ? 2 : 1) : -1; // -1 if not shown

  // Ensure activeTab is valid based on available tabs
  const maxTabIndex = showDetailsTab
    ? showAssignTab
      ? 2
      : 1
    : showAssignTab
      ? 1
      : 0;
  const [activeTab, setActiveTab] = useState(0);

  // Set active tab to assign tab when in forced assignment mode
  React.useEffect(() => {
    if (isForcedAssignment && showAssignTab) {
      setActiveTab(assignTabIndex);
    } else if (!showAssignTab) {
      // If assign tab is not shown, default to permissions tab
      setActiveTab(permissionTabIndex);
    }
  }, [isForcedAssignment, assignTabIndex, showAssignTab, permissionTabIndex]);
  const [selectedUser, setSelectedUser] = useState<string | null>(null);
  const [selectedPermission, setSelectedPermission] = useState<string | null>(
    null
  );
  const [inputValue, setInputValue] = useState("");
  const [editingUserId, setEditingUserId] = useState<string | null>(null);
  const [editingRoleId, setEditingRoleId] = useState<string | null>(null);
  const [editingPermission, setEditingPermission] = useState<string | null>(
    null
  );

  // Assign tab state
  const [currentAssignee, setCurrentAssignee] = useState<string | null>(null);
  const [newAssignee, setNewAssignee] = useState<string | null>(null);

  // Fetch assets using ListAssets API
  const { data: assetsData } = useListAssets({
    pageSize: 100,
    pageToken: "",
    orderBy: "name asc",
  } as ListAssetsRequest);

  // Filter assets to show only relevant types (responders, dispatchers, supervisors)
  const availableAssets = useMemo(() => {
    if (!assetsData?.assets) return [];

    return assetsData.assets.filter((asset: Asset) => {
      // Convert asset type to enum value using the converter
      const assetType =
        typeof asset.type === "string"
          ? stringToAssetType(asset.type)
          : asset.type;

      // Check for enum values
      return (
        assetType === AssetType.RESPONDER ||
        assetType === AssetType.DISPATCHER ||
        assetType === AssetType.SUPERVISOR
      );
    });
  }, [assetsData]);

  const { data: situationRolePermissionsData } =
    useListSituationRolePermissions(
      {
        situationId: objectId,
      } as ListSituationRolePermissionsRequest,
      {
        enabled: objectType === "situation",
      }
    );

  const { data: caseRolePermissionsData } = useListCaseRolePermissions(
    {
      caseId: objectId,
    } as ListCaseRolePermissionsRequest,
    {
      enabled: objectType === "case",
    }
  );

  const rolePermissionsData =
    objectType === "situation"
      ? situationRolePermissionsData
      : caseRolePermissionsData;

  const { mutate: setSituationObjectPermissions } =
    useUpdateSituationRolePermission();
  const { mutate: setCaseObjectPermissions } = useUpdateCaseRolePermission();

  // Case assignment mutations
  const { mutate: associateAssetToCase } = useAssociateAssetToCase();
  const { mutate: updateAssetAssociation } = useUpdateAssetAssociation();
  const { mutate: disassociateAssetFromCase } = useDisassociateAssetFromCase();

  // Fetch asset associations for the case
  const { data: assetAssociationsData, isLoading: isLoadingAssetAssociations } =
    useListAssetAssociationsForCase(
      {
        caseId: objectId,
        pageSize: 50,
        pageToken: "",
      } as any,
      {
        enabled: objectType === "case" && !!objectId,
        staleTime: 5 * 60 * 1000, // 5 minutes
      }
    );

  // Get the primary investigator from asset associations
  const primaryInvestigator = useMemo(() => {
    console.log("Asset associations data:", assetAssociationsData);
    console.log("Available assets:", availableAssets);

    if (!assetAssociationsData?.associations) {
      console.log("No asset associations data available");
      return null;
    }

    console.log("All associations:", assetAssociationsData.associations);
    console.log(
      "Association types:",
      assetAssociationsData.associations.map((a) => ({
        id: a.id,
        associationType: a.associationType,
        assetId: a.assetId,
      }))
    );
    console.log(
      "First association keys:",
      Object.keys(assetAssociationsData.associations[0] || {})
    );
    console.log(
      "First association associationType value:",
      assetAssociationsData.associations[0]?.associationType
    );
    console.log(
      "First association associationType type:",
      typeof assetAssociationsData.associations[0]?.associationType
    );

    // Find the most recent primary investigator (sort by assignedAt descending)
    const primaryInvestigators = assetAssociationsData.associations
      .filter((assoc) => {
        // Convert string association type to enum for comparison
        const associationType =
          typeof assoc.associationType === "string"
            ? stringToCaseAssetAssociationType(assoc.associationType)
            : assoc.associationType;

        console.log("Original associationType:", assoc.associationType);
        console.log("Converted associationType:", associationType);
        console.log(
          "Expected enum value:",
          CaseAssetAssociationType.ASSET_ASSOCIATION_TYPE_PRIMARY_INVESTIGATOR
        );
        console.log(
          "Comparison result:",
          associationType ===
          CaseAssetAssociationType.ASSET_ASSOCIATION_TYPE_PRIMARY_INVESTIGATOR
        );

        return (
          associationType ===
          CaseAssetAssociationType.ASSET_ASSOCIATION_TYPE_PRIMARY_INVESTIGATOR
        );
      })
      .sort((a, b) => {
        // Safely parse dates with fallback for missing or invalid dates
        const getDateValue = (dateString: string | undefined): number => {
          if (!dateString) return 0; // Treat missing dates as oldest

          const date = new Date(dateString);
          return isNaN(date.getTime()) ? 0 : date.getTime();
        };

        return getDateValue(b.assignedAt) - getDateValue(a.assignedAt);
      });

    const primaryAssoc = primaryInvestigators[0];

    if (!primaryAssoc) {
      console.log("No primary investigator found in associations");
      return null;
    }

    console.log("Found primary association:", primaryAssoc);

    // Find the asset details
    const asset = availableAssets.find((a) => a.id === primaryAssoc.assetId);
    if (!asset) {
      console.log("Asset not found in available assets:", primaryAssoc.assetId);
      return null;
    }

    console.log("Found asset:", asset);

    return {
      id: primaryAssoc.id,
      assetId: primaryAssoc.assetId,
      name: asset.name,
      permission: "Assigned Investigator",
      association: primaryAssoc,
    };
  }, [assetAssociationsData, availableAssets]);

  const assignedRolePermissions: RolePermission[] =
    rolePermissionsData?.objectViewers.map((viewer: ObjectViewer) => ({
      id: viewer.roleId,
      name: viewer.roleName,
      permission: viewer.permission
        ? (viewer.permission as unknown as string).replace(
          "OBJECT_PERMISSION_",
          ""
        )
        : "OTHER",
    })) || [];

  // Use actual assets instead of mockUsers
  const assets = availableAssets;

  // Get the actual assigned user from case data
  const assignedUser: { id: string; name: string; permission: string } | null =
    primaryInvestigator;

  const handleRemovePermission = (userId: string) => {
    // This would remove the assigned user
    console.log("Remove user:", userId);
  };

  const handleRemoveRolePermission = (roleId: string) => {
    const commonPayload = {
      roleId,
      permission: 5, // BLOCKED
    };

    const onSuccess = () => {
      if (objectType === "situation") {
        queryClient.invalidateQueries({
          queryKey: situationRolePermissionKeys.list({
            situationId: objectId,
          } as ListSituationRolePermissionsRequest),
        });
      } else if (objectType === "case") {
        queryClient.invalidateQueries({
          queryKey: caseRolePermissionKeys.list({
            caseId: objectId,
          } as ListCaseRolePermissionsRequest),
        });
      }
    };

    if (objectType === "situation") {
      setSituationObjectPermissions(
        {
          ...commonPayload,
          situationId: objectId,
        } as UpdateSituationRolePermissionRequest,
        { onSuccess }
      );
    } else if (objectType === "case") {
      setCaseObjectPermissions(
        {
          ...commonPayload,
          caseId: objectId,
        } as UpdateCaseRolePermissionRequest,
        { onSuccess }
      );
    }
  };

  const handlePermissionChange = (userId: string, newPermission: string) => {
    // This would update the user permission
    console.log("Update permission:", userId, newPermission);
    setEditingUserId(userId);
  };

  const handleRolePermissionChange = (
    roleId: string,
    newPermission: string
  ) => {
    setEditingRoleId(roleId);
    setEditingPermission(toRaw(newPermission));
  };

  const handleSaveRolePermission = (roleId: string) => {
    if (!editingPermission) {
      console.error("No permission to save");
      return;
    }

    const permissionMap: Record<string, number> = {
      CAN_MANAGE: 1,
      CAN_EDIT: 2,
      CAN_VIEW: 3,
      CAN_FIND: 4,
      BLOCKED: 5,
    };

    const permission = permissionMap[editingPermission];
    if (!permission) {
      console.error("Invalid permission:", editingPermission);
      return;
    }

    const commonPayload = {
      roleId,
      permission,
    };

    const onSuccess = () => {
      setEditingRoleId(null);
      setEditingPermission(null);
      if (objectType === "situation") {
        queryClient.invalidateQueries({
          queryKey: situationRolePermissionKeys.list({
            situationId: objectId,
          } as ListSituationRolePermissionsRequest),
        });
      } else if (objectType === "case") {
        queryClient.invalidateQueries({
          queryKey: caseRolePermissionKeys.list({
            caseId: objectId,
          } as ListCaseRolePermissionsRequest),
        });
      }
    };

    if (objectType === "situation") {
      setSituationObjectPermissions(
        {
          ...commonPayload,
          situationId: objectId,
        } as UpdateSituationRolePermissionRequest,
        { onSuccess }
      );
    } else if (objectType === "case") {
      setCaseObjectPermissions(
        {
          ...commonPayload,
          caseId: objectId,
        } as UpdateCaseRolePermissionRequest,
        { onSuccess }
      );
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    // In forced assignment mode, only allow the assign tab
    if (isForcedAssignment && newValue !== assignTabIndex) {
      return;
    }

    // Ensure the new value is within valid range
    if (newValue >= 0 && newValue <= maxTabIndex) {
      setActiveTab(newValue);
    }
  };

  const objectTypeDisplay = objectType === "situation" ? "Incident" : "Case";

  const handleReassignClick = async () => {
    if (newAssignee && newAssignee !== currentAssignee) {
      // Find the selected asset
      const selectedAsset = assets.find((asset) => asset.id === newAssignee);
      if (selectedAsset) {
        console.log("Reassigning case to:", selectedAsset.name);

        // Check if the selected asset is already assigned as a primary investigator
        const isAlreadyAssigned = assetAssociationsData?.associations?.some(
          (assoc) => {
            // Convert string association type to enum for comparison
            const associationType =
              typeof assoc.associationType === "string"
                ? stringToCaseAssetAssociationType(assoc.associationType)
                : assoc.associationType;

            return (
              associationType ===
              CaseAssetAssociationType.ASSET_ASSOCIATION_TYPE_PRIMARY_INVESTIGATOR &&
              assoc.assetId === selectedAsset.id
            );
          }
        );

        // Only prevent reassignment if NOT in forced assignment mode (reopening)
        if (isAlreadyAssigned && !isForcedAssignment) {
          console.log(
            "Asset is already assigned as primary investigator, no action needed"
          );
          // Clear the selection since it's already assigned
          setNewAssignee(null);
          return;
        }

        // For forced assignment mode (reopening), allow reassigning the same asset
        if (isAlreadyAssigned && isForcedAssignment) {
          console.log(
            "Reopening case - allowing reassignment of same primary investigator"
          );
        }

        // Get all existing primary investigator associations
        const existingPrimaryInvestigators =
          assetAssociationsData?.associations?.filter((assoc) => {
            // Convert string association type to enum for comparison
            const associationType =
              typeof assoc.associationType === "string"
                ? stringToCaseAssetAssociationType(assoc.associationType)
                : assoc.associationType;
            return (
              associationType ===
              CaseAssetAssociationType.ASSET_ASSOCIATION_TYPE_PRIMARY_INVESTIGATOR
            );
          }) || [];

        console.log(
          "Found existing primary investigators:",
          existingPrimaryInvestigators
        );

        if (existingPrimaryInvestigators.length > 0) {
          // Step 1: Remove ALL existing primary investigators
          console.log(
            `Removing ${existingPrimaryInvestigators.length} existing primary investigator(s)`
          );

          // Create a robust removal function that handles all disassociations
          const removeAllPrimaryInvestigators = async () => {
            const removalPromises = existingPrimaryInvestigators.map(
              async (investigator) => {
                console.log(`Removing association ID: ${investigator.id}`);

                return new Promise<void>((resolve, reject) => {
                  disassociateAssetFromCase(
                    {
                      caseId: objectId,
                      associationId: investigator.id,
                    } as any,
                    {
                      onSuccess: () => {
                        console.log(
                          `Successfully removed association: ${investigator.id}`
                        );
                        resolve();
                      },
                      onError: (error) => {
                        console.error(
                          `Failed to remove association ${investigator.id}:`,
                          error
                        );
                        reject(error);
                      },
                    }
                  );
                });
              }
            );

            // Wait for all removals to complete (or fail)
            await Promise.allSettled(removalPromises);
          };

          try {
            // Execute the removal chain
            await removeAllPrimaryInvestigators();
            console.log(
              "All existing primary investigators removed, adding new one"
            );

            // Step 2: Add the new primary investigator
            associateAssetToCase(
              {
                caseId: objectId,
                association: {
                  assetId: selectedAsset.id,
                  associationType:
                    CaseAssetAssociationType.ASSET_ASSOCIATION_TYPE_PRIMARY_INVESTIGATOR,
                  notes: isForcedAssignment
                    ? "Primary investigator assigned during case reopen"
                    : "Primary investigator reassigned via Manage Modal",
                  assignerAssetId: "", // This would be the current user's asset ID
                },
              } as any,
              {
                onSuccess: (response) => {
                  console.log(
                    "Successfully assigned new primary investigator:",
                    response
                  );
                  setCurrentAssignee(newAssignee);
                  setNewAssignee(null);
                  // Refresh the asset associations data
                  queryClient.invalidateQueries({
                    queryKey: ["case", objectId, "assetAssociations"],
                  });

                  // Always call the completion callback if provided
                  if (onAssignmentComplete) {
                    onAssignmentComplete();
                  } else {
                    // For normal assignment, close the modal
                    onClose();
                  }
                },
                onError: (error) => {
                  console.error(
                    "Failed to assign new primary investigator:",
                    error
                  );
                },
              }
            );
          } catch (error) {
            console.error(
              "Failed to remove existing primary investigators:",
              error
            );
            // Don't proceed with adding new investigator if removal failed
          }
        } else {
          // No existing primary investigators, just add the new one
          console.log(
            "No existing primary investigators found, adding new one"
          );

          associateAssetToCase(
            {
              caseId: objectId,
              association: {
                assetId: selectedAsset.id,
                associationType:
                  CaseAssetAssociationType.ASSET_ASSOCIATION_TYPE_PRIMARY_INVESTIGATOR,
                notes: isForcedAssignment
                  ? "Primary investigator assigned during case reopen"
                  : "Primary investigator assigned via Manage Modal",
                assignerAssetId: "", // This would be the current user's asset ID
              },
            } as any,
            {
              onSuccess: (response) => {
                console.log(
                  "Successfully assigned primary investigator:",
                  response
                );
                setCurrentAssignee(newAssignee);
                setNewAssignee(null);
                // Refresh the asset associations data
                queryClient.invalidateQueries({
                  queryKey: ["case", objectId, "assetAssociations"],
                });

                // Always call the completion callback if provided
                if (onAssignmentComplete) {
                  onAssignmentComplete();
                } else {
                  // For normal assignment, close the modal
                  onClose();
                }
              },
              onError: (error) => {
                console.error("Failed to assign primary investigator:", error);
              },
            }
          );
        }
      }
    }
  };

  return (
    <div
      className="modal-overlay"
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: "rgba(0,0,0,0.5)",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        zIndex: 1000,
      }}
      onClick={isForcedAssignment ? undefined : onClose}
    >
      <div
        className="modal-content"
        style={{
          backgroundColor: "white",
          padding: "24px",
          borderRadius: "8px",
          width: "600px",
          maxWidth: "95vw",
          maxHeight: "95vh",
          boxShadow: "0 4px 8px rgba(0,0,0,0.2)",
          display: "flex",
          flexDirection: "column",
        }}
        onClick={(e) => e.stopPropagation()}
      >
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: "16px",
            flexShrink: 0,
          }}
        >
          <TypographyDS style="h3" color={colors.grey[900]}>
            Manage {objectName}
          </TypographyDS>
          <button
            onClick={isForcedAssignment ? undefined : onClose}
            style={{
              backgroundColor: "transparent",
              border: "none",
              cursor: isForcedAssignment ? "not-allowed" : "pointer",
              display: "flex",
              alignItems: "center",
              color: isForcedAssignment ? colors.grey[400] : colors.grey[600],
            }}
            disabled={isForcedAssignment}
          >
            <CloseIcon />
          </button>
        </div>

        <Box
          sx={{ borderBottom: 1, borderColor: "divider", mb: 3, flexShrink: 0 }}
        >
          <Tabs
            value={Math.min(activeTab, maxTabIndex)}
            onChange={handleTabChange}
            sx={{
              "& .MuiTab-root": {
                textTransform: "none",
                fontWeight: 500,
                fontSize: "14px",
                minWidth: 100,
              },
              "& .Mui-selected": {
                color: colors.blue[600],
              },
              "& .MuiTabs-indicator": {
                backgroundColor: colors.blue[600],
              },
            }}
          >
            {showAssignTab && <Tab label="Assign" />}
            <Tab label="Permissions" disabled={isForcedAssignment} />
            {showDetailsTab && (
              <Tab label="Details & Tags" disabled={isForcedAssignment} />
            )}
          </Tabs>
        </Box>

        <div
          style={{
            marginBottom: "16px",
            minHeight: "200px",
            overflowY: "auto",
            flexGrow: 1,
            paddingRight: "8px",
          }}
        >
          {showAssignTab && activeTab === assignTabIndex && (
            <Box sx={{ width: "100%" }}>
              <Box sx={{ display: "flex", flexDirection: "column", gap: 3 }}>
                {/* CASE OWNER Section */}
                <Box sx={{ mb: 3 }}>
                  <TypographyDS style="h4" color={colors.grey[900]}>
                    {isForcedAssignment
                      ? "REOPEN CASE - ASSIGNMENT REQUIRED"
                      : "CASE OWNER"}
                  </TypographyDS>

                  {/* Show forced assignment message */}
                  {isForcedAssignment && (
                    <Box
                      sx={{
                        mb: 3,
                        mt: 2,
                        p: 2,
                        backgroundColor: colors.blue[50],
                        borderRadius: 1,
                        border: `1px solid ${colors.blue[200]}`,
                      }}
                    >
                      <TypographyDS style="body2" color={colors.blue[800]}>
                        This case is currently closed. To reopen it, you must
                        assign a primary investigator first.
                      </TypographyDS>
                    </Box>
                  )}

                  {/* Large spacing */}
                  <Box sx={{ mb: 4 }} />

                  <Box sx={{ display: "flex", gap: 2, alignItems: "center" }}>
                    <Dropdown
                      placeholder="Select Asset"
                      options={assets.map((asset) => ({
                        value: asset.id,
                        label: asset.name,
                      }))}
                      value={
                        newAssignee !== null ? newAssignee : currentAssignee
                      }
                      onChange={(value) => setNewAssignee(value)}
                      enableSearch
                    />
                    <Button
                      label={
                        isForcedAssignment
                          ? "Reopen"
                          : assignedUser
                            ? "Reassign"
                            : "Assign"
                      }
                      color="blue"
                      prominence={true}
                      disabled={!newAssignee}
                      onClick={handleReassignClick}
                    />
                  </Box>

                  {/* Assigned Asset Table - Only show if not in forced assignment mode */}
                  {assignedUser && !isForcedAssignment && (
                    <Box sx={{ mt: 3 }}>
                      <TableContainer
                        component={Paper}
                        sx={{
                          boxShadow: "none",
                          border: `1px solid ${colors.grey[200]}`,
                          backgroundColor: "white",
                        }}
                      >
                        <Table>
                          <TableHead>
                            <TableRow>
                              <TableCell
                                sx={{
                                  backgroundColor: "white",
                                  fontWeight: 500,
                                  display: "flex",
                                  alignItems: "center",
                                  gap: 1,
                                }}
                              >
                                <ImportExportIcon
                                  sx={{
                                    fontSize: "18px",
                                    color: colors.grey[600],
                                  }}
                                />
                                USER
                              </TableCell>
                              <TableCell
                                sx={{
                                  backgroundColor: "white",
                                  fontWeight: 500,
                                }}
                              >
                                ROLE
                              </TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            <TableRow>
                              <TableCell>
                                <Box
                                  sx={{
                                    display: "flex",
                                    alignItems: "center",
                                    gap: 2,
                                  }}
                                >
                                  <Box
                                    sx={{
                                      width: "40px",
                                      height: "40px",
                                      borderRadius: "50%",
                                      backgroundColor: colors.grey[600],
                                      color: "white",
                                      display: "flex",
                                      alignItems: "center",
                                      justifyContent: "center",
                                      fontSize: "14px",
                                      fontWeight: 500,
                                      textTransform: "uppercase",
                                    }}
                                  >
                                    {assignedUser.name
                                      .split(" ")
                                      .map((word) => word.charAt(0))
                                      .join("")
                                      .slice(0, 2)}
                                  </Box>
                                  <TypographyDS
                                    style="body1"
                                    color={colors.grey[900]}
                                  >
                                    {assignedUser.name}
                                  </TypographyDS>
                                </Box>
                              </TableCell>
                              <TableCell>
                                <TypographyDS
                                  style="body2"
                                  color={colors.grey[600]}
                                >
                                  {assignedUser.permission}
                                </TypographyDS>
                              </TableCell>
                            </TableRow>
                          </TableBody>
                        </Table>
                      </TableContainer>
                    </Box>
                  )}
                </Box>
              </Box>
            </Box>
          )}
          {activeTab === permissionTabIndex && (
            <Box sx={{ width: "100%" }}>
              {/* Asset Assignment Section - Only show if assignment is not disabled */}
              {!disableAssignment && (
                <Box
                  sx={{ display: "flex", gap: 2, alignItems: "center", mb: 3 }}
                >
                  <Autocomplete
                    value={selectedUser}
                    onChange={(event: any, newValue: string | null) => {
                      setSelectedUser(newValue);
                    }}
                    inputValue={inputValue}
                    onInputChange={(event, newInputValue) => {
                      setInputValue(newInputValue);
                    }}
                    options={assets.map((asset) => asset.name)}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        placeholder="Enter Asset"
                        sx={{
                          "& .MuiOutlinedInput-root": {
                            "& fieldset": {
                              borderColor: colors.grey[300],
                            },
                            "&:hover fieldset": {
                              borderColor: colors.grey[400],
                            },
                            "&.Mui-focused fieldset": {
                              borderColor: colors.blue[600],
                            },
                          },
                        }}
                      />
                    )}
                    sx={{
                      flex: 1,
                      "& .MuiAutocomplete-input": {
                        fontSize: "14px",
                      },
                    }}
                  />
                  <Autocomplete
                    value={selectedPermission}
                    onChange={(event: any, newValue: string | null) => {
                      setSelectedPermission(newValue);
                    }}
                    options={["Can View", "Can Edit"]}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        placeholder="Select Permission"
                        sx={{
                          "& .MuiOutlinedInput-root": {
                            "& fieldset": {
                              borderColor: colors.grey[300],
                            },
                            "&:hover fieldset": {
                              borderColor: colors.grey[400],
                            },
                            "&.Mui-focused fieldset": {
                              borderColor: colors.blue[600],
                            },
                          },
                        }}
                      />
                    )}
                    sx={{
                      width: "200px",
                      "& .MuiAutocomplete-input": {
                        fontSize: "14px",
                      },
                    }}
                  />
                  <Button
                    label=""
                    leftIcon={<AddIcon sx={{ fontSize: "20px" }} />}
                    color="blue"
                    prominence={true}
                    onClick={() => {
                      if (selectedUser && selectedPermission) {
                        const newAsset = assets.find(
                          (a) => a.name === selectedUser
                        );
                        if (newAsset) {
                          console.log(
                            "Add new asset permission:",
                            newAsset.name,
                            selectedPermission
                          );
                          setSelectedUser(null);
                          setSelectedPermission(null);
                          setInputValue("");
                        }
                      }
                    }}
                  />
                </Box>
              )}

              <Box sx={{ mt: 4, mb: 3 }}>
                <Box sx={{ mb: 2 }}>
                  <TypographyDS style="h4" color={colors.grey[900]}>
                    Role Permissions
                  </TypographyDS>
                </Box>

                <TableContainer
                  component={Paper}
                  sx={{
                    boxShadow: "none",
                    border: `1px solid ${colors.grey[200]}`,
                  }}
                >
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell
                          sx={{
                            backgroundColor: colors.grey[50],
                            fontWeight: 500,
                          }}
                        >
                          Role
                        </TableCell>
                        <TableCell
                          sx={{
                            backgroundColor: colors.grey[50],
                            fontWeight: 500,
                          }}
                        >
                          Permission
                        </TableCell>
                        <TableCell
                          sx={{ backgroundColor: "transparent", width: "50px" }}
                        ></TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {assignedRolePermissions.map((role) => (
                        <TableRow key={role.id}>
                          <TableCell>{role.name}</TableCell>
                          <TableCell>
                            <Autocomplete
                              value={toDisplay(
                                editingRoleId === role.id && editingPermission
                                  ? editingPermission
                                  : role.permission
                              )}
                              onChange={(
                                event: any,
                                newValue: string | null
                              ) => {
                                if (newValue) {
                                  handleRolePermissionChange(role.id, newValue);
                                }
                              }}
                              options={PERMISSION_OPTIONS}
                              disableClearable
                              renderInput={(params) => (
                                <TextField
                                  {...params}
                                  size="small"
                                  sx={{
                                    "& .MuiOutlinedInput-root": {
                                      "& fieldset": {
                                        borderColor: colors.grey[300],
                                      },
                                      "&:hover fieldset": {
                                        borderColor: colors.grey[400],
                                      },
                                      "&.Mui-focused fieldset": {
                                        borderColor: colors.blue[600],
                                      },
                                    },
                                  }}
                                />
                              )}
                              sx={{
                                width: "150px",
                                "& .MuiAutocomplete-input": {
                                  fontSize: "14px",
                                },
                              }}
                            />
                          </TableCell>
                          <TableCell>
                            {editingRoleId === role.id ? (
                              <Button
                                label=""
                                leftIcon={
                                  <CheckIcon
                                    sx={{
                                      fontSize: "18px",
                                      color: colors.blue[600],
                                    }}
                                  />
                                }
                                color="blue"
                                prominence={false}
                                onClick={() =>
                                  handleSaveRolePermission(role.id)
                                }
                              />
                            ) : (
                              <Button
                                label=""
                                leftIcon={
                                  <DeleteIcon
                                    sx={{
                                      fontSize: "18px",
                                      color: colors.grey[600],
                                    }}
                                  />
                                }
                                color="grey"
                                prominence={false}
                                onClick={() =>
                                  handleRemoveRolePermission(role.id)
                                }
                              />
                            )}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Box>

              {/* Asset Permissions Section - Only show if assignment is not disabled */}
              {!disableAssignment && (
                <>
                  <Box sx={{ mb: 2 }}>
                    <TypographyDS style="h4" color={colors.grey[900]}>
                      Asset Permissions
                    </TypographyDS>
                  </Box>

                  <TableContainer
                    component={Paper}
                    sx={{
                      boxShadow: "none",
                      border: `1px solid ${colors.grey[200]}`,
                    }}
                  >
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell
                            sx={{
                              backgroundColor: colors.grey[50],
                              fontWeight: 500,
                            }}
                          >
                            Asset
                          </TableCell>
                          <TableCell
                            sx={{
                              backgroundColor: colors.grey[50],
                              fontWeight: 500,
                            }}
                          >
                            Permission
                          </TableCell>
                          <TableCell
                            sx={{
                              backgroundColor: "transparent",
                              width: "50px",
                            }}
                          ></TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {/* Asset permissions would be handled here */}
                        <TableRow>
                          <TableCell
                            colSpan={3}
                            sx={{
                              textAlign: "center",
                              color: colors.grey[500],
                            }}
                          >
                            No asset permissions configured
                          </TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                  </TableContainer>
                </>
              )}
            </Box>
          )}
          {showDetailsTab && activeTab === detailsTabIndex && (
            <Box sx={{ width: "100%" }}>
              <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
                <Box sx={{ display: "flex", gap: 2, alignItems: "flex-start" }}>
                  <TextInput
                    title="Case #"
                    placeholder="Enter case number"
                    type={InputType.Text}
                  />
                  <TextInput
                    title="Case Status"
                    placeholder="Select status"
                    type={InputType.Dropdown}
                    options={[
                      { value: "open", label: "Open" },
                      { value: "in_progress", label: "In Progress" },
                      { value: "closed", label: "Closed" },
                      { value: "on_hold", label: "On Hold" },
                    ]}
                  />
                </Box>
                <Box sx={{ display: "flex", gap: 2, alignItems: "flex-start" }}>
                  <TextInput
                    title="Case Priority"
                    placeholder="Select priority"
                    type={InputType.Dropdown}
                    options={[
                      { value: "critical", label: "Critical" },
                      { value: "high", label: "High" },
                      { value: "medium", label: "Medium" },
                      { value: "low", label: "Low" },
                    ]}
                  />
                  <TextInput
                    title="Case Type"
                    placeholder="Select type"
                    type={InputType.Dropdown}
                    options={[
                      { value: "investigation", label: "Investigation" },
                      { value: "incident", label: "Incident" },
                      { value: "complaint", label: "Complaint" },
                      { value: "inquiry", label: "Inquiry" },
                    ]}
                  />
                </Box>
                <Box sx={{ display: "flex", gap: 2, alignItems: "flex-start" }}>
                  <TextInput
                    title="Date Opened"
                    placeholder="MM/DD/YYYY"
                    type={InputType.Text}
                    mask={InputMask.Date}
                  />
                  <TextInput
                    title="Date Closed"
                    placeholder="MM/DD/YYYY"
                    type={InputType.Text}
                    mask={InputMask.Date}
                  />
                </Box>
                <Box
                  sx={{
                    display: "flex",
                    gap: 2,
                    alignItems: "flex-start",
                    width: "100%",
                  }}
                >
                  <TextInput
                    title="User Tags"
                    placeholder="Enter tags (comma separated)"
                    type={InputType.Text}
                  />
                </Box>
                <Box
                  sx={{
                    display: "flex",
                    gap: 2,
                    alignItems: "flex-start",
                    width: "100%",
                  }}
                >
                  <TextInput
                    title="Release Status"
                    placeholder="Enter tags (comma separated)"
                    type={InputType.Text}
                  />
                </Box>
              </Box>
            </Box>
          )}
        </div>

        <div
          style={{
            display: "flex",
            justifyContent: "flex-end",
            gap: "12px",
            flexShrink: 0,
            marginTop: "16px",
          }}
        >
          <Button
            label="Close"
            color="grey"
            style="outline"
            onClick={onClose}
          />
        </div>
      </div>
    </div>
  );
};

export default ManageModal;
