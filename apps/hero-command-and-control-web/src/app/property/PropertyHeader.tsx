"use client";

import { useBreadcrumbs } from "@/app/contexts/Breadcrumb/BreadcrumbContext";
import { useRecentlyViewedTracker } from "@/app/hooks/useRecentlyViewedTracker";
import { Header } from "@/design-system/components/Header";
import { <PERSON><PERSON>, Snackbar } from "@mui/material";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { hookPropertyTypeToString } from "../apis/services/workflow/property/enumConverters";
import { useAddCustodyEvent, useUpdateProperty } from "../apis/services/workflow/property/hooks";
import { CustodyActionType, CustodyEvent, Property } from "../apis/services/workflow/property/types";
import EntityActionsMenu from "../entity/entityComponents/components/EntityActionsMenu";
import CustodyUpdateModal from "./components/CustodyUpdateModal";
import UpdateButton from "./components/UpdateButton";

interface PropertyHeaderProps {
    propertyData: Property | undefined;
    propertyId: string;
    onClose: () => void;
}

export default function PropertyHeader({ propertyData, propertyId, onClose }: PropertyHeaderProps) {
    const router = useRouter();

    // Get property name from workflow property data
    const getPropertyName = () => {
        if (!propertyData) return "Unknown Property";

        // Try to get from property schema first - prioritize description or make/model
        if (propertyData.propertySchema?.description) {
            return propertyData.propertySchema.description;
        }

        if (propertyData.propertySchema?.identifiers) {
            return propertyData.propertySchema.identifiers;
        }

        // Fallback to property number if available
        if (propertyData.propertyNumber) {
            return `Property ${propertyData.propertyNumber}`;
        }

        // Use property type as a last resort, but make it more readable
        if (propertyData.propertySchema?.propertyType) {
            const propertyTypeString = hookPropertyTypeToString(propertyData.propertySchema.propertyType);
            // Convert enum string to readable format
            switch (propertyTypeString) {
                case "PROPERTY_TYPE_FOUND":
                    return "Found Property";
                case "PROPERTY_TYPE_SEIZED":
                    return "Seized Property";
                case "PROPERTY_TYPE_STOLEN":
                    return "Stolen Property";
                case "PROPERTY_TYPE_SAFEKEEPING":
                    return "Safekeeping Property";
                case "PROPERTY_TYPE_MISSING":
                    return "Missing Property";
                case "PROPERTY_TYPE_RECOVERED":
                    return "Recovered Property";
                default:
                    return "Property";
            }
        }

        return "Property";
    };

    const propertyName = getPropertyName();

    // Get property ID in display format
    const displayPropertyId = propertyId?.slice(0, 7) || "Unknown ID";

    const { breadcrumbs, addBreadcrumb, clearBreadcrumbs } = useBreadcrumbs();

    // State for custody update modal
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [selectedActionType, setSelectedActionType] = useState<CustodyActionType | null>(null);
    const [showSuccessMessage, setShowSuccessMessage] = useState(false);
    const [showErrorMessage, setShowErrorMessage] = useState(false);
    const [errorMessage, setErrorMessage] = useState("");

    // Hooks for custody events and property updates
    const addCustodyEventMutation = useAddCustodyEvent();
    const updatePropertyMutation = useUpdateProperty();

    // Set custom breadcrumbs for property page
    useEffect(() => {
        clearBreadcrumbs();
        addBreadcrumb({
            id: "search",
            label: "Back to Search",
            path: "/search"
        });
        addBreadcrumb({
            id: `property-${propertyId}`,
            label: propertyName,
            path: `/property?propertyId=${propertyId}`
        });
    }, [propertyId, propertyName, addBreadcrumb, clearBreadcrumbs]);

    useRecentlyViewedTracker({
        id: `property-${propertyId}`,
        title: propertyName,
        subtitle: "Property Record",
        path: `/property?propertyId=${propertyId}`,
    });

    // Format date from timestamp or ISO string
    const formatDate = (timestamp: string): string => {
        if (!timestamp) return "N/A";
        try {
            const date = new Date(timestamp);

            return date.toLocaleDateString("en-US", {
                year: "numeric",
                month: "short",
                day: "2-digit",
            });
        } catch (e) {
            return "N/A";
        }
    };

    const handleManage = () => {
        console.log("Manage property:", propertyId);
        // TODO: Implement manage functionality
    };

    const handleDelete = () => {
        console.log("Delete property:", propertyId);
        // TODO: Implement delete functionality
    };

    const handleUpdateCustody = (actionType: CustodyActionType, _propertyId: string) => {
        setSelectedActionType(actionType);
        setIsModalOpen(true);
    };

    const handleCustodyEventSubmit = async (custodyEvent: CustodyEvent, disposalType?: string) => {
        try {
            // Add the custody event first
            await addCustodyEventMutation.mutateAsync({
                propertyId,
                custodyEvent,
            });
            
            // If this is a disposal action, also update the property status
            if (custodyEvent.actionType === CustodyActionType.CUSTODY_ACTION_TYPE_DISPOSED && propertyData) {
                console.log("🔧 DISPOSAL EVENT - Updating property status to disposed");
                console.log("🔧 Disposal type received:", disposalType);
                
                // Update property with disposal information
                const updatedProperty = {
                    ...propertyData,
                    propertyStatus: 'PROPERTY_STATUS_DISPOSED' as any, // Use string representation for hooks
                    ...(disposalType && { disposalType: disposalType as any }) // Add disposal type if provided
                };
                
                console.log("🔧 Updating property with disposal info:", updatedProperty);
                await updatePropertyMutation.mutateAsync(updatedProperty);
            }
            
            setShowSuccessMessage(true);
        } catch (error) {
            console.error("Failed to add custody event:", error);
            setShowErrorMessage(true);
            setErrorMessage("Failed to update custody. Please try again.");
        }
    };

    // Convert breadcrumbs to header format
    const headerBreadcrumbs = breadcrumbs.map((breadcrumb, index, array) => ({
        label: breadcrumb.label,
        path: breadcrumb.path,
        active: index === array.length - 1,
        onClick: index === array.length - 1 ? undefined : () => {
            // Navigate to the breadcrumb path
            router.push(breadcrumb.path);
        }
    }));

    return (
        <>
            <Header
                breadcrumbs={headerBreadcrumbs}
                title={propertyName}
                metadata={[
                    {
                        label: "Date Created",
                        value: formatDate(propertyData?.createTime || "")
                    },
                    {
                        label: "Last Updated",
                        value: formatDate(propertyData?.updateTime || "")
                    },
                    {
                        label: "ID",
                        value: displayPropertyId
                    }
                ]}
                actions={[]}
                statusIndicator={
                    <div style={{ display: "flex", alignItems: "center", gap: 2 }}>
                        <UpdateButton propertyId={propertyId} onUpdateCustody={handleUpdateCustody} />
                        <EntityActionsMenu
                            onManage={handleManage}
                            onDelete={handleDelete}
                            entityId={propertyId}
                            entityName={propertyName}
                            entityType="property"
                            entity={undefined}
                        />
                    </div>
                }
            />

            {/* Custody Update Modal */}
            {selectedActionType && (
                <CustodyUpdateModal
                    open={isModalOpen}
                    onClose={() => {
                        setIsModalOpen(false);
                        setSelectedActionType(null);
                    }}
                    onSubmit={handleCustodyEventSubmit}
                    actionType={selectedActionType}
                    propertyId={propertyId}
                />
            )}

            {/* Success Message */}
            <Snackbar
                open={showSuccessMessage}
                autoHideDuration={6000}
                onClose={() => setShowSuccessMessage(false)}
            >
                <Alert onClose={() => setShowSuccessMessage(false)} severity="success">
                    Custody event added successfully!
                </Alert>
            </Snackbar>

            {/* Error Message */}
            <Snackbar
                open={showErrorMessage}
                autoHideDuration={6000}
                onClose={() => setShowErrorMessage(false)}
            >
                <Alert onClose={() => setShowErrorMessage(false)} severity="error">
                    {errorMessage}
                </Alert>
            </Snackbar>
        </>
    );
} 