import { Label } from "@/design-system/components/Label";
import { Typography } from "@/design-system/components/Typography";
import { colors } from "@/design-system/tokens";
import { ColorToken } from "@/design-system/tokens/colors";
import AttachMoneyIcon from "@mui/icons-material/AttachMoney";
import BadgeIcon from "@mui/icons-material/Badge";
import CategoryIcon from "@mui/icons-material/Category";
import DescriptionIcon from "@mui/icons-material/Description";
import HandymanIcon from "@mui/icons-material/Handyman";
import InventoryIcon from "@mui/icons-material/Inventory";
import NoPhotographyOutlinedIcon from "@mui/icons-material/NoPhotographyOutlined";
import PersonIcon from "@mui/icons-material/Person";
import {
    Box,
    Divider,
    Typography as MuiTypography,
    Paper,
} from "@mui/material";
import React from "react";
import { hookPropertyTypeToString, stringToPropertyType } from "../../apis/services/workflow/property/enumConverters";
import { Property, PropertyType } from "../../apis/services/workflow/property/types";

interface PropertyDetailCardProps {
    propertyData: Property;
    isLoading?: boolean;
    isError?: boolean;
}

interface DisplayTag {
    label: string;
    color: ColorToken;
    prominence: boolean;
}

interface PropertyInfoItem {
    icon: React.ReactNode;
    text: string;
    wide?: boolean;
}

export default function PropertyDetailCard({
    propertyData,
    isLoading = false,
    isError = false
}: PropertyDetailCardProps) {
    // Helper function to convert property type to readable format
    const getPropertyTypeDisplay = (type: PropertyType | string): string => {
        // Handle both string and numeric enum values
        let propertyType: PropertyType;

        if (typeof type === 'string') {
            propertyType = stringToPropertyType(type);
        } else {
            propertyType = type;
        }

        switch (propertyType) {
            case PropertyType.PROPERTY_TYPE_FOUND:
                return "Found";
            case PropertyType.PROPERTY_TYPE_SEIZED:
                return "Seized";
            case PropertyType.PROPERTY_TYPE_STOLEN:
                return "Stolen";
            case PropertyType.PROPERTY_TYPE_SAFEKEEPING:
                return "Safekeeping";
            case PropertyType.PROPERTY_TYPE_MISSING:
                return "Missing";
            case PropertyType.PROPERTY_TYPE_RECOVERED:
                return "Recovered";
            default:
                return "Unknown";
        }
    };

    // Helper function to convert property type from hook data to readable format
    const getPropertyTypeDisplayFromHook = (type: PropertyType): string => {
        const typeString = hookPropertyTypeToString(type);
        return getPropertyTypeDisplay(typeString);
    };

    // Process internal tags for display (if available)
    const getTagsForDisplay = (): DisplayTag[] => {
        // For now, show property type as a tag (now in schema)
        if (propertyData.propertySchema?.propertyType) {
            return [{
                label: getPropertyTypeDisplayFromHook(propertyData.propertySchema.propertyType),
                color: 'blue',
                prominence: false
            }];
        }
        return [{ label: "No tags", color: "grey", prominence: false }];
    };

    if (isLoading) {
        return (
            <Paper
                elevation={0}
                sx={{
                    borderRadius: 2,
                    border: `1px solid ${colors.grey[200]}`,
                    mb: 3,
                    overflow: "hidden",
                    maxWidth: "100%",
                }}
            >
                <Box sx={{ p: 3, bgcolor: "white" }}>
                    <Typography style="body2" color={colors.grey[500]}>
                        Loading property information...
                    </Typography>
                </Box>
            </Paper>
        );
    }

    if (isError || !propertyData) {
        return (
            <Paper
                elevation={0}
                sx={{
                    borderRadius: 2,
                    border: `1px solid ${colors.grey[200]}`,
                    mb: 3,
                    overflow: "hidden",
                    maxWidth: "100%",
                }}
            >
                <Box sx={{ p: 3, bgcolor: "white" }}>
                    <Typography style="body2" color={colors.grey[500]}>
                        No property data available
                    </Typography>
                </Box>
            </Paper>
        );
    }

    // Get property schema from the property data
    const propertySchema = propertyData.propertySchema;

    // Create an array of property info items from PropertySchema only
    const propertyInfoItems: PropertyInfoItem[] = [
        propertySchema?.category && {
            icon: <CategoryIcon sx={{ color: colors.grey[500], flexShrink: 0 }} />,
            text: propertySchema.category,
        },
        propertySchema?.serialNumber && {
            icon: <BadgeIcon sx={{ color: colors.grey[500], flexShrink: 0 }} />,
            text: `S/N: ${propertySchema.serialNumber}`,
        },
        propertySchema?.identifiers && {
            icon: <InventoryIcon sx={{ color: colors.grey[500], flexShrink: 0 }} />,
            text: propertySchema.identifiers,
        },
        propertySchema?.owner && {
            icon: <PersonIcon sx={{ color: colors.grey[500], flexShrink: 0 }} />,
            text: propertySchema.owner,
        },
        propertySchema?.condition && {
            icon: <HandymanIcon sx={{ color: colors.grey[500], flexShrink: 0 }} />,
            text: propertySchema.condition,
        },
        propertySchema?.value && {
            icon: <AttachMoneyIcon sx={{ color: colors.grey[500], flexShrink: 0 }} />,
            text: propertySchema.value,
        },
        propertySchema?.description && {
            icon: <DescriptionIcon sx={{ color: colors.grey[500], flexShrink: 0 }} />,
            text: propertySchema.description,
            wide: true,
        },
    ].filter(Boolean) as PropertyInfoItem[];

    return (
        <Paper
            elevation={0}
            sx={{
                borderRadius: 2,
                border: `1px solid ${colors.grey[200]}`,
                mb: 3,
                overflow: "hidden",
                maxWidth: "100%",
            }}
        >
            <Box sx={{ p: 3, bgcolor: "white" }}>
                {/* wrapper flex row */}
                <Box
                    sx={{
                        display: "flex",
                        flexWrap: "wrap",
                        gap: 3,
                        width: "100%",
                        alignItems: "stretch",
                    }}
                >
                    {/* Photo / placeholder */}
                    <Box
                        sx={{
                            flexBasis: { xs: "100%", sm: "25%", md: "20%" },
                            maxWidth: { xs: "100%", sm: "25%", md: "20%" },
                            minWidth: { xs: "100%", sm: "200px", md: "200px" },
                        }}
                    >
                        <Box
                            sx={{
                                width: "100%",
                                height: "100%",
                                minHeight: 120,
                                backgroundColor: colors.grey[100],
                                border: `1px solid ${colors.grey[200]}`,
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "center",
                                borderRadius: 1,
                            }}
                        >
                            <NoPhotographyOutlinedIcon
                                sx={{ color: colors.grey[300], fontSize: 40 }}
                            />
                        </Box>
                    </Box>

                    {/* Right‑hand info */}
                    <Box
                        sx={{
                            flex: 1,
                            minWidth: 0, // Allow flex item to shrink below content size
                            display: "flex",
                            flexDirection: "column",
                            gap: 3,
                        }}
                    >
                        {/* Tags section */}
                        <Box sx={{ display: "flex", gap: 1, flexWrap: "wrap" }}>
                            {getTagsForDisplay().map((tag: DisplayTag, i: number) => (
                                <Label
                                    key={i}
                                    label={tag.label}
                                    color={tag.color}
                                    prominence={tag.prominence}
                                    size="small"
                                />
                            ))}
                        </Box>

                        <Divider />

                        <Box
                            sx={{
                                display: "grid",
                                gap: 2,
                                gridTemplateColumns: {
                                    xs: "1fr",
                                    sm: "repeat(auto-fit, minmax(280px, 1fr))",
                                    md: "repeat(auto-fit, minmax(320px, 1fr))",
                                },
                                alignItems: "start",
                            }}
                        >
                            {propertyInfoItems.map(({ icon, text, wide }, i) => (
                                <Box
                                    key={i}
                                    sx={{
                                        display: "flex",
                                        alignItems: "flex-start",
                                        gap: 1.5,
                                        gridColumn: wide ? { sm: "span 2" } : undefined,
                                        minWidth: 0, // Allow grid item to shrink
                                    }}
                                >
                                    {icon}
                                    <MuiTypography
                                        variant="body2"
                                        sx={{
                                            minWidth: 0,
                                            flex: 1,
                                            overflow: "hidden",
                                            textOverflow: "ellipsis",
                                            whiteSpace: "nowrap",
                                            lineHeight: 1.4,
                                        }}
                                        title={text} // Show full text on hover
                                    >
                                        {text}
                                    </MuiTypography>
                                </Box>
                            ))}
                        </Box>
                    </Box>
                </Box>
            </Box>
        </Paper>
    );
}