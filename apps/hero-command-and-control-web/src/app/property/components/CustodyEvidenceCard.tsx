import { colors } from "@/design-system/tokens";
import BadgeIcon from "@mui/icons-material/Badge";
import GavelIcon from "@mui/icons-material/Gavel";
import PersonIcon from "@mui/icons-material/Person";
import PlaceIcon from "@mui/icons-material/Place";
import PolicyIcon from "@mui/icons-material/Policy";
import TimelapseIcon from "@mui/icons-material/Timelapse";
import { Box, Typography as MuiTypography, Paper } from "@mui/material";
import React from "react";
import { useAsset } from "../../apis/services/workflow/assets/hooks";
import {
    hookPropertyDisposalTypeToString,
    hookPropertyStatusToString,
} from "../../apis/services/workflow/property/enumConverters";
import {
    Property,
    PropertyDisposalType,
    PropertyStatus,
} from "../../apis/services/workflow/property/types";

interface CustodyEvidenceCardProps {
    propertyData: Property;
}

function humanizeStatus(status?: PropertyStatus): string {
    if (!status && status !== 0) return "—";
    const s = hookPropertyStatusToString(status);
    switch (s) {
        case "PROPERTY_STATUS_COLLECTED":
            return "Collected";
        case "PROPERTY_STATUS_IN_CUSTODY":
            return "In Custody";
        case "PROPERTY_STATUS_CHECKED_OUT":
            return "Checked Out";
        case "PROPERTY_STATUS_DISPOSED":
            return "Disposed";
        case "PROPERTY_STATUS_MISSING":
            return "Missing";
        case "PROPERTY_STATUS_CLAIMED":
            return "Claimed";
        default:
            return "Unspecified";
    }
}

function humanizeDisposalType(disposal?: PropertyDisposalType): string {
    if (!disposal && disposal !== 0) return "—";
    const d = hookPropertyDisposalTypeToString(disposal);
    switch (d) {
        case "PROPERTY_DISPOSAL_TYPE_RELEASED":
            return "Released";
        case "PROPERTY_DISPOSAL_TYPE_DESTROYED":
            return "Destroyed";
        case "PROPERTY_DISPOSAL_TYPE_AUCTIONED":
            return "Auctioned";
        case "PROPERTY_DISPOSAL_TYPE_AGENCY_RETAIN":
            return "Agency Retain";
        case "PROPERTY_DISPOSAL_TYPE_TRANSFERRED":
            return "Transferred";
        default:
            return "Unspecified";
    }
}

export default function CustodyEvidenceCard({ propertyData }: CustodyEvidenceCardProps) {
    // Look up asset for current custodian to display name instead of ID
    const currentCustodianId = propertyData.currentCustodian || "";
    const { data: custodianAsset } = useAsset(currentCustodianId);
    const currentCustodianDisplay = custodianAsset?.asset?.name || propertyData.currentCustodian;
    const rows: Array<{ icon: React.ReactNode; label: string; value?: string } | null> = [
        propertyData.propertyNumber
            ? {
                icon: <BadgeIcon sx={{ color: colors.grey[500] }} />,
                label: "Property/Evidence #",
                value: propertyData.propertyNumber,
            }
            : null,
        {
            icon: <PolicyIcon sx={{ color: colors.grey[500] }} />,
            label: "Status",
            value: humanizeStatus(propertyData.propertyStatus),
        },
        {
            icon: <TimelapseIcon sx={{ color: colors.grey[500] }} />,
            label: "Retention Period",
            value: propertyData.retentionPeriod || "—",
        },
        {
            icon: <GavelIcon sx={{ color: colors.grey[500] }} />,
            label: "Disposal Type",
            value: humanizeDisposalType(propertyData.disposalType),
        },
        currentCustodianDisplay
            ? {
                icon: <PersonIcon sx={{ color: colors.grey[500] }} />,
                label: "Current Custodian",
                value: currentCustodianDisplay,
            }
            : null,
        propertyData.currentLocation
            ? {
                icon: <PlaceIcon sx={{ color: colors.grey[500] }} />,
                label: "Current Location",
                value: propertyData.currentLocation,
            }
            : null,
    ];

    return (
        <Paper
            elevation={0}
            sx={{
                borderRadius: 2,
                border: `1px solid ${colors.grey[200]}`,
                mb: 3,
                overflow: "hidden",
                maxWidth: "100%",
                bgcolor: "white",
            }}
        >
            <Box sx={{ p: 3 }}>
                <MuiTypography variant="subtitle2" sx={{ color: colors.grey[700], mb: 2 }}>
                    Custody & Evidence
                </MuiTypography>
                <Box sx={{ display: "grid", gridTemplateColumns: { xs: "1fr", sm: "repeat(auto-fit, minmax(280px, 1fr))" }, gap: 2 }}>
                    {rows.filter(Boolean).map((row, idx) => (
                        <Box key={idx} sx={{ display: "flex", gap: 1.5, alignItems: "flex-start" }}>
                            {row!.icon}
                            <Box sx={{ minWidth: 0 }}>
                                <MuiTypography variant="caption" sx={{ color: colors.grey[600] }}>
                                    {row!.label}
                                </MuiTypography>
                                <MuiTypography variant="body2" noWrap title={row!.value}>
                                    {row!.value || "—"}
                                </MuiTypography>
                            </Box>
                        </Box>
                    ))}
                </Box>
            </Box>
        </Paper>
    );
}

