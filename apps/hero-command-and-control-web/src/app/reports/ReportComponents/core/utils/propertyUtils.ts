import { stringToPropertyStatus, stringToPropertyType } from '@/app/apis/services/workflow/property/enumConverters';
import { Property, PropertyDisposalType, PropertyStatus, PropertyType } from '@/app/apis/services/workflow/property/types';

/**
 * Converts form values to property backend format
 * Maps form data to property backend structure
 * @param values - Form values from property form
 * @param dispatcherAsset - Dispatcher asset containing orgId and user ID
 */
export const createPropertyFromValues = (values: any, dispatcherAsset?: { id: string; orgId: number } | null): Omit<Property, 'id' | 'createTime' | 'updateTime' | 'version' | 'status' | 'resourceType'> => {

    // Map status from form to PropertyStatus enum
    const mapStatus = (status: string): PropertyStatus => {
        if (!status) return PropertyStatus.PROPERTY_STATUS_COLLECTED;

        const statusMap: Record<string, string> = {
            'collected': 'PROPERTY_STATUS_COLLECTED',
            'in_custody': 'PROPERTY_STATUS_IN_CUSTODY',
            'checked_out': 'PROPERTY_STATUS_CHECKED_OUT',
            'disposed': 'PROPERTY_STATUS_DISPOSED',
            'missing': 'PROPERTY_STATUS_MISSING',
            'claimed': 'PROPERTY_STATUS_CLAIMED',
        };

        const enumString = statusMap[status] || 'PROPERTY_STATUS_COLLECTED';
        return stringToPropertyStatus(enumString);
    };

    // Map property type from form to PropertyType enum
    const mapPropertyType = (propertyType: string): PropertyType => {
        if (!propertyType) return PropertyType.PROPERTY_TYPE_UNSPECIFIED;
        const map: Record<string, string> = {
            'found': 'PROPERTY_TYPE_FOUND',
            'seized': 'PROPERTY_TYPE_SEIZED',
            'stolen': 'PROPERTY_TYPE_STOLEN',
            'safekeeping': 'PROPERTY_TYPE_SAFEKEEPING',
            'missing': 'PROPERTY_TYPE_MISSING',
            'recovered': 'PROPERTY_TYPE_RECOVERED',
        };
        const enumString = map[propertyType] || 'PROPERTY_TYPE_UNSPECIFIED';
        return stringToPropertyType(enumString);
    };

    return {
        orgId: dispatcherAsset?.orgId || 0,
        propertyNumber: values.propertyNumber || '',
        isEvidence: Boolean(values.isEvidence),
        retentionPeriod: values.retentionPeriod || '',
        propertyStatus: mapStatus(values.status),
        disposalType: values.disposalType || 0,
        notes: values.notes || '',
        caseNumber: values.caseNumber || '',
        currentCustodian: values.currentCustodian || '',
        currentLocation: values.currentLocation || '',
        custodyChain: [],
        propertySchema: {
            description: values.description || '',
            quantity: values.quantity || '',
            category: values.category || '',
            identifiers: values.identifiers || '',
            owner: values.owner || '',
            condition: values.condition || '',
            serialNumber: values.serialNumber || '',
            value: values.value || '',
            propertyType: mapPropertyType(values.propertyType),
        },
        createdBy: dispatcherAsset?.id || '',
        updatedBy: dispatcherAsset?.id || '',
    };
};

/**
 * Maps entity property types to property backend types
 */
const mapEntityPropertyType = (type: string): PropertyType => {
    if (!type) return PropertyType.PROPERTY_TYPE_UNSPECIFIED;

    const typeMap: Record<string, string> = {
        'seized': 'PROPERTY_TYPE_SEIZED',
        'found': 'PROPERTY_TYPE_FOUND',
        'stolen': 'PROPERTY_TYPE_STOLEN',
        'safekeeping': 'PROPERTY_TYPE_SAFEKEEPING',
        'missing': 'PROPERTY_TYPE_MISSING',
        'recovered': 'PROPERTY_TYPE_RECOVERED',
        'evidence': 'PROPERTY_TYPE_SEIZED', // Map evidence to seized
    };

    const enumString = typeMap[type?.toLowerCase()] || 'PROPERTY_TYPE_UNSPECIFIED';
    return stringToPropertyType(enumString);
};

/**
 * Maps disposal types from entity format to property backend format
 */
const mapDisposalType = (disposalType?: string): PropertyDisposalType => {
    if (!disposalType) return PropertyDisposalType.PROPERTY_DISPOSAL_TYPE_UNSPECIFIED;
    switch (disposalType?.toLowerCase()) {
        case 'released':
            return PropertyDisposalType.PROPERTY_DISPOSAL_TYPE_RELEASED;
        case 'destroyed':
            return PropertyDisposalType.PROPERTY_DISPOSAL_TYPE_DESTROYED;
        case 'auctioned':
            return PropertyDisposalType.PROPERTY_DISPOSAL_TYPE_AUCTIONED;
        case 'agency_retain':
            return PropertyDisposalType.PROPERTY_DISPOSAL_TYPE_AGENCY_RETAIN;
        case 'transferred':
            return PropertyDisposalType.PROPERTY_DISPOSAL_TYPE_TRANSFERRED;
        default:
            return PropertyDisposalType.PROPERTY_DISPOSAL_TYPE_UNSPECIFIED;
    }
};

/**
 * Converts property backend format to form format for UI compatibility
 * This maintains backward compatibility with existing UI components
 */
export const propertyToEntityFormat = (property: Property): any => {
    return {
        id: property.id,
        entityType: "ENTITY_TYPE_PROPERTY",
        // Include propertyStatus at top-level for consumers expecting it
        propertyStatus: property.propertyStatus,
        data: {
            propertyInformationSection: {
                propertyType: property.propertySchema?.propertyType, // Now in schema
                serialNumber: property.propertySchema?.serialNumber,
                category: property.propertySchema?.category,
                value: property.propertySchema?.value,
                identifiers: property.propertySchema?.identifiers,
                description: property.propertySchema?.description,
                quantity: property.propertySchema?.quantity,
                owner: property.propertySchema?.owner,
                condition: property.propertySchema?.condition,
                // Add status inside section for older consumers
                status: property.propertyStatus,
            },
            // Keep the flat structure for backward compatibility
            propertyType: property.propertySchema?.propertyType, // Now in schema
            serialNumber: property.propertySchema?.serialNumber,
            category: property.propertySchema?.category,
            value: property.propertySchema?.value,
            identifiers: property.propertySchema?.identifiers,
            description: property.propertySchema?.description,
            quantity: property.propertySchema?.quantity,
            owner: property.propertySchema?.owner,
            condition: property.propertySchema?.condition,
            propertyNumber: property.propertyNumber, // Add property number
            isEvidence: property.isEvidence,
            retentionPeriod: property.retentionPeriod,
            disposalType: property.disposalType,
            notes: property.notes,
            caseNumber: property.caseNumber,
            currentCustodian: property.currentCustodian,
            currentLocation: property.currentLocation,
            // Handle uploaded images from metadata
            uploadImage: property.metadata?.fileAttachments ? property.metadata.fileAttachments.map((file: any) => ({
                id: file.fileId,
                name: file.fileName,
                size: file.fileSize,
                type: file.fileType,
                uploadedAt: new Date(file.uploadedAt),
            })) : [],
        },
        createTime: property.createTime,
        updateTime: property.updateTime,
        status: property.status,
        version: property.version,
        createdBy: property.createdBy,
        updatedBy: property.updatedBy,
    };
}; 