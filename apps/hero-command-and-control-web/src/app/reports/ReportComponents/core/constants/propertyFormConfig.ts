import { InputType } from '@/design-system/components/TextInput';

export const PROPERTY_FORM_CONFIG = {
    id: "property_form",
    title: "Property Information",
    sections: [
        {
            id: "basicInfo",
            title: "Basic Property Information",
            fields: [
                {
                    id: "description",
                    type: "text",
                    title: "Description",
                    placeholder: "Detailed description of the property",
                    required: true,
                    row: 1
                },
                {
                    id: "propertyType",
                    type: InputType.Dropdown,
                    title: "Property Type",
                    placeholder: "Select property type",
                    required: true,
                    options: [
                        { value: "found", label: "Found" },
                        { value: "seized", label: "Seized" },
                        { value: "stolen", label: "Stolen" },
                        { value: "safekeeping", label: "Safekeeping" },
                        { value: "missing", label: "Missing" },
                        { value: "recovered", label: "Recovered" }
                    ],
                    row: 2,
                    width: "50%"
                },
                {
                    id: "category",
                    type: InputType.Dropdown,
                    title: "Category",
                    placeholder: "Select category",
                    options: [
                        { value: "currency", label: "Currency" },
                        { value: "jewelry", label: "Jewelry" },
                        { value: "clothing", label: "Clothing" },
                        { value: "tools", label: "Tools" },
                        { value: "weapons", label: "Weapons" },
                        { value: "vehicles", label: "Vehicles" },
                        { value: "documents", label: "Documents" },
                        { value: "drugs", label: "Drugs" },
                        { value: "electronics", label: "Electronics" },
                        { value: "other", label: "Other" }
                    ],
                    row: 2,
                    width: "50%"
                },
                {
                    id: "quantity",
                    type: "text",
                    title: "Quantity",
                    placeholder: "Number of items",
                    row: 3,
                    width: "50%"
                },
                {
                    id: "identifiers",
                    type: "text",
                    title: "Make/Model/Brand",
                    placeholder: "Make, model, or brand information",
                    row: 3,
                    width: "50%"
                },
                {
                    id: "owner",
                    type: "text",
                    title: "Owner",
                    placeholder: "Owner information if applicable",
                    row: 4,
                    width: "50%"
                },
                {
                    id: "condition",
                    type: InputType.Dropdown,
                    title: "Condition",
                    options: [
                        { value: "excellent", label: "Excellent" },
                        { value: "good", label: "Good" },
                        { value: "fair", label: "Fair" },
                        { value: "poor", label: "Poor" },
                        { value: "damaged", label: "Damaged" }
                    ],
                    row: 4,
                    width: "50%"
                },
                {
                    id: "serialNumber",
                    type: "text",
                    title: "Serial Number",
                    placeholder: "Serial number or unique identifier",
                    row: 5,
                    width: "50%"
                },
                {
                    id: "value",
                    type: "currency",
                    title: "Property Value",
                    placeholder: "$0.00",
                    row: 5,
                    width: "50%"
                }
            ]
        },
        {
            id: "adminInfo",
            title: "Administrative Information",
            fields: [
                {
                    id: "status",
                    type: InputType.Dropdown,
                    title: "Status",
                    required: true,
                    options: [
                        { value: "collected", label: "Collected" },
                        { value: "in_custody", label: "In Custody" },
                        { value: "checked_out", label: "Checked Out" },
                        { value: "disposed", label: "Disposed" },
                        { value: "missing", label: "Missing" },
                        { value: "claimed", label: "Claimed" }
                    ],
                    row: 1,
                    width: "50%"
                },
                {
                    id: "propertyNumber",
                    type: "text",
                    title: "Property Number",
                    placeholder: "Enter property or evidence number",
                    row: 1,
                    width: "50%"
                },
                {
                    id: "retentionPeriod",
                    type: "text",
                    title: "Retention Period",
                    placeholder: "Enter retention period",
                    row: 2,
                    width: "50%"
                },
                {
                    id: "currentCustodian",
                    type: "text",
                    title: "Current Custodian",
                    placeholder: "Name or ID of current custodian",
                    row: 2,
                    width: "50%"
                },
                {
                    id: "currentLocation",
                    type: "text",
                    title: "Current Location",
                    placeholder: "Current storage location",
                    row: 3
                }
            ]
        }
    ]
};