import { hookPropertyStatusToString, hookPropertyTypeToString, stringToPropertyStatus, stringToPropertyType } from '@/app/apis/services/workflow/property/enumConverters';
import { Button } from '@/design-system/components/Button';
import { InputType } from '@/design-system/components/TextInput';
import { Typography } from '@/design-system/components/Typography';
import { FormRenderer, FormRendererRef } from '@/design-system/form/FormRenderer';
import { colors } from '@/design-system/tokens';
import { create } from "@bufbuild/protobuf";
import { Box } from '@mui/material';
import { AssetStatus, AssetType, ListAssetsRequest } from "proto/hero/assets/v2/assets_pb";
import { FileReferenceSchema } from "proto/hero/reports/v2/reports_pb";
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useFileUpload } from '../../../apis/services/filerepository/hooks';
import { useListAssets } from '../../../apis/services/workflow/assets/hooks';
import { useAddPropertyFileAttachment, useProperty, useUpdateProperty } from '../../../apis/services/workflow/property/hooks';
import { PROPERTY_FORM_CONFIG } from '../core/constants/propertyFormConfig';
import {
    FileUploadDropZone,
    StagedFilesList,
    UploadProgressDashboard,
    type StagedFileReference
} from './uiComponents/MediaPanel';

interface UploadResult {
    fileId: string;
    fileName: string;
    fileType: string;
    displayName?: string;
    caption?: string;
    fileCategory?: string;
}

interface PropertyFormProps {
    onSubmit: (values: any) => void;
    onCancel: () => void;
    onSaveAndAddAnother?: (values: any) => void;
    onPropertyUpdated?: (updatedProperty: any) => void; // Callback for when property is updated successfully
    initialValues?: any;
    readOnly?: boolean;
    customTitle?: string;
    propertyId?: string; // For existing properties when editing
    reportId?: string; // For draft persistence
}

export const PropertyForm: React.FC<PropertyFormProps> = ({
    onSubmit,
    onCancel,
    onSaveAndAddAnother,
    onPropertyUpdated,
    initialValues = {},
    readOnly = false,
    customTitle,
    propertyId,
    reportId,
}) => {
    // Remove formValues state to prevent circular dependencies - FormRenderer manages its own state
    const [validationError, setValidationError] = useState<string | null>(null);
    const [uploadedFiles, setUploadedFiles] = useState<Array<{ fileId: string; fileName: string; fileType: string }>>([]);
    // Remove formKey that was causing form resets
    const formRef = useRef<FormRendererRef>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);

    // Fetch assets for Current Custodian dropdown
    const { data: assetsResponse } = useListAssets({
        pageSize: 100,
        pageToken: "",
        type: AssetType.UNSPECIFIED,
        status: AssetStatus.UNSPECIFIED,
        orderBy: "name",
    } as ListAssetsRequest);

    // New state hooks for file upload UI
    const [isDragging, setIsDragging] = useState(false);
    const [stagedFiles, setStagedFiles] = useState<StagedFileReference[]>([]);
    const [overallProgress, setOverallProgress] = useState(0);
    const [uploadingSummary, setUploadingSummary] = useState({
        totalFiles: 0,
        completedFiles: 0,
        failedFiles: 0,
        totalBytes: 0,
        uploadedBytes: 0,
        averageSpeed: 0,
        estimatedTimeRemaining: 0,
        isComplete: false
    });

    // Draft persistence functions
    const getDraftKey = useCallback(() => {
        return `property_draft_${reportId || 'new'}`;
    }, [reportId]);

    // Use ref to access current stagedFiles without causing re-renders
    const stagedFilesRef = useRef<StagedFileReference[]>([]);
    stagedFilesRef.current = stagedFiles;

    const saveDraftToStorage = useCallback((data: any) => {
        if (!reportId) return; // Only save for existing reports
        try {
            // Clean the data before saving - only save flat values
            const cleanData: any = {};

            Object.keys(data).forEach(key => {
                const value = data[key];
                // Only save flat field values
                if (key !== 'propertyInformationSection' &&
                    (typeof value !== 'object' || Array.isArray(value))) {
                    cleanData[key] = value;
                }
            });

            // Save staged files using ref to avoid dependency issues
            cleanData.stagedFiles = stagedFilesRef.current.map(file => ({
                ...file,
                // Don't save File objects
                file: undefined,
                // Keep preview URLs for images
                preview: file.preview,
                // Reset upload status
                isUploading: false,
                uploadProgress: file.fileId ? 100 : 0,
                uploadError: undefined,
                uploadSpeed: undefined,
                timeRemaining: undefined,
                bytesUploaded: undefined
            }));

            localStorage.setItem(getDraftKey(), JSON.stringify(cleanData));
            console.log('PropertyForm - Draft saved (cleaned):', cleanData);
        } catch (error) {
            console.warn('PropertyForm - Failed to save draft:', error);
        }
    }, [getDraftKey, reportId]);

    const loadDraftFromStorage = useCallback(() => {
        if (!reportId) return null; // Only load for existing reports
        try {
            const stored = localStorage.getItem(getDraftKey());
            const draft = stored ? JSON.parse(stored) : null;
            console.log('PropertyForm - Draft loaded:', draft);

            // Restore staged files if present
            if (draft?.stagedFiles) {
                setStagedFiles(draft.stagedFiles.map((file: StagedFileReference) => ({
                    ...file,
                    // Reset upload status
                    isUploading: false,
                    uploadProgress: file.fileId ? 100 : 0
                })));
                delete draft.stagedFiles;
            }

            return draft;
        } catch (error) {
            console.warn('PropertyForm - Failed to load draft:', error);
            return null;
        }
    }, [getDraftKey, reportId]);

    const clearDraftFromStorage = useCallback(() => {
        if (!reportId) return;
        try {
            localStorage.removeItem(getDraftKey());
            console.log('PropertyForm - Draft cleared');
        } catch (error) {
            console.warn('PropertyForm - Failed to clear draft:', error);
        }
    }, [getDraftKey, reportId]);

    // Hooks for file operations
    const { uploadFile, isLoading: isUploadingFile } = useFileUpload();
    const addPropertyFileAttachmentMutation = useAddPropertyFileAttachment();

    // Fetch existing property data if propertyId is provided (for editing)
    const { data: existingProperty, isLoading: isLoadingProperty } = useProperty(propertyId || '');

    // Property update hook
    const updatePropertyMutation = useUpdateProperty();

    // Remove draft loading since we're not using formValues state anymore
    // Draft functionality can be re-implemented later if needed without causing circular dependencies
    useEffect(() => {
        // Draft loading removed to prevent circular dependencies
        return;
    }, [loadDraftFromStorage]);

    // Remove all formValues-dependent effects to prevent circular dependencies
    useEffect(() => {
        // FormRenderer handles its own state - no need for external state management
        return;
    }, []);

    // Remove auto-save entirely to prevent circular dependencies
    useEffect(() => {
        // Auto-save removed to prevent text input issues and circular dependencies
        return;
    }, []);

    // Create dynamic form config with asset options for Current Custodian
    const dynamicFormConfig = useMemo(() => {
        const assetOptions = assetsResponse?.assets?.map(asset => ({
            label: asset.name,
            value: asset.id
        })) || [];

        // Deep clone the config to avoid mutating the original
        const config = JSON.parse(JSON.stringify(PROPERTY_FORM_CONFIG));

        // Find and update the currentCustodian field
        const adminInfoSection = config.sections.find((section: any) => section.id === 'adminInfo');
        if (adminInfoSection) {
            const currentCustodianField = adminInfoSection.fields.find((field: any) => field.id === 'currentCustodian');
            if (currentCustodianField) {
                currentCustodianField.type = InputType.Dropdown;
                currentCustodianField.placeholder = "Select current custodian";
                currentCustodianField.options = assetOptions;
                currentCustodianField.enableSearch = true;
            }
        }

        return {
            ...config,
            title: customTitle || PROPERTY_FORM_CONFIG.title,
        };
    }, [assetsResponse, customTitle]);

    // Split the form config into two pages
    // Single page config (basic + admin info)
    const singlePageConfig = dynamicFormConfig;

    // Memoize initial values to prevent infinite re-renders
    // FormRenderer expects structured format: { sectionId: { fieldId: value } }
    const memoizedInitialValues = useMemo(() => {
        // Extract only the flat field values, excluding nested structures and meta fields
        const cleanValues: any = {};

        // Start with initialValues prop
        if (initialValues && typeof initialValues === 'object') {
            Object.keys(initialValues).forEach(key => {
                const value = initialValues[key];
                if (typeof value !== 'object' || Array.isArray(value)) {
                    cleanValues[key] = value;
                }
            });
        }

        // Convert enum formats to dropdown formats if needed
        if (cleanValues.propertyType && typeof cleanValues.propertyType === 'string' && cleanValues.propertyType.startsWith('PROPERTY_TYPE_')) {
            const map: Record<string, string> = {
                'PROPERTY_TYPE_FOUND': 'found',
                'PROPERTY_TYPE_SEIZED': 'seized',
                'PROPERTY_TYPE_STOLEN': 'stolen',
                'PROPERTY_TYPE_SAFEKEEPING': 'safekeeping',
                'PROPERTY_TYPE_MISSING': 'missing',
                'PROPERTY_TYPE_RECOVERED': 'recovered',
            };
            cleanValues.propertyType = map[cleanValues.propertyType] || cleanValues.propertyType.toLowerCase();
            console.log('PropertyForm - Converting propertyType from enum:', initialValues.propertyType, '->', cleanValues.propertyType);
        }

        // Convert category to lowercase if it's in title case
        if (cleanValues.category && typeof cleanValues.category === 'string') {
            cleanValues.category = cleanValues.category.toLowerCase();
        }

        // If we have existing property data (for editing), populate the form fields
        if (existingProperty?.propertySchema) {
            const propSchema = existingProperty.propertySchema;

            // Map property data to form fields
            if (propSchema.description) cleanValues.description = propSchema.description;
            if (propSchema.category) cleanValues.category = propSchema.category;
            if (propSchema.quantity) cleanValues.quantity = propSchema.quantity.toString();
            if (propSchema.identifiers) cleanValues.identifiers = propSchema.identifiers;
            if (propSchema.owner) cleanValues.owner = propSchema.owner;
            if (propSchema.condition) cleanValues.condition = propSchema.condition;
            if (propSchema.serialNumber) cleanValues.serialNumber = propSchema.serialNumber;
            if (propSchema.value) cleanValues.value = propSchema.value.toString();

            // Map propertyType enum to dropdown value
            if (propSchema.propertyType) {
                const enumString = typeof propSchema.propertyType === 'string' ? propSchema.propertyType : hookPropertyTypeToString(propSchema.propertyType);
                const map: Record<string, string> = {
                    'PROPERTY_TYPE_FOUND': 'found',
                    'PROPERTY_TYPE_SEIZED': 'seized',
                    'PROPERTY_TYPE_STOLEN': 'stolen',
                    'PROPERTY_TYPE_SAFEKEEPING': 'safekeeping',
                    'PROPERTY_TYPE_MISSING': 'missing',
                    'PROPERTY_TYPE_RECOVERED': 'recovered',
                };
                cleanValues.propertyType = map[enumString] || enumString.toLowerCase();
                console.log('PropertyForm - propertyType mapping:', enumString, '->', cleanValues.propertyType);
            }
        }

        // If we have existing property admin fields (for editing), populate them  
        if (existingProperty?.propertyStatus) {
            // Convert PropertyStatus enum to form dropdown value
            const statusString = typeof existingProperty.propertyStatus === 'string'
                ? existingProperty.propertyStatus
                : hookPropertyStatusToString(existingProperty.propertyStatus);
            const statusMap: Record<string, string> = {
                'PROPERTY_STATUS_COLLECTED': 'collected',
                'PROPERTY_STATUS_IN_CUSTODY': 'in_custody',
                'PROPERTY_STATUS_CHECKED_OUT': 'checked_out',
                'PROPERTY_STATUS_DISPOSED': 'disposed',
                'PROPERTY_STATUS_MISSING': 'missing',
                'PROPERTY_STATUS_CLAIMED': 'claimed'
            };
            cleanValues.status = statusMap[statusString] || 'collected';
            console.log('PropertyForm - status mapping:', statusString, '->', cleanValues.status);
        }
        if (existingProperty?.propertyNumber) cleanValues.propertyNumber = existingProperty.propertyNumber;
        if (existingProperty?.currentCustodian) cleanValues.currentCustodian = existingProperty.currentCustodian;
        if (existingProperty?.currentLocation) cleanValues.currentLocation = existingProperty.currentLocation;

        console.log('PropertyForm - flat cleanValues:', cleanValues);

        // Convert flat values to structured format expected by FormRenderer
        // FormRenderer expects: { sectionId: { fieldId: value } }
        const structuredValues: any = {
            basicInfo: {},
            adminInfo: {}
        };

        // Map fields to their appropriate sections based on PROPERTY_FORM_CONFIG
        const basicInfoFields = ['description', 'propertyType', 'category', 'quantity', 'identifiers', 'owner', 'condition', 'serialNumber', 'value'];
        const adminInfoFields = ['status', 'propertyNumber', 'retentionPeriod', 'currentCustodian', 'currentLocation'];

        // Populate basicInfo section
        basicInfoFields.forEach(fieldId => {
            if (cleanValues[fieldId] !== undefined) {
                structuredValues.basicInfo[fieldId] = cleanValues[fieldId];
            }
        });

        // Populate adminInfo section
        adminInfoFields.forEach(fieldId => {
            if (cleanValues[fieldId] !== undefined) {
                structuredValues.adminInfo[fieldId] = cleanValues[fieldId];
            }
        });

        // Handle any other fields that don't fit in the above categories
        Object.keys(cleanValues).forEach(fieldId => {
            if (!basicInfoFields.includes(fieldId) && !adminInfoFields.includes(fieldId)) {
                // Put miscellaneous fields in basicInfo by default
                structuredValues.basicInfo[fieldId] = cleanValues[fieldId];
            }
        });

        console.log('PropertyForm - structured memoizedInitialValues:', structuredValues);
        return structuredValues;
    }, [initialValues, existingProperty]); // Depend on both initialValues prop and existingProperty
    const validateRequiredFields = (allValues: any): boolean => {
        // Required: description, propertyType, status
        const requiredFields = ['description', 'propertyType', 'status'];
        const missingFields = requiredFields.filter(field => {
            const flatValue = allValues[field];
            return !flatValue;
        });

        if (missingFields.length > 0) {
            setValidationError(
                `Please fill in the following required fields: ${missingFields.join(', ')}`
            );
            return false;
        }

        // Validate property value is a number
        const propertyValue = allValues.value;
        if (propertyValue) {
            // More robust currency parsing that handles multiple formats
            const cleanValue = String(propertyValue).replace(/[^0-9.-]/g, '');
            if (cleanValue && isNaN(Number(cleanValue))) {
                setValidationError('Property value must be a valid number');
                return false;
            }
        }

        setValidationError(null);
        return true;
    };

    // Handle file uploads from the form
    const handleFileUpload = async (files: File[]): Promise<Array<{ fileId: string; fileName: string; fileType: string }>> => {
        console.log('🔴 DEBUG: handleFileUpload called with files:', files);
        console.log('🔴 DEBUG: handleFileUpload files length:', files.length);
        console.log('🔴 DEBUG: File details:', files.map(f => ({ name: f.name, size: f.size, type: f.type })));

        const uploadPromises = files.map(async (file, index) => {
            console.log(`🔴 DEBUG: uploading file ${index + 1}:`, file.name, file.size, file.type);

            // Retry configuration
            const maxRetries = 3;
            let lastError: Error;

            for (let attempt = 1; attempt <= maxRetries; attempt++) {
                try {
                    console.log(`🔴 DEBUG: upload attempt ${attempt}/${maxRetries} for ${file.name}`);

                    const result = await uploadFile(file, undefined, {
                        propertyId: propertyId || 'pending', // Will be updated after property creation
                        fileCategory: getFileCategory(file),
                        uploadContext: 'property_form'
                    });

                    console.log(`🔴 DEBUG: upload result for ${file.name} (attempt ${attempt}):`, result);

                    if (result.success && result.fileId) {
                        const fileData = {
                            fileId: result.fileId!,
                            fileName: file.name,
                            fileType: file.type
                        };

                        console.log(`🔴 DEBUG: file uploaded successfully on attempt ${attempt}:`, fileData);
                        return fileData;
                    } else {
                        const error = new Error(result.error || 'File upload failed');
                        console.warn(`🔴 WARNING: upload failed for ${file.name} on attempt ${attempt}:`, result.error);
                        lastError = error;

                        // Don't retry on the last attempt
                        if (attempt === maxRetries) {
                            throw error;
                        }
                    }
                } catch (error) {
                    console.warn(`🔴 WARNING: upload error for ${file.name} on attempt ${attempt}:`, error);
                    lastError = error instanceof Error ? error : new Error('Unknown upload error');

                    // Don't retry on the last attempt
                    if (attempt === maxRetries) {
                        throw lastError;
                    }
                }

                // Exponential backoff: wait 2^attempt seconds (2s, 4s, 8s, etc.)
                if (attempt < maxRetries) {
                    const delayMs = Math.pow(2, attempt) * 1000;
                    console.log(`🔴 DEBUG: waiting ${delayMs}ms before retry for ${file.name}`);
                    await new Promise(resolve => setTimeout(resolve, delayMs));
                }
            }

            // This should never be reached due to the throw in the loop, but TypeScript requires it
            throw lastError!;
        });

        const uploadedFileData = await Promise.all(uploadPromises);
        console.log('🔴 DEBUG: all uploads completed, uploaded file data:', uploadedFileData);

        // Update state for UI purposes
        setUploadedFiles(prev => {
            const updated = [...prev, ...uploadedFileData];
            console.log('🔴 DEBUG: updated uploadedFiles state:', updated);
            return updated;
        });

        return uploadedFileData;
    };

    // Helper functions for file upload UI
    const formatBytes = (bytes: number): string => {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
    };

    const formatDuration = (seconds: number): string => {
        if (seconds < 60) return `${Math.round(seconds)}s`;
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = Math.round(seconds % 60);
        return `${minutes}m ${remainingSeconds}s`;
    };

    const createPreview = (file: File): string => {
        // Create preview for supported file types (same as MediaPanel)
        if (file.type.startsWith('image/') || file.type.startsWith('video/') || file.type === 'application/pdf') {
            return URL.createObjectURL(file);
        }
        return '';
    };

    const getFileCategory = (file: File): string => {
        if (file.type.startsWith('image/')) return 'property_image';
        if (file.type.startsWith('video/')) return 'property_video';
        if (file.type === 'application/pdf') return 'property_document';
        if (file.type.startsWith('audio/')) return 'property_audio';
        return 'property_media';
    };

    const renderFilePreview = (file: StagedFileReference) => {
        if (file.preview) {
            return (
                <img
                    src={file.preview}
                    alt={file.displayName || 'Preview'}
                    style={{
                        width: '100%',
                        height: '100%',
                        objectFit: 'cover',
                    }}
                />
            );
        }
        return null;
    };

    const updateFileProgress = (fileId: string, progress: number, bytesUploaded: number) => {
        setStagedFiles(prev =>
            prev.map(file => {
                if (file.id === fileId) {
                    const now = Date.now();
                    const timeElapsed = (now - (file.uploadStartTime || now)) / 1000;
                    const uploadSpeed = bytesUploaded / timeElapsed;
                    const remainingBytes = file.file ? file.file.size - bytesUploaded : 0;
                    const timeRemaining = uploadSpeed > 0 ? remainingBytes / uploadSpeed : 0;

                    return {
                        ...file,
                        uploadProgress: progress,
                        bytesUploaded,
                        uploadSpeed,
                        timeRemaining
                    };
                }
                return file;
            })
        );

        // Update overall progress
        updateOverallProgress();
    };

    const updateOverallProgress = () => {
        const summary = stagedFiles.reduce(
            (acc, file) => {
                acc.totalFiles++;
                if (file.uploadProgress === 100) acc.completedFiles++;
                if (file.uploadError) acc.failedFiles++;
                if (file.file) {
                    acc.totalBytes += file.file.size;
                    acc.uploadedBytes += file.bytesUploaded || 0;
                }
                if (file.uploadSpeed) {
                    acc.totalSpeed += file.uploadSpeed;
                    acc.speedCount++;
                }
                return acc;
            },
            {
                totalFiles: 0,
                completedFiles: 0,
                failedFiles: 0,
                totalBytes: 0,
                uploadedBytes: 0,
                totalSpeed: 0,
                speedCount: 0
            }
        );

        const averageSpeed = summary.speedCount > 0 ? summary.totalSpeed / summary.speedCount : 0;
        const remainingBytes = summary.totalBytes - summary.uploadedBytes;
        const estimatedTimeRemaining = averageSpeed > 0 ? remainingBytes / averageSpeed : 0;
        const progress = summary.totalBytes > 0 ? (summary.uploadedBytes / summary.totalBytes) * 100 : 0;

        setOverallProgress(progress);
        setUploadingSummary({
            ...summary,
            averageSpeed,
            estimatedTimeRemaining,
            isComplete: progress === 100
        });
    };

    const uploadStagedFiles = async (): Promise<UploadResult[]> => {
        const results: UploadResult[] = [];
        const filesToUpload = stagedFiles.filter(file =>
            file.file && !file.fileId && !file.isUploading && !file.uploadError
        );

        if (filesToUpload.length === 0) {
            // Return already uploaded files
            return stagedFiles
                .filter(file => file.fileId)
                .map(file => ({
                    fileId: file.fileId!,
                    fileName: String(file.displayName || file.metadata?.originalFilename || ''),
                    fileType: String(file.metadata?.fileType || ''),
                    displayName: file.displayName,
                    caption: file.caption,
                    fileCategory: file.fileCategory
                }));
        }

        for (const stagedFile of filesToUpload) {
            if (!stagedFile.file) continue;

            try {
                // Mark file as uploading
                setStagedFiles(prev =>
                    prev.map(f =>
                        f.id === stagedFile.id
                            ? { ...f, isUploading: true, uploadStartTime: Date.now() }
                            : f
                    )
                );

                // Upload the file with progress tracking
                const result = await uploadFile(
                    stagedFile.file,
                    undefined, // Progress callback not supported in current uploadFile signature
                    {
                        propertyId: propertyId || 'pending',
                        fileCategory: stagedFile.fileCategory || (stagedFile.file ? getFileCategory(stagedFile.file) : 'property_media'),
                        uploadContext: 'property_form',
                        metadata: {
                            displayName: stagedFile.displayName,
                            caption: stagedFile.caption
                        }
                    }
                );

                if (result.success && result.fileId) {
                    // Update staged file with upload result
                    setStagedFiles(prev =>
                        prev.map(f =>
                            f.id === stagedFile.id
                                ? {
                                    ...f,
                                    fileId: result.fileId || '',
                                    isUploading: false,
                                    uploadProgress: 100
                                }
                                : f
                        )
                    );

                    results.push({
                        fileId: result.fileId,
                        fileName: String(stagedFile.displayName || stagedFile.metadata?.originalFilename || ''),
                        fileType: String(stagedFile.metadata?.fileType || ''),
                        displayName: stagedFile.displayName,
                        caption: stagedFile.caption,
                        fileCategory: stagedFile.fileCategory
                    });
                } else {
                    throw new Error(result.error || 'Upload failed');
                }
            } catch (error) {
                // Mark file as failed
                setStagedFiles(prev =>
                    prev.map(f =>
                        f.id === stagedFile.id
                            ? {
                                ...f,
                                isUploading: false,
                                uploadError: error instanceof Error ? error.message : 'Upload failed'
                            }
                            : f
                    )
                );
                console.error(`Failed to upload file ${stagedFile.displayName}:`, error);
            }
        }

        return results;
    };

    // Create file attachment function that captures current uploaded files
    const createAttachFilesToPropertyFunction = (filesToAttach: Array<{ fileId: string; fileName: string; fileType: string }>) => {
        return async (createdPropertyId: string) => {
            console.log('🟡 DEBUG: attachFilesToProperty called with propertyId:', createdPropertyId);
            console.log('🟡 DEBUG: attachFilesToProperty filesToAttach:', filesToAttach);
            console.log('🟡 DEBUG: filesToAttach length:', filesToAttach.length);

            if (filesToAttach.length === 0) {
                console.log('🟡 DEBUG: attachFilesToProperty - no files to attach');
                return;
            }

            console.log(`🟡 DEBUG: attachFilesToProperty - attaching ${filesToAttach.length} files`);

            const attachmentPromises = filesToAttach.map(async (file, index) => {
                const attachmentData = {
                    propertyId: createdPropertyId,
                    fileAttachment: {
                        propertyId: createdPropertyId,
                        fileId: file.fileId,
                        displayName: file.fileName,
                        fileCategory: file.fileType ? (file.fileType.startsWith('image/') ? 'property_image' :
                            file.fileType.startsWith('video/') ? 'property_video' :
                                file.fileType === 'application/pdf' ? 'property_document' :
                                    file.fileType.startsWith('audio/') ? 'property_audio' : 'property_media') : 'property_media',
                        displayOrder: index,
                        metadata: {
                            uploadContext: 'property_form',
                            fileType: file.fileType
                        }
                    }
                };

                console.log(`🟡 DEBUG: attaching file ${index + 1}:`, attachmentData);
                console.log(`🟡 DEBUG: using addPropertyFileAttachmentMutation:`, addPropertyFileAttachmentMutation);
                console.log(`🟡 DEBUG: calling mutateAsync with:`, attachmentData);

                const result = await addPropertyFileAttachmentMutation.mutateAsync(attachmentData);
                console.log(`🟡 DEBUG: attachment result for file ${index + 1}:`, result);
                return result;
            });

            try {
                const results = await Promise.all(attachmentPromises);
                console.log('🟡 SUCCESS: attachFilesToProperty - all files attached successfully:', results);
                return results;
            } catch (error) {
                console.error('🟡 ERROR: attachFilesToProperty - error attaching files:', error);
                console.error('🟡 ERROR: error details:', error);
                throw error;
            }
        };
    };

    const handleSubmit = async () => {
        if (formRef.current) {
            const currentValues = formRef.current.getFlatValues();
            // Use only current values from the form, no need for formValues state
            const allValues = currentValues;

            // Validate all required fields before submitting
            if (validateRequiredFields(allValues)) {
                try {
                    // Upload any remaining staged files
                    const uploadedFiles = await uploadStagedFiles();

                    // Flatten the values to ensure compatibility with parent components
                    const flattenedValues = flattenValues(allValues);

                    // Remove the old uploadImage field since we're using the new system
                    delete flattenedValues.uploadImage;

                    // If we have a propertyId, this is an UPDATE operation - ONLY UPDATE!
                    if (propertyId) {
                        console.log('PropertyForm - EDIT MODE: Updating existing property:', propertyId, flattenedValues);

                        // Use existing property as base and only update changed fields
                        if (!existingProperty) {
                            throw new Error('Cannot update property: existing property data not loaded');
                        }

                        const updateData = {
                            ...existingProperty, // Preserve all existing fields (orgId, version, etc.)
                            // Update the property schema with form values
                            propertySchema: {
                                description: flattenedValues.description || '',
                                category: flattenedValues.category || '',
                                quantity: flattenedValues.quantity || '',
                                identifiers: flattenedValues.identifiers || '',
                                owner: flattenedValues.owner || '',
                                condition: flattenedValues.condition || '',
                                serialNumber: flattenedValues.serialNumber || '',
                                value: flattenedValues.value || '',
                                // Map dropdown string (e.g., 'found') back to enum (PropertyType)
                                propertyType: flattenedValues.propertyType
                                    ? stringToPropertyType(`PROPERTY_TYPE_${String(flattenedValues.propertyType).toUpperCase()}`)
                                    : stringToPropertyType('PROPERTY_TYPE_FOUND'),
                            },
                            // Update admin fields if provided
                            propertyStatus: flattenedValues.status
                                ? stringToPropertyStatus(`PROPERTY_STATUS_${String(flattenedValues.status).toUpperCase()}`)
                                : existingProperty.propertyStatus,
                            propertyNumber: flattenedValues.propertyNumber || existingProperty.propertyNumber || '',
                            currentCustodian: flattenedValues.currentCustodian || existingProperty.currentCustodian || '',
                            currentLocation: flattenedValues.currentLocation || existingProperty.currentLocation || '',
                        };

                        const updatedProperty = await updatePropertyMutation.mutateAsync(updateData);

                        // Handle file attachments after update
                        if (uploadedFiles.length > 0) {
                            const attachFilesToProperty = createAttachFilesToPropertyFunction(uploadedFiles);
                            await attachFilesToProperty(propertyId);
                        }

                        console.log('PropertyForm - Property UPDATED successfully (no new property created)');

                        // Notify parent component of the updated property
                        if (onPropertyUpdated && updatedProperty) {
                            onPropertyUpdated(updatedProperty);
                        }

                        // Clear draft on successful update
                        clearDraftFromStorage();

                        // Don't call onCancel() here - let onPropertyUpdated handle closing the panel

                    } else {
                        // This is CREATE MODE - create new property via parent handler
                        console.log('PropertyForm - CREATE MODE: Creating new property via parent');
                        flattenedValues._attachFilesToProperty = createAttachFilesToPropertyFunction(uploadedFiles);

                        // Clear draft on successful submission
                        clearDraftFromStorage();

                        onSubmit(flattenedValues);
                    }
                } catch (error) {
                    console.error('PropertyForm handleSubmit - error:', error);
                    setValidationError(error instanceof Error ? error.message : 'Error processing files');
                    // Don't clear form data on error - keep user's input
                }
            } else {
                // On validation failure, preserve form data - don't clear anything
                console.log('PropertyForm - Validation failed, preserving form data');
            }
        }
    };

    const handleSaveAndAddAnother = async () => {
        // Save and Add Another should only work in CREATE mode, not EDIT mode
        if (propertyId) {
            console.log('PropertyForm - Save and Add Another not allowed in edit mode');
            setValidationError('Save and Add Another is only available when creating new properties');
            return;
        }

        if (formRef.current) {
            const currentValues = formRef.current.getFlatValues();
            // Use only current values from the form, no need for formValues state
            const allValues = currentValues;

            // Validate all required fields before submitting
            if (validateRequiredFields(allValues)) {
                try {
                    // Upload any remaining staged files
                    const uploadedFiles = await uploadStagedFiles();

                    // Flatten the values to ensure compatibility with parent components
                    const flattenedValues = flattenValues(allValues);

                    // Remove the old uploadImage field since we're using the new system
                    delete flattenedValues.uploadImage;

                    // Pass the file attachment handler to the parent with uploaded files
                    flattenedValues._attachFilesToProperty = createAttachFilesToPropertyFunction(uploadedFiles);

                    // Clear draft on successful submission
                    clearDraftFromStorage();

                    if (onSaveAndAddAnother) {
                        onSaveAndAddAnother(flattenedValues);
                    }
                } catch (error) {
                    console.error('PropertyForm handleSaveAndAddAnother - error:', error);
                    setValidationError(error instanceof Error ? error.message : 'Error processing files');
                    // Don't clear form data on error - keep user's input
                }
            } else {
                // On validation failure, preserve form data - don't clear anything
                console.log('PropertyForm - Validation failed in saveAndAddAnother, preserving form data');
            }
        }
    };

    // Helper function to flatten sectioned values
    const flattenValues = (values: any): any => {
        const flattened: any = {};

        Object.keys(values).forEach(key => {
            if (typeof values[key] === 'object' && values[key] !== null) {
                // If it's a section (like page1, page2), flatten its contents
                Object.keys(values[key]).forEach(fieldKey => {
                    flattened[fieldKey] = values[key][fieldKey];
                });
            } else {
                // If it's already a flat value, keep it as is
                flattened[key] = values[key];
            }
        });

        return flattened;
    };

    // Helper function to flatten form values from FormRenderer
    const flattenFormValues = (values: any): any => {
        const flattened: any = {};

        Object.keys(values).forEach(key => {
            const value = values[key];
            if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
                // If it's a nested object (like propertyInformationSection), flatten it
                Object.keys(value).forEach(fieldKey => {
                    flattened[fieldKey] = value[fieldKey];
                });
            } else {
                // If it's a flat value, keep it as is
                flattened[key] = value;
            }
        });

        return flattened;
    };

    const handleCancel = () => {
        onCancel();
    };

    // File upload event handlers
    const handleDragEnter = (event: React.DragEvent<HTMLDivElement>) => {
        event.preventDefault();
        event.stopPropagation();
        setIsDragging(true);
    };

    const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
        event.preventDefault();
        event.stopPropagation();
        setIsDragging(true);
    };

    const handleDragLeave = (event: React.DragEvent<HTMLDivElement>) => {
        event.preventDefault();
        event.stopPropagation();
        setIsDragging(false);
    };

    const handleDrop = async (event: React.DragEvent<HTMLDivElement>) => {
        event.preventDefault();
        event.stopPropagation();
        setIsDragging(false);

        if (event.dataTransfer.files) {
            const files = Array.from(event.dataTransfer.files);
            await handleFiles(files);
        }
    };

    const handleClickUpload = () => {
        fileInputRef.current?.click();
    };

    const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
        if (event.target.files) {
            const files = Array.from(event.target.files);
            await handleFiles(files);

            // Clear the input
            if (fileInputRef.current) {
                fileInputRef.current.value = '';
            }
        }
    };

    const handleFiles = async (files: File[]) => {
        for (const file of files) {
            // Accept the same file types as MediaPanel: images, videos, PDFs, and audio files
            const isValidFileType = file.type.startsWith('image/') ||
                file.type.startsWith('video/') ||
                file.type === 'application/pdf' ||
                file.type.startsWith('audio/');

            if (!isValidFileType) {
                console.warn(`Skipping unsupported file type: ${file.type} (${file.name})`);
                continue;
            }

            const tempId = Date.now().toString() + Math.random().toString(36).substring(2, 9);
            const baseFileRef = create(FileReferenceSchema, {
                id: tempId,
                fileId: "", // Empty until uploaded
                caption: "",
                displayName: file.name,
                displayOrder: 0,
                fileCategory: getFileCategory(file),
                metadata: {
                    originalFilename: file.name,
                    fileSize: file.size,
                    fileType: file.type,
                },
            });

            const stagedFile: StagedFileReference = {
                ...baseFileRef,
                file,
                preview: createPreview(file),
                isUploading: false,
            };

            setStagedFiles(prev => [...prev, stagedFile]);
        }
    };

    // File metadata handlers
    const handleDisplayNameChange = (id: string, displayName: string) => {
        setStagedFiles(prev =>
            prev.map(file =>
                file.id === id ? { ...file, displayName } : file
            )
        );
    };

    const handleCaptionChange = (id: string, caption: string) => {
        setStagedFiles(prev =>
            prev.map(file =>
                file.id === id ? { ...file, caption } : file
            )
        );
    };

    const handleCategoryChange = (id: string, fileCategory: string) => {
        setStagedFiles(prev =>
            prev.map(file =>
                file.id === id ? { ...file, fileCategory } : file
            )
        );
    };

    const handleRemoveFile = (id: string) => {
        setStagedFiles(prev => {
            const file = prev.find(f => f.id === id);
            if (file?.preview) {
                URL.revokeObjectURL(file.preview);
            }
            return prev.filter(f => f.id !== id);
        });
    };

    // Use single-page config and remove any legacy uploadImage field if present
    // Memoize to avoid changing object identity on unrelated re-renders (e.g., media staging),
    // which can cause FormRenderer to reset form state.
    const currentConfig = useMemo(() => ({
        ...singlePageConfig,
        sections: singlePageConfig.sections.map((section: any) => ({
            ...section,
            fields: section.fields.filter((field: any) => field.id !== 'uploadImage')
        }))
    }), [singlePageConfig]);

    // Remove onValueChange entirely to prevent circular dependencies
    // FormRenderer will handle its own state, we'll get values only on submit
    const handleValueChange = useCallback((values: any) => {
        // Do nothing - let FormRenderer manage its own state
        // We'll get the current values from formRef.current.getFlatValues() when needed
    }, []);

    // Show loading state while fetching existing property data
    if (propertyId && isLoadingProperty) {
        return (
            <Box sx={{
                height: '100%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                flexDirection: 'column',
                gap: 2
            }}>
                <Typography style="h3">Loading Property Data...</Typography>
                {/* Could add a loading spinner here if available */}
            </Box>
        );
    }

    return (
        <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column', minHeight: 0 }}>
            {/* Single page form - no step indicator */}

            {/* Validation Error Display */}
            {validationError && (
                <Box sx={{
                    p: 2,
                    mb: 2,
                    backgroundColor: colors.rose[50],
                    border: `1px solid ${colors.rose[200]}`,
                    borderRadius: 1
                }}>
                    <Typography style="body2" color={colors.rose[600]}>
                        {validationError}
                    </Typography>
                </Box>
            )}

            {/* Form content */}
            <Box sx={{ flex: 1, overflow: 'auto', pb: '128px' }}>
                <FormRenderer
                    ref={formRef}
                    config={currentConfig}
                    initialValues={memoizedInitialValues}
                    readOnly={readOnly}
                    customTitle={customTitle}
                    showSubmitButton={false}
                    onValueChange={handleValueChange}
                />

                {/* Add the new file upload UI components */}
                <Box sx={{ mt: 4, px: 3 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <Typography style="caps1" color={colors.grey[500]}>
                            Images
                        </Typography>
                    </Box>
                    <Box>
                        <FileUploadDropZone
                            isDragging={isDragging}
                            onDragEnter={handleDragEnter}
                            onDragOver={handleDragOver}
                            onDragLeave={handleDragLeave}
                            onDrop={handleDrop}
                            onClick={handleClickUpload}
                            fileInputRef={fileInputRef}
                            onFileSelect={handleFileSelect}
                        />

                        {stagedFiles.length > 0 && (
                            <Box sx={{ mt: 3 }}>
                                <StagedFilesList
                                    stagedFiles={stagedFiles}
                                    readOnly={readOnly}
                                    onDisplayNameChange={handleDisplayNameChange}
                                    onCaptionChange={handleCaptionChange}
                                    onCategoryChange={handleCategoryChange}
                                    onRemoveFile={handleRemoveFile}
                                    renderFilePreview={renderFilePreview}
                                    formatBytes={formatBytes}
                                    formatDuration={formatDuration}
                                />
                            </Box>
                        )}

                        {(stagedFiles.some(f => f.isUploading) || uploadingSummary.isComplete) && (
                            <Box sx={{ mt: 3 }}>
                                <UploadProgressDashboard
                                    overallProgress={overallProgress}
                                    uploadingSummary={uploadingSummary}
                                    formatBytes={formatBytes}
                                    formatDuration={formatDuration}
                                />
                            </Box>
                        )}
                    </Box>
                </Box>
            </Box>

            {/* Navigation buttons */}
            <Box sx={{
                position: "fixed",
                bottom: 0,
                left: 0,
                right: 0,
                padding: "16px 24px",
                backgroundColor: "white",
                borderTop: "1px solid #E0E0E0",
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                gap: 2,
                zIndex: 10,
            }}>
                <Button
                    label="Cancel"
                    color="grey"
                    prominence={false}
                    onClick={handleCancel}
                />

                <Box sx={{ display: "flex", gap: 2, marginLeft: "auto" }}>
                    {onSaveAndAddAnother && (
                        <Button
                            label="Save + Add Another"
                            color="blue"
                            prominence={false}
                            onClick={handleSaveAndAddAnother}
                        />
                    )}
                    <Button
                        label="Save"
                        color="blue"
                        prominence={true}
                        onClick={handleSubmit}
                    />
                </Box>
            </Box>
        </Box>
    );
}; 