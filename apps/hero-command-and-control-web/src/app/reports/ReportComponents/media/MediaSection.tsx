/**
 * @fileoverview MediaSection Component - Complete media file management for report sections
 * 
 * This is the main component for managing media files within report sections in the Hero
 * Command and Control web application. It provides a comprehensive interface for viewing,
 * managing, downloading, and deleting media files attached to specific report sections.
 * 
 * Core Functionality:
 * - Display media files in a tabular format with file previews
 * - Handle file uploads and attachments to report sections
 * - Provide download functionality with presigned URLs
 * - Enable file deletion with confirmation and cleanup
 * - Support various file types (images, videos, audio, documents, PDFs)
 * - Integrate with comment system for collaboration
 * - Handle loading states and error scenarios
 * 
 * Technical Architecture:
 * - Uses React Query for efficient API state management
 * - Integrates with filerepository service for file operations
 * - Manages protobuf data structures for report sections
 * - Implements optimistic updates for better UX
 * - <PERSON><PERSON> complex state synchronization between UI and backend
 * 
 * API Integration:
 * - Fetches media section data using useReportSection hook
 * - Updates section data using useUpdateReportSection mutation
 * - Manages file operations through filerepository service hooks
 * - <PERSON>les presigned URL generation for secure file access
 * 
 * @component
 * @example
 * // Basic usage in a report
 * <MediaSection
 *   reportId="report-123"
 *   mediaSectionId="section-456"
 *   onAddClick={() => openFileUploadDialog()}
 *   comments={sectionComments}
 *   onAddComment={handleAddComment}
 *   onResolveComment={handleResolveComment}
 * />
 * 
 * // Read-only mode for viewing
 * <MediaSection
 *   reportId="report-123"
 *   mediaSectionId="section-456"
 *   onAddClick={() => {}}
 *   readOnly={true}
 * />
 */

import { Button } from "@/design-system/components/Button";
import { Typography } from "@/design-system/components/Typography";
import { colors } from "@/design-system/tokens";
import { create } from "@bufbuild/protobuf";
import AddIcon from "@mui/icons-material/Add";
import CloseIcon from "@mui/icons-material/Close";
import DeleteIcon from "@mui/icons-material/Delete";
import DownloadIcon from "@mui/icons-material/Download";
import { Box, Dialog, DialogActions, DialogContent, DialogTitle, IconButton } from "@mui/material";
import { FileReference, FileReferenceSchema, SectionType } from "proto/hero/reports/v2/reports_pb";
import React, { useEffect, useState } from "react";
import {
  FaFile,
  FaFileAlt,
  FaFileAudio,
  FaFileCsv,
  FaFileImage,
  FaFilePdf,
  FaFileVideo,
} from "react-icons/fa";
import { useDeleteFile, useGetPresignedDownloadUrl } from "../../../apis/services/filerepository/hooks";
import { listPropertyFileAttachments } from "../../../apis/services/workflow/property/endpoints";
import { useReportSection, useUpdateReportSection } from "../../../apis/services/workflow/reports/v2/hooks";
import CommentSection from "../common/CommentSection";
import { LocalComment } from "../entities/EntityCard";

/**
 * Props interface for the MediaSection component
 * 
 * @interface MediaSectionProps
 */
interface MediaSectionProps {
  /** The unique identifier of the report containing this media section */
  reportId: string;

  /** The unique identifier of the media section (null for new sections) */
  mediaSectionId: string | null;

  /** Callback function triggered when the Add button is clicked to upload new files */
  onAddClick: () => void;

  /** Array of comments associated with this media section for collaboration */
  comments?: LocalComment[];

  /** Callback function for adding new comments to the section */
  onAddComment?: (text: string) => void;

  /** Callback function for resolving or unresolving comments */
  onResolveComment?: (id: string, resolved: boolean) => void;

  /** Whether the comment section should be initially expanded */
  commentsInitiallyExpanded?: boolean;

  /** Whether the component is in read-only mode (disables editing, deleting, and adding) */
  readOnly?: boolean;

  /** Properties data for showing property-associated media */
  properties?: any[];
}

/**
 * MediaSection Component
 * 
 * The primary component for managing media files within report sections. This component
 * handles the complete lifecycle of media files including display, download, deletion,
 * and section updates. It integrates closely with the backend services and maintains
 * complex state for optimal user experience.
 * 
 * State Management:
 * - mediaFiles: Array of FileReference objects from the backend
 * - deletingFileId: Tracks which file is currently being deleted
 * - downloadingFileId: Tracks which file is currently being downloaded/opened
 * 
 * The component uses React Query hooks for efficient data fetching and mutation
 * handling, providing automatic loading states, error handling, and cache management.
 * 
 * @param props - The props object containing configuration and event handlers
 * @returns A rendered MediaSection component with file management capabilities
 */
const MediaSection: React.FC<MediaSectionProps> = ({
  reportId,
  mediaSectionId,
  onAddClick,
  comments = [],
  onAddComment,
  onResolveComment,
  commentsInitiallyExpanded = false,
  readOnly = false,
  properties = [],
}) => {
  // State for managing media files and loading states
  const [mediaFiles, setMediaFiles] = useState<FileReference[]>([]);
  const [deletingFileId, setDeletingFileId] = useState<string | null>(null);
  const [downloadingFileId, setDownloadingFileId] = useState<string | null>(null);
  const [propertyFileAttachments, setPropertyFileAttachments] = useState<Record<string, any[]>>({});
  const [previewModalOpen, setPreviewModalOpen] = useState(false);
  const [previewFile, setPreviewFile] = useState<FileReference | null>(null);
  const [blobCache, setBlobCache] = useState<Map<string, string>>(new Map());
  const [previewLoading, setPreviewLoading] = useState<Set<string>>(new Set());
  const [csvData, setCsvData] = useState<string[][] | null>(null);

  // Cleanup blob URLs on unmount to prevent memory leaks
  React.useEffect(() => {
    return () => {
      blobCache.forEach((blobUrl) => {
        URL.revokeObjectURL(blobUrl);
      });
    };
  }, []);

  // React Query hook for fetching media section data
  const { data: mediaSectionData, refetch: refetchMediaSection } = useReportSection(
    reportId,
    mediaSectionId || "",
    {
      enabled: !!reportId && !!mediaSectionId,
    }
  );

  // Mutation hook for updating report section data
  const updateReportSectionMutation = useUpdateReportSection({
    onSuccess: () => {
      console.log("Media section updated successfully");
      refetchMediaSection();
    },
    onError: (error) => {
      console.error("Error updating media section:", error);
    },
  });

  // Mutation hook for deleting files from the repository
  const deleteFileMutation = useDeleteFile({
    onSuccess: () => {
      console.log("File deleted successfully");
    },
    onError: (error) => {
      console.error("Error deleting file:", error);
    },
  });

  // Mutation hook for generating presigned download URLs
  const getDownloadUrlMutation = useGetPresignedDownloadUrl({
    onSuccess: () => {
      console.log("Download URL generated successfully");
    },
    onError: (error) => {
      console.error("Error generating download URL:", error);
    },
  });

  // Effect to fetch property file attachments
  useEffect(() => {
    if (properties && properties.length > 0) {
      // Fetch property file attachments using the API endpoints directly
      const fetchPropertyAttachments = async () => {
        const attachmentPromises = properties.map(async (property) => {
          try {
            const response = await listPropertyFileAttachments({
              propertyId: property.id,
              pageSize: 50,
            });

            return { propertyId: property.id, attachments: response.fileAttachments || [] };
          } catch (error) {
            console.error(`Failed to fetch attachments for property ${property.id}:`, error);
            return { propertyId: property.id, attachments: [] };
          }
        });

        const results = await Promise.all(attachmentPromises);
        const attachmentMap: Record<string, any[]> = {};
        results.forEach(({ propertyId, attachments }) => {
          attachmentMap[propertyId] = attachments;
        });

        setPropertyFileAttachments(attachmentMap);
      };

      fetchPropertyAttachments();
    } else {
      // Clear attachments if no properties
      setPropertyFileAttachments({});
    }
  }, [properties]);

  /**
   * Effect hook to extract and set media files from section data
   * 
   * This effect handles the complex protobuf data structure transformation
   * and ensures the component state stays synchronized with the backend data.
   * It supports both the protobuf structure and transformed data formats.
   */
  useEffect(() => {
    if (mediaSectionData) {
      // Access media content from protobuf structure
      let mediaContent = null;
      if (mediaSectionData.content?.case === "mediaList") {
        mediaContent = mediaSectionData.content.value;
      } else if ((mediaSectionData as any).mediaList) {
        // Fallback for transformed data structure
        mediaContent = (mediaSectionData as any).mediaList;
      }

      if (mediaContent?.fileRefs) {
        setMediaFiles(mediaContent.fileRefs);
      } else {
        setMediaFiles([]);
      }
    }
  }, [mediaSectionData]);

  /**
   * Groups media files by their associated items (Report, PR1, PR2, etc.)
   */
  const getGroupedMediaFiles = () => {
    const groups: Array<{
      label: string;
      files: FileReference[];
      type: 'report' | 'property';
    }> = [];

    // Add report media files
    if (mediaFiles && mediaFiles.length > 0) {
      groups.push({
        label: 'Report',
        files: mediaFiles,
        type: 'report'
      });
    }

    // Add property media files from fetched attachments
    if (properties && properties.length > 0) {
      properties.forEach((property, index) => {
        // Get property file attachments from fetched data
        const propertyFiles = propertyFileAttachments[property.id] || [];
        if (propertyFiles.length > 0) {
          // Convert property file attachment format to FileReference format for consistency
          const convertedFiles: FileReference[] = propertyFiles.map((attachment: any) =>
            create(FileReferenceSchema, {
              id: attachment.id || `prop-${property.id}-${attachment.fileId}`,
              fileId: attachment.fileId || "",
              displayName: attachment.displayName || attachment.fileName || `Property File ${index + 1}`,
              caption: attachment.caption || `Attached to ${property.data?.description || 'Property'}`,
              fileCategory: attachment.fileCategory || 'property_attachment',
              displayOrder: attachment.displayOrder || 0,
              metadata: {
                fileType: attachment.fileType || attachment.mimeType || attachment.type || 'application/octet-stream',
                originalFilename: attachment.fileName || attachment.displayName || `Property File ${index + 1}`,
                ...attachment.metadata
              }
            })
          );

          groups.push({
            label: `Property ${index + 1}`,
            files: convertedFiles,
            type: 'property'
          });
        }
      });
    }

    return groups;
  };

  /**
   * Determines the appropriate file icon based on MIME type
   * 
   * This function analyzes the file metadata to determine the file type
   * and returns the corresponding React icon component. It provides
   * fallback handling for unknown or missing MIME types.
   * 
   * @param metadata - File metadata object containing type information
   * @returns JSX element representing the file type icon
   */
  const getFileIcon = (metadata?: any) => {
    const fileType = (typeof metadata?.fileType === 'string' ? metadata.fileType.toLowerCase() : '') || '';

    // Detection based ONLY on MIME type
    if (fileType.startsWith('image/')) {
      return <FaFileImage style={{ fontSize: 20, color: colors.blue[600] }} />;
    }
    if (fileType.startsWith('video/')) {
      return <FaFileVideo style={{ fontSize: 20, color: colors.blue[600] }} />;
    }
    if (fileType.startsWith('audio/')) {
      return <FaFileAudio style={{ fontSize: 20, color: colors.blue[600] }} />;
    }
    if (fileType === 'application/pdf') {
      return <FaFilePdf style={{ fontSize: 20, color: colors.blue[600] }} />;
    }
    if (fileType === 'text/csv' || fileType === 'application/vnd.ms-excel') {
      return <FaFileCsv style={{ fontSize: 20, color: colors.blue[600] }} />;
    }
    if (fileType.includes('text/') || fileType.includes('document') || fileType.includes('word')) {
      return <FaFileAlt style={{ fontSize: 20, color: colors.blue[600] }} />;
    }

    // Default icon if MIME type is unknown or missing
    return <FaFile style={{ fontSize: 20, color: colors.grey[500] }} />;
  };

  const isPreviewableFile = (metadata?: any): boolean => {
    const fileType = (typeof metadata?.fileType === 'string' ? metadata.fileType.toLowerCase() : '') || '';
    return fileType.startsWith('image/') || fileType.startsWith('video/') || fileType === 'application/pdf' || fileType === 'text/csv' || fileType === 'application/vnd.ms-excel';
  };

  /**
   * Parse CSV text with proper handling of quoted values containing commas
   * 
   * @param text - Raw CSV text content
   * @returns Array of rows, where each row is an array of cell values
   */
  const parseCSV = (text: string): string[][] => {
    const rows: string[][] = [];
    const lines = text.split('\n');

    for (const line of lines) {
      const trimmedLine = line.trim();
      if (!trimmedLine) continue;

      const row: string[] = [];
      let currentCell = '';
      let insideQuotes = false;
      let i = 0;

      while (i < trimmedLine.length) {
        const char = trimmedLine[i];

        if (char === '"') {
          // Handle escaped quotes (double quotes)
          if (i + 1 < trimmedLine.length && trimmedLine[i + 1] === '"') {
            currentCell += '"';
            i += 2;
          } else {
            // Toggle quote state
            insideQuotes = !insideQuotes;
            i++;
          }
        } else if (char === ',' && !insideQuotes) {
          // End of cell
          row.push(currentCell.trim());
          currentCell = '';
          i++;
        } else {
          // Regular character
          currentCell += char;
          i++;
        }
      }

      // Add the last cell
      row.push(currentCell.trim());

      // Only add rows that have content
      if (row.some(cell => cell !== '')) {
        rows.push(row);
      }
    }

    return rows;
  };

  const FilePreview: React.FC<{
    file: FileReference;
    blobCache: Map<string, string>;
    setBlobCache: React.Dispatch<React.SetStateAction<Map<string, string>>>;
    previewLoading: Set<string>;
    setPreviewLoading: React.Dispatch<React.SetStateAction<Set<string>>>;
    getDownloadUrlMutation: any;
    onPreview: (file: FileReference) => void;
  }> = ({ file, blobCache, setBlobCache, previewLoading, setPreviewLoading, getDownloadUrlMutation, onPreview }) => {
    const fileType = (typeof file.metadata?.fileType === 'string' ? file.metadata.fileType.toLowerCase() : '') || '';
    const isImage = fileType.startsWith('image/');
    const isVideo = fileType.startsWith('video/');
    const isPdf = fileType === 'application/pdf';
    const isCsv = fileType === 'text/csv' || fileType === 'application/vnd.ms-excel';
    const isLoading = previewLoading.has(file.id);
    const blobUrl = blobCache.get(file.id);

    const loadImagePreview = async () => {
      if (!isImage || blobUrl || isLoading) return;

      try {
        setPreviewLoading(prev => new Set([...prev, file.id]));

        const response = await getDownloadUrlMutation.mutateAsync({
          id: file.fileId,
          expiresIn: 300
        } as any);

        const fileResponse = await fetch(response.presignedUrl);
        const blob = await fileResponse.blob();
        const url = URL.createObjectURL(blob);

        setBlobCache(prev => new Map(prev).set(file.id, url));
      } catch (error) {
        console.error('Failed to load image preview:', error);
      } finally {
        setPreviewLoading(prev => {
          const newSet = new Set(prev);
          newSet.delete(file.id);
          return newSet;
        });
      }
    };

    // Only load image previews automatically
    React.useEffect(() => {
      if (isImage) {
        loadImagePreview();
      }
    }, [file.id, isImage]);

    const handleClick = () => {
      onPreview(file);
    };

    return (
      <Box
        onClick={handleClick}
        sx={{
          width: 80,
          height: 60,
          borderRadius: "8px",
          border: `1px solid ${colors.grey[200]}`,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          backgroundColor: colors.grey[50],
          cursor: 'pointer',
          overflow: 'hidden',
          position: 'relative',
          '&:hover': {
            borderColor: colors.blue[300],
            backgroundColor: colors.blue[50],
          },
        }}
      >
        {isLoading && isImage ? (
          <Box
            sx={{
              width: 16,
              height: 16,
              border: `2px solid ${colors.blue[200]}`,
              borderTop: `2px solid ${colors.blue[600]}`,
              borderRadius: '50%',
              animation: 'spin 1s linear infinite',
              '@keyframes spin': {
                '0%': { transform: 'rotate(0deg)' },
                '100%': { transform: 'rotate(360deg)' },
              },
            }}
          />
        ) : blobUrl && isImage ? (
          <img
            src={blobUrl}
            alt={file.displayName || 'File preview'}
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'cover',
            }}
            onError={() => console.error('Failed to load image thumbnail')}
          />
        ) : isLoading && (isVideo || isPdf || isCsv) ? (
          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 1 }}>
            <Box
              sx={{
                width: 16,
                height: 16,
                border: `2px solid ${colors.blue[200]}`,
                borderTop: `2px solid ${colors.blue[600]}`,
                borderRadius: '50%',
                animation: 'spin 1s linear infinite',
                '@keyframes spin': {
                  '0%': { transform: 'rotate(0deg)' },
                  '100%': { transform: 'rotate(360deg)' },
                },
              }}
            />
            {isVideo ? (
              <FaFileVideo style={{ fontSize: 16, color: colors.blue[600] }} />
            ) : isCsv ? (
              <FaFileCsv style={{ fontSize: 16, color: colors.blue[600] }} />
            ) : (
              <FaFilePdf style={{ fontSize: 16, color: colors.blue[600] }} />
            )}
          </Box>
        ) : isVideo ? (
          <FaFileVideo style={{ fontSize: 24, color: colors.blue[600] }} />
        ) : isPdf ? (
          <FaFilePdf style={{ fontSize: 24, color: colors.blue[600] }} />
        ) : isCsv ? (
          <FaFileCsv style={{ fontSize: 24, color: colors.blue[600] }} />
        ) : (
          getFileIcon(file.metadata)
        )}
      </Box>
    );
  };

  /**
   * Handles file downloads with presigned URLs
   * 
   * This function generates a secure presigned URL, fetches the file content,
   * and triggers a browser download with the correct filename. It includes
   * proper error handling and loading states.
   * 
   * @param fileId - The unique identifier of the file in the repository
   * @param fileName - The display name for the downloaded file
   * @param fileRefId - The reference ID for tracking the file in the UI
   */
  const handleDownloadFile = async (fileId: string, fileName: string, fileRefId: string) => {
    try {
      setDownloadingFileId(fileRefId);
      console.log('Downloading file:', fileId, fileName);

      // Get presigned download URL with 5-minute expiration
      const response = await getDownloadUrlMutation.mutateAsync({
        id: fileId,
        expiresIn: 300 // 5 minutes
      } as any);

      // The backend automatically logs 'download' when GetPresignedDownloadUrl is called
      // Fetch the file content and create a blob URL to force download
      const fileResponse = await fetch(response.presignedUrl);
      const blob = await fileResponse.blob();

      // Create a blob URL and trigger download
      const blobUrl = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = blobUrl;
      link.download = fileName; // This forces download with the correct filename
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Clean up the blob URL to free memory
      URL.revokeObjectURL(blobUrl);

      console.log('File download initiated successfully');
    } catch (error) {
      console.error('Download failed:', error);
      alert('Failed to download file. Please try again.');
    } finally {
      setDownloadingFileId(null);
    }
  };

  /**
   * Handles file preview with modal display
   * 
   * This function handles both previewable files (images, videos, PDFs) by showing them
   * in a modal, and non-previewable files by triggering download.
   * 
   * @param file - The FileReference object containing file information
   */
  const handlePreviewFile = async (file: FileReference) => {
    if (!isPreviewableFile(file.metadata)) {
      // For non-previewable files, just download
      return handleDownloadFile(
        file.fileId,
        file.displayName || (typeof file.metadata?.originalFilename === 'string' ? file.metadata.originalFilename : '') || 'Untitled',
        file.id
      );
    }

    const fileType = (typeof file.metadata?.fileType === 'string' ? file.metadata.fileType.toLowerCase() : '') || '';
    const isCsv = fileType === 'text/csv' || fileType === 'application/vnd.ms-excel';

    // Check if we already have the blob cached
    const cachedBlob = blobCache.get(file.id);
    if (cachedBlob && !isCsv) {
      setPreviewFile(file);
      setPreviewModalOpen(true);
      return;
    }

    // Check if we have CSV data cached
    if (isCsv && csvData && previewFile?.id === file.id) {
      setPreviewFile(file);
      setPreviewModalOpen(true);
      return;
    }

    // Download and cache the file for preview (lazy loading)
    try {
      setPreviewLoading(prev => new Set([...prev, file.id]));

      const response = await getDownloadUrlMutation.mutateAsync({
        id: file.fileId,
        expiresIn: 300
      } as any);

      const fileResponse = await fetch(response.presignedUrl);
      const blob = await fileResponse.blob();

      if (isCsv) {
        // Parse CSV data with proper handling of quoted values
        const text = await blob.text();
        const rows = parseCSV(text);
        setCsvData(rows);
      } else {
        // Store blob URL for other file types
        const blobUrl = URL.createObjectURL(blob);
        setBlobCache(prev => new Map(prev).set(file.id, blobUrl));
      }

      setPreviewFile(file);
      setPreviewModalOpen(true);
    } catch (error) {
      console.error('Failed to open file preview:', error);
      alert('Failed to open file preview. Please try again.');
    } finally {
      setPreviewLoading(prev => {
        const newSet = new Set(prev);
        newSet.delete(file.id);
        return newSet;
      });
    }
  };

  const handleClosePreview = () => {
    setPreviewModalOpen(false);
    setPreviewFile(null);
    setCsvData(null);
  };

  /**
   * Handles file deletion with confirmation and cleanup
   * 
   * This function performs a two-step deletion process:
   * 1. Soft deletes the file from the repository
   * 2. Updates the media section to remove the file reference
   * 
   * It includes confirmation dialog, error handling, and proper state management.
   * The function also handles display order reordering for remaining files.
   * 
   * @param fileId - The unique identifier of the file in the repository
   * @param fileRefId - The reference ID of the file in the section
   */
  const handleDeleteFile = async (fileId: string, fileRefId: string) => {
    if (!confirm('Are you sure you want to delete this file? This will remove it from the report and delete it from the database.')) {
      return;
    }

    try {
      setDeletingFileId(fileRefId);
      console.log('Deleting file:', fileId, 'with reference ID:', fileRefId);

      // Step 1: Soft delete the file in the file repository
      await deleteFileMutation.mutateAsync({ id: fileId } as any);

      // Step 2: Update the media section to remove the file reference
      if (mediaSectionData && mediaSectionId) {
        // Get current media content
        let currentMediaContent = null;
        if (mediaSectionData.content?.case === "mediaList") {
          currentMediaContent = mediaSectionData.content.value;
        } else if ((mediaSectionData as any).mediaList) {
          currentMediaContent = (mediaSectionData as any).mediaList;
        }

        if (currentMediaContent) {
          // Remove the file reference from the list
          const updatedFileRefs = (currentMediaContent.fileRefs || []).filter(
            (fileRef: FileReference) => fileRef.id !== fileRefId
          );

          // Update display order for remaining files using protobuf create
          const reorderedFileRefs = updatedFileRefs.map((fileRef: FileReference, index: number) =>
            create(FileReferenceSchema, {
              ...fileRef,
              displayOrder: index,
            })
          );

          // Create updated media content with metadata
          const updatedMediaContent = {
            ...currentMediaContent,
            fileRefs: reorderedFileRefs,
            metadata: {
              ...(currentMediaContent.metadata || {}),
              totalFiles: reorderedFileRefs.length,
              lastUpdated: new Date().toISOString(),
            },
          };

          // Update the media section using flat structure (like other sections)
          const section = {
            ...mediaSectionData,
            id: mediaSectionId,
            type: SectionType.MEDIA,
            mediaList: updatedMediaContent,
            reportId,
          };

          const updateRequest = {
            reportId,
            section,
          };

          console.log('Updating media section after file deletion:', updateRequest);
          // @ts-expect-error TODO: Fix type issue - Type mismatch with protobuf, but structure is correct
          await updateReportSectionMutation.mutateAsync(updateRequest);
        }
      }

      console.log('File deleted and media section updated successfully');
    } catch (error) {
      console.error('Error deleting file:', error);
      alert('Failed to delete file. Please try again.');
    } finally {
      setDeletingFileId(null);
    }
  };

  return (
    <Box
      sx={{
        display: "flex",
        width: "100%",
        flexDirection: "column",
        alignItems: "flex-start",
        gap: "24px",
        borderRadius: "12px",
        border: `1px solid ${colors.grey[200]}`,
        background: "#FFF",
        overflow: "hidden",
        boxShadow: "0px 0px 16px 0px rgba(0, 0, 0, 0.04)",
      }}
    >
      {/* Section Header - Contains title and Add button */}
      <Box
        sx={{
          display: "flex",
          width: "100%",
          justifyContent: "space-between",
          alignItems: "center",
          padding: "24px",
          paddingBottom: "0px",
        }}
      >
        <Typography style="h2" color={colors.grey[900]}>
          Media
        </Typography>

        {/* Add Button - Only shown in edit mode */}
        {!readOnly && (
          <Button
            label="Add"
            leftIcon={<AddIcon />}
            color="blue"
            prominence={false}
            onClick={onAddClick}
          />
        )}
      </Box>

      {/* Media Files Table - Displays files in a structured table format grouped by source */}
      {(() => {
        const groupedMedia = getGroupedMediaFiles();
        if (groupedMedia.length === 0) return null;

        return (
          <Box sx={{ width: "100%", px: 3 }}>
            {/* Use HTML table for proper rowspan support */}
            <Box
              component="table"
              sx={{
                width: "100%",
                borderCollapse: "separate",
                borderSpacing: 0,
              }}
            >
              {/* Table Header */}
              <Box component="thead">
                <Box
                  component="tr"
                  sx={{
                    borderBottom: `1px solid ${colors.grey[200]}`,
                  }}
                >
                  <Box
                    component="th"
                    sx={{
                      width: "120px",
                      textAlign: "left",
                      py: 1,
                      px: 1,
                    }}
                  >
                    <Typography style="body3" color={colors.grey[500]}>
                      Source
                    </Typography>
                  </Box>
                  <Box
                    component="th"
                    sx={{
                      width: "60px",
                      textAlign: "left",
                      py: 1,
                      px: 1,
                    }}
                  >
                    <Typography style="body3" color={colors.grey[500]}>
                      Preview
                    </Typography>
                  </Box>
                  <Box
                    component="th"
                    sx={{
                      textAlign: "left",
                      py: 1,
                      px: 1,
                    }}
                  >
                    <Typography style="body3" color={colors.grey[500]}>
                      Title
                    </Typography>
                  </Box>
                  <Box
                    component="th"
                    sx={{
                      textAlign: "left",
                      py: 1,
                      px: 1,
                    }}
                  >
                    <Typography style="body3" color={colors.grey[500]}>
                      Description
                    </Typography>
                  </Box>
                  <Box
                    component="th"
                    sx={{
                      width: "120px",
                      textAlign: "left",
                      py: 1,
                      px: 1,
                    }}
                  >
                    <Typography style="body3" color={colors.grey[500]}>
                      Category
                    </Typography>
                  </Box>
                  <Box
                    component="th"
                    sx={{
                      width: "80px",
                      textAlign: "left",
                      py: 1,
                      px: 1,
                    }}
                  >
                    <Typography style="body3" color={colors.grey[500]}>
                      Actions
                    </Typography>
                  </Box>
                </Box>
              </Box>

              {/* Table Body */}
              <Box component="tbody">
                {groupedMedia.map((group, groupIndex) => {
                  return group.files.map((file, fileIndex) => {
                    const isFirstInGroup = fileIndex === 0;
                    const isLastFileInGroup = fileIndex === group.files.length - 1;
                    const isLastGroup = groupIndex === groupedMedia.length - 1;
                    const isLastFile = isLastGroup && isLastFileInGroup;

                    // Determine border style: thicker border between groups, thin border within groups
                    let borderBottom = 'none';
                    if (!isLastFile) {
                      if (isLastFileInGroup && !isLastGroup) {
                        // Last file in group but not last group overall - thicker separator
                        borderBottom = `2px solid ${colors.grey[200]}`;
                      } else {
                        // Regular file within group - thin separator
                        borderBottom = `1px solid ${colors.grey[100]}`;
                      }
                    }

                    return (
                      <Box
                        key={`${groupIndex}-${fileIndex}`}
                        component="tr"
                        sx={{
                          borderBottom,
                          '& td': {
                            borderBottom: borderBottom === 'none' ? 'none' : 'inherit',
                          }
                        }}
                      >
                        {/* Source Label Column with rowspan - only show for first file in group */}
                        {isFirstInGroup && (
                          <Box
                            component="td"
                            rowSpan={group.files.length}
                            sx={{
                              borderRight: `1px solid ${colors.grey[200]}`,
                              verticalAlign: 'middle',
                              textAlign: 'center',
                              px: 1,
                              py: 2,
                            }}
                          >
                            <Box
                              sx={{
                                px: 1.5,
                                py: 0.75,
                                borderRadius: '8px',
                                backgroundColor: group.type === 'report' ? colors.blue[100] : colors.amber[100],
                                color: group.type === 'report' ? colors.blue[700] : colors.amber[700],
                                fontSize: '12px',
                                fontWeight: 600,
                                textAlign: 'center',
                                minWidth: '50px',
                                whiteSpace: 'nowrap',
                                display: 'inline-block',
                              }}
                            >
                              {group.label}
                            </Box>
                          </Box>
                        )}

                        {/* File Icon/Preview Column */}
                        <Box
                          component="td"
                          sx={{
                            px: 1,
                            py: 2,
                            verticalAlign: 'middle',
                          }}
                        >
                          <FilePreview
                            file={file}
                            blobCache={blobCache}
                            setBlobCache={setBlobCache}
                            previewLoading={previewLoading}
                            setPreviewLoading={setPreviewLoading}
                            getDownloadUrlMutation={getDownloadUrlMutation}
                            onPreview={handlePreviewFile}
                          />
                        </Box>

                        {/* Title Column - Clickable to open file in new tab */}
                        <Box
                          component="td"
                          sx={{
                            px: 1,
                            py: 2,
                            verticalAlign: 'middle',
                          }}
                        >
                          <Box
                            onClick={() => handlePreviewFile(file)}
                            sx={{
                              cursor: downloadingFileId === file.id ? 'wait' : 'pointer',
                              display: 'flex',
                              alignItems: 'center',
                              gap: 1,
                              '&:hover': {
                                textDecoration: 'underline',
                              },
                            }}
                          >
                            {/* Loading spinner for file operations */}
                            {downloadingFileId === file.id && (
                              <Box
                                sx={{
                                  width: 12,
                                  height: 12,
                                  border: `2px solid ${colors.blue[200]}`,
                                  borderTop: `2px solid ${colors.blue[600]}`,
                                  borderRadius: '50%',
                                  animation: 'spin 1s linear infinite',
                                  '@keyframes spin': {
                                    '0%': { transform: 'rotate(0deg)' },
                                    '100%': { transform: 'rotate(360deg)' },
                                  },
                                }}
                              />
                            )}
                            <Typography
                              style="body2"
                              color={downloadingFileId === file.id ? colors.blue[300] : colors.blue[600]}
                            >
                              {file.displayName || (typeof file.metadata?.originalFilename === 'string' ? file.metadata.originalFilename : '') || 'Untitled'}
                            </Typography>
                          </Box>
                        </Box>

                        {/* Description Column - Shows file caption or default text */}
                        <Box
                          component="td"
                          sx={{
                            px: 1,
                            py: 2,
                            verticalAlign: 'middle',
                          }}
                        >
                          <Typography style="body3" color={colors.grey[700]}>
                            {file.caption || 'No description'}
                          </Typography>
                        </Box>

                        {/* Category Column - Shows file category as a styled tag */}
                        <Box
                          component="td"
                          sx={{
                            px: 1,
                            py: 2,
                            verticalAlign: 'middle',
                          }}
                        >
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "center",
                            }}
                          >
                            <Box
                              sx={{
                                px: 2,
                                py: 0.5,
                                borderRadius: "12px",
                                backgroundColor: colors.grey[100],
                                display: "inline-flex",
                                alignItems: "center",
                                justifyContent: "center",
                                maxWidth: "100%",
                              }}
                            >
                              <Typography style="tag2" color={colors.grey[700]}>
                                {file.fileCategory || 'Unknown'}
                              </Typography>
                            </Box>
                          </Box>
                        </Box>

                        {/* Action Buttons Column - Download and Delete buttons */}
                        <Box
                          component="td"
                          sx={{
                            px: 1,
                            py: 2,
                            verticalAlign: 'middle',
                          }}
                        >
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                            {/* Download Button - Triggers file download */}
                            <IconButton
                              onClick={() => handleDownloadFile(
                                file.fileId,
                                file.displayName || (typeof file.metadata?.originalFilename === 'string' ? file.metadata.originalFilename : '') || 'Untitled',
                                file.id
                              )}
                              size="small"
                              disabled={downloadingFileId === file.id}
                              sx={{
                                color: downloadingFileId === file.id ? colors.grey[300] : colors.blue[600],
                                "&:hover": {
                                  color: downloadingFileId === file.id ? colors.grey[300] : colors.blue[700],
                                  backgroundColor: downloadingFileId === file.id ? 'transparent' : colors.blue[50],
                                },
                              }}
                              aria-label={`Download ${file.displayName || 'file'}`}
                            >
                              {downloadingFileId === file.id ? (
                                <Box
                                  sx={{
                                    width: 16,
                                    height: 16,
                                    border: `2px solid ${colors.blue[200]}`,
                                    borderTop: `2px solid ${colors.blue[600]}`,
                                    borderRadius: '50%',
                                    animation: 'spin 1s linear infinite',
                                    '@keyframes spin': {
                                      '0%': { transform: 'rotate(0deg)' },
                                      '100%': { transform: 'rotate(360deg)' },
                                    },
                                  }}
                                />
                              ) : (
                                <DownloadIcon sx={{ fontSize: 16 }} />
                              )}
                            </IconButton>

                            {/* Delete Button - Only shown in edit mode */}
                            {!readOnly && (
                              <IconButton
                                onClick={() => handleDeleteFile(file.fileId, file.id)}
                                size="small"
                                disabled={deletingFileId === file.id}
                                sx={{
                                  color: deletingFileId === file.id ? colors.grey[300] : colors.grey[400],
                                  "&:hover": {
                                    color: deletingFileId === file.id ? colors.grey[300] : colors.grey[600],
                                    backgroundColor: deletingFileId === file.id ? 'transparent' : colors.grey[100],
                                  },
                                }}
                                aria-label={`Delete ${file.displayName || 'file'}`}
                              >
                                {deletingFileId === file.id ? (
                                  <Box
                                    sx={{
                                      width: 16,
                                      height: 16,
                                      border: `2px solid ${colors.grey[300]}`,
                                      borderTop: `2px solid ${colors.grey[600]}`,
                                      borderRadius: '50%',
                                      animation: 'spin 1s linear infinite',
                                      '@keyframes spin': {
                                        '0%': { transform: 'rotate(0deg)' },
                                        '100%': { transform: 'rotate(360deg)' },
                                      },
                                    }}
                                  />
                                ) : (
                                  <DeleteIcon sx={{ fontSize: 16 }} />
                                )}
                              </IconButton>
                            )}
                          </Box>
                        </Box>
                      </Box>
                    );
                  });
                })}
              </Box>
            </Box>
          </Box>
        );
      })()}

      {/* Comment Section - Enables collaboration and feedback on the media section */}
      <CommentSection
        comments={comments}
        onAddComment={onAddComment}
        onResolveComment={onResolveComment}
        initiallyExpanded={commentsInitiallyExpanded}
        readOnly={readOnly}
      />

      {/* Preview Modal */}
      <Dialog
        open={previewModalOpen}
        onClose={handleClosePreview}
        maxWidth="lg"
        fullWidth
        slotProps={{
          paper: {
            sx: {
              borderRadius: 2,
              maxHeight: '90vh',
            }
          }
        }}
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Typography style="h3" color={colors.grey[900]}>
              {previewFile?.displayName || (typeof previewFile?.metadata?.originalFilename === 'string' ? previewFile.metadata.originalFilename : '') || 'Untitled'}
            </Typography>
            <IconButton
              onClick={handleClosePreview}
              sx={{ color: colors.grey[500] }}
            >
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>

        <DialogContent sx={{ p: 0, display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 400 }}>
          {previewFile && (
            <Box sx={{ width: '100%', height: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
              {previewLoading.has(previewFile.id) ? (
                <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 2 }}>
                  <Box
                    sx={{
                      width: 32,
                      height: 32,
                      border: `3px solid ${colors.blue[200]}`,
                      borderTop: `3px solid ${colors.blue[600]}`,
                      borderRadius: '50%',
                      animation: 'spin 1s linear infinite',
                      '@keyframes spin': {
                        '0%': { transform: 'rotate(0deg)' },
                        '100%': { transform: 'rotate(360deg)' },
                      },
                    }}
                  />
                  <Typography style="body2" color={colors.grey[600]}>
                    Loading preview...
                  </Typography>
                </Box>
              ) : (
                <>
                  {typeof previewFile.metadata?.fileType === 'string' && previewFile.metadata.fileType.startsWith('image/') && blobCache.get(previewFile.id) && (
                    <img
                      src={blobCache.get(previewFile.id)}
                      alt={previewFile.displayName || 'File preview'}
                      style={{
                        maxWidth: '100%',
                        maxHeight: '70vh',
                        objectFit: 'contain',
                      }}
                    />
                  )}
                  {typeof previewFile.metadata?.fileType === 'string' && previewFile.metadata.fileType.startsWith('video/') && blobCache.get(previewFile.id) && (
                    <video
                      src={blobCache.get(previewFile.id)}
                      controls
                      style={{
                        maxWidth: '100%',
                        maxHeight: '70vh',
                      }}
                    />
                  )}
                  {typeof previewFile.metadata?.fileType === 'string' && previewFile.metadata.fileType === 'application/pdf' && blobCache.get(previewFile.id) && (
                    <iframe
                      src={blobCache.get(previewFile.id)}
                      style={{
                        width: '100%',
                        height: '70vh',
                        border: 'none',
                      }}
                      title={previewFile.displayName || 'PDF preview'}
                    />
                  )}
                  {typeof previewFile.metadata?.fileType === 'string' && (previewFile.metadata.fileType === 'text/csv' || previewFile.metadata.fileType === 'application/vnd.ms-excel') && csvData && (
                    <Box sx={{
                      width: '100%',
                      height: '70vh',
                      overflow: 'auto',
                      p: 2,
                    }}>
                      <table style={{
                        width: '100%',
                        borderCollapse: 'collapse',
                        fontSize: '14px',
                      }}>
                        <thead>
                          <tr>
                            {csvData[0]?.map((header, index) => (
                              <th key={index} style={{
                                padding: '8px 12px',
                                textAlign: 'left',
                                borderBottom: `2px solid ${colors.grey[300]}`,
                                backgroundColor: colors.grey[50],
                                fontWeight: 600,
                                position: 'sticky',
                                top: 0,
                                zIndex: 1,
                              }}>
                                {header}
                              </th>
                            ))}
                          </tr>
                        </thead>
                        <tbody>
                          {csvData.slice(1).map((row, rowIndex) => (
                            <tr key={rowIndex}>
                              {row.map((cell, cellIndex) => (
                                <td key={cellIndex} style={{
                                  padding: '8px 12px',
                                  borderBottom: `1px solid ${colors.grey[200]}`,
                                }}>
                                  {cell}
                                </td>
                              ))}
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </Box>
                  )}
                </>
              )}
            </Box>
          )}
        </DialogContent>

        <DialogActions sx={{ px: 3, pb: 3, gap: 2 }}>
          <Button
            label="Download"
            leftIcon={<DownloadIcon />}
            color="blue"
            prominence={false}
            onClick={() => previewFile && handleDownloadFile(
              previewFile.fileId,
              previewFile.displayName || (typeof previewFile.metadata?.originalFilename === 'string' ? previewFile.metadata.originalFilename : '') || 'Untitled',
              previewFile.id
            )}
          />
          <Button
            label="Close"
            color="grey"
            prominence={true}
            onClick={handleClosePreview}
          />
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default MediaSection; 