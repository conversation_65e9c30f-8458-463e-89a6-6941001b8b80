// Shared property helpers

export const getReadablePropertyType = (propertyType: string): string => {
    switch (propertyType) {
        case "PROPERTY_TYPE_FOUND":
            return "Found Property";
        case "PROPERTY_TYPE_SEIZED":
            return "Seized Property";
        case "PROPERTY_TYPE_STOLEN":
            return "Stolen Property";
        case "PROPERTY_TYPE_SAFEKEEPING":
            return "Safekeeping Property";
        case "PROPERTY_TYPE_MISSING":
            return "Missing Property";
        case "PROPERTY_TYPE_RECOVERED":
            return "Recovered Property";
        default:
            return "Property";
    }
};

