"use client";

import { useBreadcrumbHeader } from "@/app/hooks/useBreadcrumbHeader";
import { useRecentlyViewedTracker } from "@/app/hooks/useRecentlyViewedTracker";
import { Header } from "@/design-system/components/Header";
import { Entity } from "proto/hero/entity/v1/entity_pb";
import { OrganizationData } from "../utils/organizationTypes";
import EntityActionsMenu from "./EntityActionsMenu";

interface OrganizationHeaderProps {
  organization: Entity | undefined;
  onClose: () => void;
}

export default function OrganizationHeader({ organization, onClose }: OrganizationHeaderProps) {
  // Extract organization data
  const extractOrganizationData = (): OrganizationData | null => {
    if (!organization?.data) return null;

    try {
      const data = typeof organization.data === 'string' ? JSON.parse(organization.data) : organization.data;
      return {
        informationSection: data?.informationSection || {},
        createTime: data?.createTime || "",
        updateTime: data?.updateTime || "",
      };
    } catch (error) {
      console.warn("Failed to parse organization entity data:", error);
      return null;
    }
  };

  const organizationData = extractOrganizationData();
  const organizationName = organizationData?.informationSection?.name || "Unknown Organization";

  // Get organization ID in display format
  const organizationId = organization?.id?.slice(0, 7) || "Unknown ID";

  const { breadcrumbs } = useBreadcrumbHeader({
    id: `entity-${organization?.id}`,
    label: organizationName,
    path: `/entity?entityId=${organization?.id}`,
  });

  useRecentlyViewedTracker({
    id: `entity-${organization?.id}`,
    title: organizationName,
    subtitle: "Organization Record",
    path: `/entity?entityId=${organization?.id}`,
  });

  // Format date from timestamp or ISO string
  const formatDate = (timestamp: string): string => {
    if (!timestamp) return "N/A";
    try {
      const date = new Date(timestamp);

      return date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "2-digit",
      });
    } catch (e) {
      return "N/A";
    }
  };

  const handleManage = () => {
    console.log("Manage organization:", organization?.id);
    // TODO: Implement manage functionality
  };

  const handleDelete = () => {
    console.log("Delete organization:", organization?.id);
    // TODO: Implement delete functionality
  };

  return (
    <Header
      breadcrumbs={breadcrumbs}
      title={organizationName}
      metadata={[
        {
          label: "Date Created",
          value: formatDate(organization?.createTime || "")
        },
        {
          label: "Last Updated",
          value: formatDate(organization?.updateTime || "")
        },
        {
          label: "ID",
          value: organizationId
        }
      ]}
      actions={[]}
      statusIndicator={
        <EntityActionsMenu
          onManage={handleManage}
          onDelete={handleDelete}
          entityId={organization?.id}
          entityName={organizationName}
          entityType="organization"
          entity={organization}
        />
      }
    />
  );
}