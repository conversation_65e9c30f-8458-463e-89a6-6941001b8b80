import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
    addCustodyEvent,
    addPropertyFileAttachment,
    batchGetProperties,
    createProperty,
    deleteProperty,
    getCustodyChain,
    getProperty,
    listProperties,
    listPropertiesByCaseNumber,
    listPropertiesByOfficer,
    listPropertyFileAttachments,
    removePropertyFileAttachment,
    searchProperties,
    updateProperty,
    updatePropertyStatus
} from './endpoints';
import { CustodyEvent, PropertyFileReference, PropertyStatus, PropertyType, SearchPropertiesRequest } from './types';

// Query Keys
export const propertyKeys = {
    all: ['properties'] as const,
    lists: () => [...propertyKeys.all, 'list'] as const,
    list: (filters: any) => [...propertyKeys.lists(), filters] as const,
    details: () => [...propertyKeys.all, 'detail'] as const,
    detail: (id: string) => [...propertyKeys.details(), id] as const,
    byCase: (caseNumber: string) => [...propertyKeys.all, 'case', caseNumber] as const,
    byOfficer: (officerId: string) => [...propertyKeys.all, 'officer', officerId] as const,
    custodyChain: (propertyId: string) => [...propertyKeys.all, 'custody', propertyId] as const,
};

// Queries
export const useProperty = (id: string) => {
    return useQuery({
        queryKey: propertyKeys.detail(id),
        queryFn: async () => {
            if (!id) {
                console.warn('useProperty: No property ID provided');
                throw new Error('Property ID is required');
            }
            return getProperty(id);
        },
        enabled: !!id,
        retry: (failureCount, error) => {
            // Retry up to 3 times for network errors, but not for 404s
            if (failureCount >= 3) return false;
            if (error?.message?.includes('404') || error?.message?.includes('not found')) return false;
            return true;
        },
    });
};

export const useProperties = (params: {
    pageSize?: number;
    pageToken?: string;
    propertyType?: PropertyType;
    propertyStatus?: PropertyStatus;
    orderBy?: string;
}) => {
    return useQuery({
        queryKey: propertyKeys.list(params),
        queryFn: () => listProperties(params),
    });
};

export const useSearchProperties = (request: SearchPropertiesRequest, options?: { enabled?: boolean }) => {
    return useQuery({
        queryKey: [...propertyKeys.all, 'search', request],
        queryFn: () => searchProperties(request),
        enabled: options?.enabled ?? (!!request.query || (request.fieldQueries && request.fieldQueries.length > 0)),
    });
};

export const useBatchGetProperties = (ids: string[]) => {
    return useQuery({
        queryKey: [...propertyKeys.all, 'batch', ids],
        queryFn: async () => {
            if (!ids || ids.length === 0) {
                console.warn('useBatchGetProperties: No property IDs provided');
                return [];
            }
            return batchGetProperties(ids);
        },
        enabled: ids.length > 0,
        retry: (failureCount, error) => {
            // Retry up to 3 times for network errors, but not for 404s
            if (failureCount >= 3) return false;
            if (error?.message?.includes('404') || error?.message?.includes('not found')) return false;
            return true;
        },
    });
};

export const usePropertiesByCaseNumber = (caseNumber: string) => {
    return useQuery({
        queryKey: propertyKeys.byCase(caseNumber),
        queryFn: () => listPropertiesByCaseNumber(caseNumber),
        enabled: !!caseNumber,
    });
};

export const usePropertiesByOfficer = (officerId: string) => {
    return useQuery({
        queryKey: propertyKeys.byOfficer(officerId),
        queryFn: () => listPropertiesByOfficer(officerId),
        enabled: !!officerId,
    });
};

export const useCustodyChain = (propertyId: string) => {
    return useQuery({
        queryKey: propertyKeys.custodyChain(propertyId),
        queryFn: async () => {
            if (!propertyId) {
                console.warn('useCustodyChain: No propertyId provided');
                return [];
            }
            return getCustodyChain(propertyId);
        },
        enabled: !!propertyId,
        retry: (failureCount, error) => {
            // Retry up to 3 times for network errors, but not for 404s
            if (failureCount >= 3) return false;
            if (error?.message?.includes('404') || error?.message?.includes('not found')) return false;
            return true;
        },
    });
};

// Mutations
export const useCreateProperty = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: createProperty,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: propertyKeys.lists() });
        },
    });
};

export const useUpdateProperty = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: updateProperty,
        onSuccess: (updatedProperty) => {
            queryClient.invalidateQueries({ queryKey: propertyKeys.detail(updatedProperty.id) });
            queryClient.invalidateQueries({ queryKey: propertyKeys.lists() });
        },
    });
};

export const useDeleteProperty = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: deleteProperty,
        onSuccess: (_, deletedId) => {
            queryClient.invalidateQueries({ queryKey: propertyKeys.lists() });
            queryClient.removeQueries({ queryKey: propertyKeys.detail(deletedId) });
        },
    });
};

export const useAddCustodyEvent = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: ({ propertyId, custodyEvent }: { propertyId: string; custodyEvent: CustodyEvent }) =>
            addCustodyEvent(propertyId, custodyEvent),
        onSuccess: (_, { propertyId }) => {
            queryClient.invalidateQueries({ queryKey: propertyKeys.detail(propertyId) });
            queryClient.invalidateQueries({ queryKey: propertyKeys.custodyChain(propertyId) });
        },
    });
};

export const useUpdatePropertyStatus = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: ({ propertyId, status }: { propertyId: string; status: PropertyStatus }) =>
            updatePropertyStatus(propertyId, status),
        onSuccess: (_, { propertyId }) => {
            queryClient.invalidateQueries({ queryKey: propertyKeys.detail(propertyId) });
            queryClient.invalidateQueries({ queryKey: propertyKeys.lists() });
        },
    });
};

// Property File Attachment Hooks
export const usePropertyFileAttachments = (params: {
    propertyId: string;
    fileCategory?: string;
    pageSize?: number;
    pageToken?: string;
}) => {
    return useQuery({
        queryKey: [...propertyKeys.all, 'fileAttachments', params.propertyId, params],
        queryFn: () => listPropertyFileAttachments(params),
        enabled: !!params.propertyId,
    });
};

export const useAddPropertyFileAttachment = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: ({ propertyId, fileAttachment }: { propertyId: string; fileAttachment: Omit<PropertyFileReference, 'id'> }) =>
            addPropertyFileAttachment(propertyId, fileAttachment),
        onSuccess: (_, { propertyId }) => {
            queryClient.invalidateQueries({ queryKey: [...propertyKeys.all, 'fileAttachments', propertyId] });
            queryClient.invalidateQueries({ queryKey: propertyKeys.detail(propertyId) });
        },
    });
};

export const useRemovePropertyFileAttachment = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: ({ propertyId, attachmentId }: { propertyId: string; attachmentId: string }) =>
            removePropertyFileAttachment(propertyId, attachmentId),
        onSuccess: (_, { propertyId }) => {
            queryClient.invalidateQueries({ queryKey: [...propertyKeys.all, 'fileAttachments', propertyId] });
            queryClient.invalidateQueries({ queryKey: propertyKeys.detail(propertyId) });
        },
    });
}; 