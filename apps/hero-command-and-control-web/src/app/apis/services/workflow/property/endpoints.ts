import workflowAxiosInstance from '../axiosInstance';
import { CustodyEvent, Property, PropertyFileReference, PropertyStatus, PropertyType, SearchPropertiesRequest, SearchPropertiesResponse } from './types';

// Create Property
export const createProperty = async (property: Omit<Property, 'id' | 'createTime' | 'updateTime' | 'version' | 'status' | 'resourceType'>): Promise<Property> => {
    const response = await workflowAxiosInstance.post('/hero.property.v1.PropertyService/CreateProperty', {
        property
    });
    return response.data.property;
};

// Get Property by ID
export const getProperty = async (id: string): Promise<Property> => {
    try {
        const response = await workflowAxiosInstance.post('/hero.property.v1.PropertyService/GetProperty', {
            id
        });

        // Ensure we have a valid response with property data
        if (!response.data || !response.data.property) {
            console.warn('getProperty: Invalid response structure', response.data);
            throw new Error('Invalid response structure from GetProperty');
        }

        return response.data.property;
    } catch (error) {
        console.error('getProperty: Error fetching property', error);
        throw error; // Re-throw for proper error handling in the hook
    }
};

// List Properties
export const listProperties = async (params: {
    pageSize?: number;
    pageToken?: string;
    propertyType?: PropertyType;
    propertyStatus?: PropertyStatus;
    orderBy?: string;
}): Promise<{ properties: Property[]; nextPageToken?: string }> => {
    const response = await workflowAxiosInstance.post('/hero.property.v1.PropertyService/ListProperties', params);
    return response.data;
};

// Update Property
export const updateProperty = async (property: Property): Promise<Property> => {
    const response = await workflowAxiosInstance.post('/hero.property.v1.PropertyService/UpdateProperty', {
        property
    });
    return response.data.property;
};

// Delete Property
export const deleteProperty = async (id: string): Promise<void> => {
    await workflowAxiosInstance.post('/hero.property.v1.PropertyService/DeleteProperty', {
        id
    });
};

// Search Properties
export const searchProperties = async (request: SearchPropertiesRequest): Promise<SearchPropertiesResponse> => {
    const response = await workflowAxiosInstance.post('/hero.property.v1.PropertyService/SearchProperties', request);
    return response.data;
};

// Batch Get Properties
export const batchGetProperties = async (ids: string[]): Promise<Property[]> => {
    try {
        const response = await workflowAxiosInstance.post('/hero.property.v1.PropertyService/BatchGetProperties', {
            ids
        });

        // Ensure we have a valid response with properties data
        if (!response.data || !response.data.properties) {
            console.warn('batchGetProperties: Invalid response structure', response.data);
            throw new Error('Invalid response structure from BatchGetProperties');
        }

        return response.data.properties;
    } catch (error) {
        console.error('batchGetProperties: Error fetching properties', error);
        throw error;
    }
};

// Add Custody Event
export const addCustodyEvent = async (propertyId: string, custodyEvent: CustodyEvent): Promise<void> => {
    await workflowAxiosInstance.post('/hero.property.v1.PropertyService/AddCustodyEvent', {
        propertyId,
        custodyEvent
    });
};

// Get Custody Chain
export const getCustodyChain = async (propertyId: string): Promise<CustodyEvent[]> => {
    try {
        const response = await workflowAxiosInstance.post('/hero.property.v1.PropertyService/GetCustodyChain', {
            propertyId
        });

        // Ensure we have a valid response with custodyChain data
        if (!response.data || !response.data.custodyChain) {
            console.warn('getCustodyChain: Invalid response structure', response.data);
            throw new Error('Invalid response structure from GetCustodyChain');
        }

        return response.data.custodyChain;
    } catch (error) {
        console.error('getCustodyChain: Error fetching custody chain', error);
        throw error;
    }
};

// Update Property Status
export const updatePropertyStatus = async (propertyId: string, status: PropertyStatus): Promise<void> => {
    await workflowAxiosInstance.post('/hero.property.v1.PropertyService/UpdatePropertyStatus', {
        propertyId,
        status
    });
};

// List Properties by Case Number
export const listPropertiesByCaseNumber = async (caseNumber: string): Promise<Property[]> => {
    const response = await workflowAxiosInstance.post('/hero.property.v1.PropertyService/ListPropertiesByCaseNumber', {
        caseNumber
    });
    return response.data.properties;
};

// List Properties by Officer
export const listPropertiesByOfficer = async (officerId: string): Promise<Property[]> => {
    const response = await workflowAxiosInstance.post('/hero.property.v1.PropertyService/ListPropertiesByOfficer', {
        officerId
    });
    return response.data.properties;
};

// List Property File Attachments
export const listPropertyFileAttachments = async (params: {
    propertyId: string;
    fileCategory?: string;
    pageSize?: number;
    pageToken?: string;
}): Promise<{ fileAttachments: PropertyFileReference[]; nextPageToken?: string }> => {
    const response = await workflowAxiosInstance.post('/hero.property.v1.PropertyService/ListPropertyFileAttachments', {
        propertyId: params.propertyId,
        fileCategory: params.fileCategory,
        pageSize: params.pageSize,
        pageToken: params.pageToken
    });
    return response.data;
};

// Add Property File Attachment
export const addPropertyFileAttachment = async (propertyId: string, fileAttachment: Omit<PropertyFileReference, 'id'>): Promise<PropertyFileReference> => {
    const response = await workflowAxiosInstance.post('/hero.property.v1.PropertyService/AddPropertyFileAttachment', {
        propertyId,
        fileAttachment
    });
    return response.data.fileAttachment;
};

// Remove Property File Attachment
export const removePropertyFileAttachment = async (propertyId: string, attachmentId: string): Promise<void> => {
    await workflowAxiosInstance.post('/hero.property.v1.PropertyService/RemovePropertyFileAttachment', {
        propertyId,
        attachmentId
    });
}; 