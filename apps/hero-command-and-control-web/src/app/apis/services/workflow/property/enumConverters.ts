import { CustodyActionType, PropertyDisposalType, PropertyStatus, PropertyType } from "./types";

// Function to convert a string (e.g. "PROPERTY_TYPE_FOUND")
// to the corresponding enum value (e.g. PropertyType.PROPERTY_TYPE_FOUND).
export function stringToPropertyType(value: string): PropertyType {
    // The enum keys are the full strings, so we can use the value directly
    if (value in PropertyType) {
        return PropertyType[value as keyof typeof PropertyType];
    }
    return PropertyType.PROPERTY_TYPE_UNSPECIFIED;
}

// Function to convert an enum value (e.g. PropertyType.PROPERTY_TYPE_FOUND)
// back to its string representation (e.g. "PROPERTY_TYPE_FOUND").
export function propertyTypeToString(propertyType: PropertyType): string {
    const key = PropertyType[propertyType];
    return key || "PROPERTY_TYPE_UNSPECIFIED";
}

// For crazy reason when you use our hooks to get Property it gives you not the right Enum 
// It gives you the string representation of the enum
// But type is still PropertyType
// You can just type cast it from PropertyType
export function hookPropertyTypeToString(propertyType: PropertyType): string {
    return String(propertyType);
}

// Function to convert a string (e.g. "PROPERTY_STATUS_COLLECTED")
// to the corresponding enum value (e.g. PropertyStatus.PROPERTY_STATUS_COLLECTED).
export function stringToPropertyStatus(value: string): PropertyStatus {
    // The enum keys are the full strings, so we can use the value directly
    if (value in PropertyStatus) {
        return PropertyStatus[value as keyof typeof PropertyStatus];
    }
    return PropertyStatus.PROPERTY_STATUS_UNSPECIFIED;
}

// Function to convert an enum value (e.g. PropertyStatus.PROPERTY_STATUS_COLLECTED)
// back to its string representation (e.g. "PROPERTY_STATUS_COLLECTED").
export function propertyStatusToString(propertyStatus: PropertyStatus): string {
    const key = PropertyStatus[propertyStatus];
    return key || "PROPERTY_STATUS_UNSPECIFIED";
}

// For crazy reason when you use our hooks to get Property it gives you not the right Enum 
// It gives you the string representation of the enum
// But type is still PropertyStatus
// You can just type cast it from PropertyStatus
export function hookPropertyStatusToString(propertyStatus: PropertyStatus): string {
    return String(propertyStatus);
}

// Function to convert a string (e.g. "PROPERTY_DISPOSAL_TYPE_RELEASED")
// to the corresponding enum value (e.g. PropertyDisposalType.PROPERTY_DISPOSAL_TYPE_RELEASED).
export function stringToPropertyDisposalType(value: string): PropertyDisposalType {
    // The enum keys are the full strings, so we can use the value directly
    if (value in PropertyDisposalType) {
        return PropertyDisposalType[value as keyof typeof PropertyDisposalType];
    }
    return PropertyDisposalType.PROPERTY_DISPOSAL_TYPE_UNSPECIFIED;
}

// Function to convert an enum value (e.g. PropertyDisposalType.PROPERTY_DISPOSAL_TYPE_RELEASED)
// back to its string representation (e.g. "PROPERTY_DISPOSAL_TYPE_RELEASED").
export function propertyDisposalTypeToString(disposalType: PropertyDisposalType): string {
    const key = PropertyDisposalType[disposalType];
    return key || "PROPERTY_DISPOSAL_TYPE_UNSPECIFIED";
}

// For crazy reason when you use our hooks to get Property it gives you not the right Enum 
// It gives you the string representation of the enum
// But type is still PropertyDisposalType
// You can just type cast it from PropertyDisposalType
export function hookPropertyDisposalTypeToString(disposalType: PropertyDisposalType): string {
    return String(disposalType);
}

// Function to convert a string (e.g. "CUSTODY_ACTION_TYPE_COLLECTED")
// to the corresponding enum value (e.g. CustodyActionType.CUSTODY_ACTION_TYPE_COLLECTED).
export function stringToCustodyActionType(value: string): CustodyActionType {
    // The enum keys are the full strings, so we can use the value directly
    if (value in CustodyActionType) {
        return CustodyActionType[value as keyof typeof CustodyActionType];
    }
    return CustodyActionType.CUSTODY_ACTION_TYPE_UNSPECIFIED;
}

// Function to convert an enum value (e.g. CustodyActionType.CUSTODY_ACTION_TYPE_COLLECTED)
// back to its string representation (e.g. "CUSTODY_ACTION_TYPE_COLLECTED").
export function custodyActionTypeToString(actionType: CustodyActionType): string {
    const key = CustodyActionType[actionType];
    return key || "CUSTODY_ACTION_TYPE_UNSPECIFIED";
}

// For crazy reason when you use our hooks to get Property it gives you not the right Enum 
// It gives you the string representation of the enum
// But type is still CustodyActionType
// You can just type cast it from CustodyActionType
export function hookCustodyActionTypeToString(actionType: CustodyActionType): string {
    return String(actionType);
} 