import {
  useQuery,
  UseQueryOptions
} from "@tanstack/react-query";

import {
  ListCallsRequest,
  ListCallsResponse,
} from "proto/hero/communications/v1/conversation_pb";

import {
  listCalls
} from "./endpoints";

const CELLULAR_CALL_QUERY_KEY = "cellular_call";

/**
 * Hook to list calls with pagination and filtering.
 * @param params - ListCallsRequest with pagination and filter parameters.
 * @param options - Additional useQuery options.
 * @returns Query result with paginated call records.
 */
export function useListCalls(
  params: ListCallsRequest,
  options?: Omit<
    UseQueryOptions<ListCallsResponse, Error, ListCallsResponse>,
    "queryKey" | "queryFn"
  >
) {
  return useQuery({
    queryKey: [CELLULAR_CALL_QUERY_KEY, "listCalls", params],
    queryFn: () => listCalls(params),
    retry: 2,
    ...options,
  });
}