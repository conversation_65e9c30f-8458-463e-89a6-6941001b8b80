import { Button } from "@/design-system/components/Button";
import { Typography } from "@/design-system/components/Typography";
import { colors } from "@/design-system/tokens";
import AddIcon from "@mui/icons-material/Add";
import DownloadIcon from "@mui/icons-material/Download";
import DeleteIcon from "@mui/icons-material/Delete";
import CloseIcon from "@mui/icons-material/Close";
import { Box, Dialog, DialogActions, DialogContent, DialogTitle, IconButton } from "@mui/material";
import React, { useMemo, useState } from "react";
import {
  FaFile,
  FaFileAlt,
  FaFileAudio,
  FaFileImage,
  FaFilePdf,
  FaFileVideo,
  FaFileCsv,
} from "react-icons/fa";
import { useGetPresignedDownloadUrl, useDeleteFile } from "../../../apis/services/filerepository/hooks";
import { useAddAdditionalInfo } from "../../../apis/services/workflow/cases/hooks";
import { create } from "@bufbuild/protobuf";
import { AddAdditionalInfoRequestSchema } from "proto/hero/cases/v1/cases_pb";

interface CaseMediaFile {
  id: string;
  fileId: string;
  fileName: string;
  displayName: string;
  fileType?: string;
  fileSize?: number;
  caption?: string;
  source: 'timeline' | 'case';
  sourceId?: string;
  sourceText?: string;
  createdAt?: string;
  metadata?: {
    fileType?: string;
    originalFilename?: string;
    [key: string]: any;
  };
}

interface CaseMediaSectionProps {
  caseId: string;
  timelineEntries?: any[];
  caseData?: any;
  readOnly?: boolean;
  onAddMedia?: () => void;
}

const CaseMediaSection: React.FC<CaseMediaSectionProps> = ({
  caseId,
  timelineEntries = [],
  caseData,
  readOnly = false,
  onAddMedia,
}) => {
  const [downloadingFileId, setDownloadingFileId] = useState<string | null>(null);
  const [deletingFileId, setDeletingFileId] = useState<string | null>(null);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [fileToDelete, setFileToDelete] = useState<CaseMediaFile | null>(null);
  const [previewModalOpen, setPreviewModalOpen] = useState(false);
  const [previewFile, setPreviewFile] = useState<CaseMediaFile | null>(null);
  const [blobCache, setBlobCache] = useState<Map<string, string>>(new Map());
  const [previewLoading, setPreviewLoading] = useState<Set<string>>(new Set());
  const [csvData, setCsvData] = useState<string[][] | null>(null);

  // Cleanup blob URLs on unmount to prevent memory leaks
  React.useEffect(() => {
    return () => {
      blobCache.forEach((blobUrl) => {
        URL.revokeObjectURL(blobUrl);
      });
    };
  }, []);

  const getDownloadUrlMutation = useGetPresignedDownloadUrl({
    onSuccess: () => console.log("Download URL generated successfully"),
    onError: (error) => console.error("Error generating download URL:", error),
  });

  const deleteFileMutation = useDeleteFile({
    onSuccess: () => console.log("File deleted successfully"),
    onError: (error) => console.error("Error deleting file:", error),
  });

  const addAdditionalInfo = useAddAdditionalInfo();

  const mediaFiles = useMemo((): CaseMediaFile[] => {
    const allFiles: CaseMediaFile[] = [];

    // Add timeline media files
    timelineEntries.forEach((entry) => {
      if (entry.fileAttachments && entry.fileAttachments.length > 0) {
        entry.fileAttachments.forEach((attachment: any) => {
          allFiles.push({
            id: `timeline-${attachment.id || attachment.fileId}`,
            fileId: attachment.fileId || attachment.id,
            fileName: attachment.fileName || attachment.displayName || 'Untitled',
            displayName: attachment.displayName || attachment.fileName || 'Untitled',
            fileType: attachment.fileType || attachment.metadata?.fileType,
            fileSize: attachment.fileSize || attachment.metadata?.fileSize,
            caption: attachment.caption || attachment.description,
            source: 'timeline',
            sourceId: entry.id,
            sourceText: entry.text || entry.description || 'Timeline Entry',
            createdAt: entry.createdAt || attachment.createdAt,
            metadata: attachment.metadata || {},
          });
        });
      }
    });

    const additionalMediaFiles = caseData?.additionalInfoJson?.mediaFiles || [];
    additionalMediaFiles.forEach((file: any) => {
      allFiles.push({
        id: `case-additional-${file.id || file.fileId}`,
        fileId: file.fileId || file.id,
        fileName: file.fileName || file.displayName || 'Untitled',
        displayName: file.displayName || file.fileName || 'Untitled',
        fileType: file.fileType || file.metadata?.fileType,
        fileSize: file.fileSize || file.metadata?.fileSize,
        caption: file.caption || file.description,
        source: 'case',
        sourceId: file.id,
        sourceText: 'Case Media',
        createdAt: file.createdAt,
        metadata: file.metadata || {},
      });
    });

    return allFiles.sort((a, b) => {
      const dateA = a.createdAt ? new Date(a.createdAt).getTime() : 0;
      const dateB = b.createdAt ? new Date(b.createdAt).getTime() : 0;
      return dateB - dateA;
    });
  }, [timelineEntries, caseData]);

  const getFileIcon = (fileType?: string) => {
    const type = fileType?.toLowerCase() || '';

    if (type.startsWith('image/')) {
      return <FaFileImage style={{ fontSize: 20, color: colors.blue[600] }} />;
    }
    if (type.startsWith('video/')) {
      return <FaFileVideo style={{ fontSize: 20, color: colors.blue[600] }} />;
    }
    if (type.startsWith('audio/')) {
      return <FaFileAudio style={{ fontSize: 20, color: colors.blue[600] }} />;
    }
    if (type === 'application/pdf') {
      return <FaFilePdf style={{ fontSize: 20, color: colors.blue[600] }} />;
    }
    if (type === 'text/csv' || type === 'application/vnd.ms-excel') {
      return <FaFileCsv style={{ fontSize: 20, color: colors.blue[600] }} />;
    }
    if (type.includes('text/') || type.includes('document') || type.includes('word')) {
      return <FaFileAlt style={{ fontSize: 20, color: colors.blue[600] }} />;
    }

    return <FaFile style={{ fontSize: 20, color: colors.grey[500] }} />;
  };

  const formatFileSize = (bytes?: number): string => {
    if (!bytes || bytes === 0) return '';
    
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${Math.round(bytes / Math.pow(1024, i) * 100) / 100} ${sizes[i]}`;
  };

  const isPreviewableFile = (fileType?: string): boolean => {
    const type = fileType?.toLowerCase() || '';
    return type.startsWith('image/') || type.startsWith('video/') || type === 'application/pdf' || type === 'text/csv' || type === 'application/vnd.ms-excel';
  };

  /**
   * Parse CSV text with proper handling of quoted values containing commas
   * 
   * @param text - Raw CSV text content
   * @returns Array of rows, where each row is an array of cell values
   */
  const parseCSV = (text: string): string[][] => {
    const rows: string[][] = [];
    const lines = text.split('\n');
    
    for (const line of lines) {
      const trimmedLine = line.trim();
      if (!trimmedLine) continue;
      
      const row: string[] = [];
      let currentCell = '';
      let insideQuotes = false;
      let i = 0;
      
      while (i < trimmedLine.length) {
        const char = trimmedLine[i];
        
        if (char === '"') {
          // Handle escaped quotes (double quotes)
          if (i + 1 < trimmedLine.length && trimmedLine[i + 1] === '"') {
            currentCell += '"';
            i += 2;
          } else {
            // Toggle quote state
            insideQuotes = !insideQuotes;
            i++;
          }
        } else if (char === ',' && !insideQuotes) {
          // End of cell
          row.push(currentCell.trim());
          currentCell = '';
          i++;
        } else {
          // Regular character
          currentCell += char;
          i++;
        }
      }
      
      // Add the last cell
      row.push(currentCell.trim());
      
      // Only add rows that have content
      if (row.some(cell => cell !== '')) {
        rows.push(row);
      }
    }
    
    return rows;
  };

  const FilePreview: React.FC<{
    file: CaseMediaFile;
    blobCache: Map<string, string>;
    setBlobCache: React.Dispatch<React.SetStateAction<Map<string, string>>>;
    previewLoading: Set<string>;
    setPreviewLoading: React.Dispatch<React.SetStateAction<Set<string>>>;
    getDownloadUrlMutation: any;
    onPreview: (file: CaseMediaFile) => void;
  }> = ({ file, blobCache, setBlobCache, previewLoading, setPreviewLoading, getDownloadUrlMutation, onPreview }) => {
    const fileType = file.fileType?.toLowerCase() || '';
    const isImage = fileType.startsWith('image/');
    const isVideo = fileType.startsWith('video/');
    const isPdf = fileType === 'application/pdf';
    const isCsv = fileType === 'text/csv' || fileType === 'application/vnd.ms-excel';
    const isLoading = previewLoading.has(file.fileId);
    const blobUrl = blobCache.get(file.fileId);
    
    const loadImagePreview = async () => {
      if (!isImage || blobUrl || isLoading) return;
      
      try {
        setPreviewLoading(prev => new Set([...prev, file.fileId]));
        
        const response = await getDownloadUrlMutation.mutateAsync({
          id: file.fileId,
          expiresIn: 300
        } as any);
        
        const fileResponse = await fetch(response.presignedUrl);
        const blob = await fileResponse.blob();
        const url = URL.createObjectURL(blob);
        
        setBlobCache(prev => new Map(prev).set(file.fileId, url));
      } catch (error) {
        console.error('Failed to load image preview:', error);
      } finally {
        setPreviewLoading(prev => {
          const newSet = new Set(prev);
          newSet.delete(file.fileId);
          return newSet;
        });
      }
    };
    
    // Only load image previews automatically
    React.useEffect(() => {
      if (isImage) {
        loadImagePreview();
      }
    }, [file.fileId, isImage]);
    
    const handleClick = () => {
      onPreview(file);
    };
    
    return (
      <Box
        onClick={handleClick}
        sx={{
          width: 80,
          height: 60,
          borderRadius: "8px",
          border: `1px solid ${colors.grey[200]}`,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          backgroundColor: colors.grey[50],
          cursor: 'pointer',
          overflow: 'hidden',
          position: 'relative',
          '&:hover': {
            borderColor: colors.blue[300],
            backgroundColor: colors.blue[50],
          },
        }}
      >
        {isLoading && isImage ? (
          <Box
            sx={{
              width: 16,
              height: 16,
              border: `2px solid ${colors.blue[200]}`,
              borderTop: `2px solid ${colors.blue[600]}`,
              borderRadius: '50%',
              animation: 'spin 1s linear infinite',
              '@keyframes spin': {
                '0%': { transform: 'rotate(0deg)' },
                '100%': { transform: 'rotate(360deg)' },
              },
            }}
          />
        ) : blobUrl && isImage ? (
          <img
            src={blobUrl}
            alt={file.displayName}
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'cover',
            }}
            onError={() => console.error('Failed to load image thumbnail')}
          />
        ) : isLoading && (isVideo || isPdf || isCsv) ? (
          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 1 }}>
            <Box
              sx={{
                width: 16,
                height: 16,
                border: `2px solid ${colors.blue[200]}`,
                borderTop: `2px solid ${colors.blue[600]}`,
                borderRadius: '50%',
                animation: 'spin 1s linear infinite',
                '@keyframes spin': {
                  '0%': { transform: 'rotate(0deg)' },
                  '100%': { transform: 'rotate(360deg)' },
                },
              }}
            />
            {isVideo ? (
              <FaFileVideo style={{ fontSize: 16, color: colors.blue[600] }} />
            ) : isCsv ? (
              <FaFileCsv style={{ fontSize: 16, color: colors.blue[600] }} />
            ) : (
              <FaFilePdf style={{ fontSize: 16, color: colors.blue[600] }} />
            )}
          </Box>
        ) : isVideo ? (
          <FaFileVideo style={{ fontSize: 24, color: colors.blue[600] }} />
        ) : isPdf ? (
          <FaFilePdf style={{ fontSize: 24, color: colors.blue[600] }} />
        ) : isCsv ? (
          <FaFileCsv style={{ fontSize: 24, color: colors.blue[600] }} />
        ) : (
          getFileIcon(file.fileType)
        )}
      </Box>
    );
  };

  const handleDownloadFile = async (fileId: string, fileName: string) => {
    try {
      setDownloadingFileId(fileId);
      console.log('Downloading file:', fileId, fileName);

      const response = await getDownloadUrlMutation.mutateAsync({
        id: fileId,
        expiresIn: 300 // 5 minutes
      } as any);

      const fileResponse = await fetch(response.presignedUrl);
      const blob = await fileResponse.blob();

      // Create a blob URL and trigger download
      const blobUrl = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = blobUrl;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      URL.revokeObjectURL(blobUrl);

      console.log('File download initiated successfully');
    } catch (error) {
      console.error('Download failed:', error);
      alert('Failed to download file. Please try again.');
    } finally {
      setDownloadingFileId(null);
    }
  };

  const handlePreviewFile = async (file: CaseMediaFile) => {
    if (!isPreviewableFile(file.fileType)) {
      // For non-previewable files, just download
      return handleDownloadFile(file.fileId, file.displayName);
    }
    
    const fileType = file.fileType?.toLowerCase() || '';
    const isCsv = fileType === 'text/csv' || fileType === 'application/vnd.ms-excel';
    
    // Check if we already have the blob cached
    const cachedBlob = blobCache.get(file.fileId);
    if (cachedBlob && !isCsv) {
      setPreviewFile(file);
      setPreviewModalOpen(true);
      return;
    }
    
    // Check if we have CSV data cached
    if (isCsv && csvData && previewFile?.fileId === file.fileId) {
      setPreviewFile(file);
      setPreviewModalOpen(true);
      return;
    }
    
    // Download and cache the file for preview (lazy loading)
    try {
      setPreviewLoading(prev => new Set([...prev, file.fileId]));
      
      const response = await getDownloadUrlMutation.mutateAsync({
        id: file.fileId,
        expiresIn: 300
      } as any);
      
      const fileResponse = await fetch(response.presignedUrl);
      const blob = await fileResponse.blob();
      
      if (isCsv) {
        // Parse CSV data with proper handling of quoted values
        const text = await blob.text();
        const rows = parseCSV(text);
        setCsvData(rows);
      } else {
        // Store blob URL for other file types
        const blobUrl = URL.createObjectURL(blob);
        setBlobCache(prev => new Map(prev).set(file.fileId, blobUrl));
      }
      
      setPreviewFile(file);
      setPreviewModalOpen(true);
    } catch (error) {
      console.error('Failed to open file preview:', error);
      alert('Failed to open file preview. Please try again.');
    } finally {
      setPreviewLoading(prev => {
        const newSet = new Set(prev);
        newSet.delete(file.fileId);
        return newSet;
      });
    }
  };

  const handleClosePreview = () => {
    setPreviewModalOpen(false);
    setPreviewFile(null);
    setCsvData(null);
  };

  const handleDeleteFile = async (file: CaseMediaFile) => {
    setFileToDelete(file);
    setDeleteModalOpen(true);
  };

  const confirmDeleteFile = async () => {
    if (!fileToDelete || !caseData) return;

    try {
      setDeletingFileId(fileToDelete.id);
      
      await deleteFileMutation.mutateAsync({ id: fileToDelete.fileId } as any);
      
      if (fileToDelete.source === 'case') {
        const currentAdditionalInfo = caseData.additionalInfoJson || {};
        const existingMediaFiles = currentAdditionalInfo.mediaFiles || [];
        
        const updatedMediaFiles = existingMediaFiles.filter(
          (mediaFile: any) => mediaFile.fileId !== fileToDelete.fileId
        );
        
        const updatedAdditionalInfo = {
          ...currentAdditionalInfo,
          mediaFiles: updatedMediaFiles
        };
        
        const request = create(AddAdditionalInfoRequestSchema, {
          caseId,
          additionalInfoJson: JSON.stringify(updatedAdditionalInfo),
        });
        
        await addAdditionalInfo.mutateAsync(request);
      }
      
      console.log('File deleted successfully');
      setDeleteModalOpen(false);
      setFileToDelete(null);
    } catch (error) {
      console.error('Error deleting file:', error);
      alert('Failed to delete file. Please try again.');
    } finally {
      setDeletingFileId(null);
    }
  };

  const cancelDeleteFile = () => {
    setDeleteModalOpen(false);
    setFileToDelete(null);
  };

  return (
    <Box
      sx={{
        display: "flex",
        width: "100%",
        flexDirection: "column",
        alignItems: "flex-start",
        gap: "24px",
        borderRadius: "12px",
        border: `1px solid ${colors.grey[200]}`,
        background: "#FFF",
        overflow: "hidden",
        boxShadow: "0px 0px 16px 0px rgba(0, 0, 0, 0.04)",
      }}
    >
      <Box
        sx={{
          display: "flex",
          width: "100%",
          justifyContent: "space-between",
          alignItems: "center",
          padding: "24px",
          paddingBottom: mediaFiles?.length > 0 ? "0px" : "24px",
        }}
      >
        <Typography style="caps1" color={colors.grey[500]}>
          Media
        </Typography>

        {!readOnly && onAddMedia && (
          <Button
            label="Add Media"
            leftIcon={<AddIcon />}
            color="blue"
            size="small"
            prominence={false}
            onClick={onAddMedia}
          />
        )}
      </Box>

      {mediaFiles?.length > 0 && (
        <Box sx={{ width: "100%", px: 3, pb: 3 }}>
          <Box
            sx={{
              display: "grid",
              gridTemplateColumns: "100px 1fr 120px 80px",
              gap: 2,
              py: 1,
              borderBottom: `1px solid ${colors.grey[200]}`,
              mb: 1,
            }}
          >
            <Typography style="body3" color={colors.grey[500]}>
              Preview
            </Typography>
            <Typography style="body3" color={colors.grey[500]}>
              File Name
            </Typography>
            <Typography style="body3" color={colors.grey[500]}>
              Size
            </Typography>
            <Typography style="body3" color={colors.grey[500]}>
              Actions
            </Typography>
          </Box>

          {mediaFiles.map((file, index) => (
            <Box
              key={file.id}
              sx={{
                display: "grid",
                gridTemplateColumns: "100px 1fr 120px 80px",
                gap: 2,
                py: 2,
                alignItems: "center",
                borderBottom: index < mediaFiles.length - 1 ? `1px solid ${colors.grey[100]}` : 'none',
              }}
            >
              {/* File Preview Column */}
              <FilePreview 
                file={file}
                blobCache={blobCache}
                setBlobCache={setBlobCache}
                previewLoading={previewLoading}
                setPreviewLoading={setPreviewLoading}
                getDownloadUrlMutation={getDownloadUrlMutation}
                onPreview={handlePreviewFile}
              />

              {/* File Name Column - Clickable to open file */}
              <Box
                onClick={() => handlePreviewFile(file)}
                sx={{
                  cursor: downloadingFileId === file.fileId ? 'wait' : 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1,
                  '&:hover': {
                    textDecoration: 'underline',
                  },
                }}
              >
                {downloadingFileId === file.fileId && (
                  <Box
                    sx={{
                      width: 12,
                      height: 12,
                      border: `2px solid ${colors.blue[200]}`,
                      borderTop: `2px solid ${colors.blue[600]}`,
                      borderRadius: '50%',
                      animation: 'spin 1s linear infinite',
                      '@keyframes spin': {
                        '0%': { transform: 'rotate(0deg)' },
                        '100%': { transform: 'rotate(360deg)' },
                      },
                    }}
                  />
                )}
                <Box sx={{ overflow: 'hidden' }}>
                  <Box sx={{ 
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap'
                  }}>
                    <Typography
                      style="body2"
                      color={downloadingFileId === file.fileId ? colors.blue[300] : colors.blue[600]}
                      lineHeight={"20px"}
                    >
                      {file.displayName}
                    </Typography>
                  </Box>
                  {file.caption && (
                    <Box sx={{ 
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap'
                    }}>
                      <Typography 
                        style="tag2" 
                        color={colors.grey[500]}
                      >
                        {file.caption}
                      </Typography>
                    </Box>
                  )}
                </Box>
              </Box>

              {/* File Size Column */}
              <Typography style="tag2" color={colors.grey[500]}>
                {formatFileSize(file.fileSize)}
              </Typography>

              {/* Actions Column */}
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                {/* Download Button */}
                <IconButton
                  onClick={() => handleDownloadFile(file.fileId, file.displayName)}
                  size="small"
                  disabled={downloadingFileId === file.fileId}
                  sx={{
                    color: downloadingFileId === file.fileId ? colors.grey[300] : colors.blue[600],
                    "&:hover": {
                      color: downloadingFileId === file.fileId ? colors.grey[300] : colors.blue[700],
                      backgroundColor: downloadingFileId === file.fileId ? 'transparent' : colors.blue[50],
                    },
                  }}
                  aria-label={`Download ${file.displayName}`}
                >
                  <DownloadIcon sx={{ fontSize: 16 }} />
                </IconButton>

                {/* Delete Button - Only for case media and when not read-only */}
                {!readOnly && file.source === 'case' && (
                  <IconButton
                    onClick={() => handleDeleteFile(file)}
                    size="small"
                    disabled={deletingFileId === file.id}
                    sx={{
                      color: deletingFileId === file.id ? colors.grey[300] : colors.grey[400],
                      "&:hover": {
                        color: deletingFileId === file.id ? colors.grey[300] : colors.grey[600],
                        backgroundColor: deletingFileId === file.id ? 'transparent' : colors.grey[100],
                      },
                    }}
                    aria-label={`Delete ${file.displayName}`}
                  >
                    {deletingFileId === file.id ? (
                      <Box
                        sx={{
                          width: 16,
                          height: 16,
                          border: `2px solid ${colors.grey[300]}`,
                          borderTop: `2px solid ${colors.grey[600]}`,
                          borderRadius: '50%',
                          animation: 'spin 1s linear infinite',
                        }}
                      />
                    ) : (
                      <DeleteIcon sx={{ fontSize: 16 }} />
                    )}
                  </IconButton>
                )}
              </Box>
            </Box>
          ))}
        </Box>
      )}

      {/* Delete Confirmation Modal */}
      <Dialog
        open={deleteModalOpen}
        onClose={cancelDeleteFile}
        maxWidth="sm"
        fullWidth
        slotProps={{
          paper: {
            sx: {
              borderRadius: 2,
            }
          }
        }}
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Typography style="h3" color={colors.grey[900]}>
              Delete Media File
            </Typography>
            <IconButton
              onClick={cancelDeleteFile}
              sx={{ color: colors.grey[500] }}
            >
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        
        <DialogContent>
          <Typography style="body1" color={colors.grey[700]}>
            Are you sure you want to delete &ldquo;{fileToDelete?.displayName}&rdquo;? This action cannot be undone.
          </Typography>
        </DialogContent>
        
        <DialogActions sx={{ px: 3, pb: 3, gap: 2 }}>
          <Button
            label="Cancel"
            color="grey"
            prominence={false}
            onClick={cancelDeleteFile}
          />
          <Button
            label="Delete"
            color="rose"
            prominence={true}
            onClick={confirmDeleteFile}
            disabled={deletingFileId === fileToDelete?.id}
          />
        </DialogActions>
      </Dialog>

      {/* Preview Modal */}
      <Dialog
        open={previewModalOpen}
        onClose={handleClosePreview}
        maxWidth="lg"
        fullWidth
        slotProps={{
          paper: {
            sx: {
              borderRadius: 2,
              maxHeight: '90vh',
            }
          }
        }}
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Typography style="h3" color={colors.grey[900]}>
              {previewFile?.displayName}
            </Typography>
            <IconButton
              onClick={handleClosePreview}
              sx={{ color: colors.grey[500] }}
            >
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        
        <DialogContent sx={{ p: 0, display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 400 }}>
          {previewFile && (
            <Box sx={{ width: '100%', height: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
              {previewLoading.has(previewFile.fileId) ? (
                <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 2 }}>
                  <Box
                    sx={{
                      width: 32,
                      height: 32,
                      border: `3px solid ${colors.blue[200]}`,
                      borderTop: `3px solid ${colors.blue[600]}`,
                      borderRadius: '50%',
                      animation: 'spin 1s linear infinite',
                      '@keyframes spin': {
                        '0%': { transform: 'rotate(0deg)' },
                        '100%': { transform: 'rotate(360deg)' },
                      },
                    }}
                  />
                  <Typography style="body2" color={colors.grey[600]}>
                    Loading preview...
                  </Typography>
                </Box>
              ) : (
                <>
                  {previewFile.fileType?.startsWith('image/') && blobCache.get(previewFile.fileId) && (
                    <img
                      src={blobCache.get(previewFile.fileId)}
                      alt={previewFile.displayName}
                      style={{
                        maxWidth: '100%',
                        maxHeight: '70vh',
                        objectFit: 'contain',
                      }}
                    />
                  )}
                  {previewFile.fileType?.startsWith('video/') && blobCache.get(previewFile.fileId) && (
                    <video
                      src={blobCache.get(previewFile.fileId)}
                      controls
                      style={{
                        maxWidth: '100%',
                        maxHeight: '70vh',
                      }}
                    />
                  )}
                  {previewFile.fileType === 'application/pdf' && blobCache.get(previewFile.fileId) && (
                    <iframe
                      src={blobCache.get(previewFile.fileId)}
                      style={{
                        width: '100%',
                        height: '70vh',
                        border: 'none',
                      }}
                      title={previewFile.displayName}
                    />
                  )}
                  {(previewFile.fileType === 'text/csv' || previewFile.fileType === 'application/vnd.ms-excel') && csvData && (
                    <Box sx={{ 
                      width: '100%', 
                      height: '70vh', 
                      overflow: 'auto',
                      p: 2,
                    }}>
                      <table style={{ 
                        width: '100%', 
                        borderCollapse: 'collapse',
                        fontSize: '14px',
                      }}>
                        <thead>
                          <tr>
                            {csvData[0]?.map((header, index) => (
                              <th key={index} style={{
                                padding: '8px 12px',
                                textAlign: 'left',
                                borderBottom: `2px solid ${colors.grey[300]}`,
                                backgroundColor: colors.grey[50],
                                fontWeight: 600,
                                position: 'sticky',
                                top: 0,
                                zIndex: 1,
                              }}>
                                {header}
                              </th>
                            ))}
                          </tr>
                        </thead>
                        <tbody>
                          {csvData.slice(1).map((row, rowIndex) => (
                            <tr key={rowIndex}>
                              {row.map((cell, cellIndex) => (
                                <td key={cellIndex} style={{
                                  padding: '8px 12px',
                                  borderBottom: `1px solid ${colors.grey[200]}`,
                                }}>
                                  {cell}
                                </td>
                              ))}
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </Box>
                  )}
                </>
              )}
            </Box>
          )}
        </DialogContent>
        
        <DialogActions sx={{ px: 3, pb: 3, gap: 2 }}>
          <Button
            label="Download"
            leftIcon={<DownloadIcon />}
            color="blue"
            prominence={false}
            onClick={() => previewFile && handleDownloadFile(previewFile.fileId, previewFile.displayName)}
          />
          <Button
            label="Close"
            color="grey"
            prominence={true}
            onClick={handleClosePreview}
          />
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default CaseMediaSection;