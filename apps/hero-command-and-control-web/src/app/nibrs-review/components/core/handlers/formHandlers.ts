import { QueryClient } from "@tanstack/react-query";
import { Asset } from "proto/hero/assets/v2/assets_pb";
import { EntityType } from "proto/hero/entity/v1/entity_pb";
import { createPropertyFromValues } from "../../../../reports/ReportComponents/core/utils/propertyUtils";
import { FORM_RESET_TIMEOUT_MS } from "../constants";
import { PanelType, SaveStatus } from "../types";
import { createEntityFromValues } from "../utils/schemaUtils";

interface FormHandlersProps {
  // State Management Functions
  /** Sets the loading state for primary save operations */
  setIsSaveLoading: (loading: boolean) => void;

  /** Sets the loading state for entity update operations */
  setIsUpdateLoading: (loading: boolean) => void;

  /** Sets the loading state for save-and-add-another operations */
  setIsSaveAndAddAnotherLoading: (loading: boolean) => void;

  /** Sets notification messages for user feedback */
  setNotification: (notification: any) => void;

  /** Controls which panel type is currently active */
  setActivePanelType: React.Dispatch<React.SetStateAction<PanelType | null>>;

  // UI References
  /** Reference to panel content for scroll management */
  panelContentRef: React.MutableRefObject<{ scrollToTop: () => void } | null>;

  // Current State Data
  /** Currently editing entity data */
  editingEntity: any;

  /** ID of the entity being edited (null for new entities) */
  editingEntityId: string | null;

  /** Currently active panel type */
  activePanelType: PanelType | null;

  // Schema Configuration
  /** Person entity schemas for validation and creation */
  personSchemas: any;

  /** Vehicle entity schemas for validation and creation */
  vehicleSchemas: any;

  /** Property entity schemas for validation and creation */
  propertySchemas: any;

  /** Organization entity schemas for validation and creation */
  organizationSchemas: any;

  // User Context
  /** Current dispatcher/user asset for setting createdBy fields */
  dispatcherAsset?: Asset;

  // API Mutations
  /** Mutation for creating new entities */
  createEntityMutation: any;

  /** Optional mutation for creating properties via property backend */
  createPropertyMutation?: any;

  /** Mutation for updating existing entities */
  updateEntityMutation: any;

  /** Mutation for updating report sections (used for media) */
  updateReportSectionMutation: any;

  // Report Context
  /** ID of the current report */
  reportId: string | null;

  // External Dependencies
  /** React Query client for data fetching and cache management */
  queryClient: QueryClient;

  // Event Handlers
  /** Function to close the side panel */
  handleCloseSidePanel: (force?: boolean) => void;
}

/**
 * Helper function to handle property creation logic shared between
 * handleFormSubmit and handleSaveAndAddAnother functions
 */
const createPropertyEntity = ({
  values,
  dispatcherAsset,
  createPropertyMutation,
  createEntityMutation,
  propertySchemas,
  onSuccess,
  onError,
}: {
  values: any;
  dispatcherAsset?: Asset;
  createPropertyMutation?: any;
  createEntityMutation: any;
  propertySchemas: any;
  onSuccess: () => void;
  onError: (error: any) => void;
}) => {
  // Use property backend if available, otherwise fall back to entity backend
  if (createPropertyMutation) {
    try {
      const propertyData = createPropertyFromValues(values, dispatcherAsset);

      createPropertyMutation.mutate(propertyData, {
        onSuccess,
        onError,
      });
    } catch (error) {
      console.error("Error in property creation:", error);
      onError(new Error("Error preparing property data"));
    }
  } else {
    // Fallback to entity backend
    const schemaId = propertySchemas?.schemas?.[0]?.id || "";
    const schemaVersion = propertySchemas?.schemas?.[0]?.version || 1;
    createEntityMutation.mutate(
      createEntityFromValues(
        values,
        EntityType.PROPERTY,
        schemaId,
        schemaVersion
      ),
      {
        onSuccess,
        onError,
      }
    );
  }
};

export const createFormHandlers = ({
  setIsSaveLoading,
  setIsUpdateLoading,
  setIsSaveAndAddAnotherLoading,
  setNotification,
  setActivePanelType,
  panelContentRef,
  editingEntity,
  editingEntityId,
  activePanelType,
  personSchemas,
  vehicleSchemas,
  propertySchemas,
  organizationSchemas,
  dispatcherAsset,
  createEntityMutation,
  createPropertyMutation,
  updateEntityMutation,
  updateReportSectionMutation,
  reportId,
  queryClient,
  handleCloseSidePanel,
}: FormHandlersProps) => {
  const handleFormSubmit = async (values: any, additionalVictimData?: any) => {
    setIsSaveLoading(true);

    // Person Entity Submission
    if (activePanelType === PanelType.PERSON) {
      const schemaId = personSchemas?.schemas?.[0]?.id || "";
      const schemaVersion = personSchemas?.schemas?.[0]?.version || 1;
      console.log("Submitting person entity creation");
      createEntityMutation.mutate(
        createEntityFromValues(
          values,
          EntityType.PERSON,
          schemaId,
          schemaVersion
        ),
        {
          onSuccess: () => {
            handleCloseSidePanel();
          },
          additionalVictimData: additionalVictimData,
        }
      );
    }
    // Vehicle Entity Submission
    else if (activePanelType === PanelType.VEHICLE) {
      const schemaId = vehicleSchemas?.schemas?.[0]?.id || "";
      const schemaVersion = vehicleSchemas?.schemas?.[0]?.version || 1;
      console.log("Submitting vehicle entity creation");
      createEntityMutation.mutate(
        createEntityFromValues(
          values,
          EntityType.VEHICLE,
          schemaId,
          schemaVersion
        ),
        {
          onSuccess: () => {
            handleCloseSidePanel();
          },
        }
      );
    }
    // Property Entity Submission
    else if (activePanelType === PanelType.PROPERTY) {
      console.log("Submitting property creation");

      createPropertyEntity({
        values,
        dispatcherAsset,
        createPropertyMutation,
        createEntityMutation,
        propertySchemas,
        onSuccess: () => {
          // Don't call handleCloseSidePanel here - let the wrapped mutation handle it
          // The wrapped mutation will call handleCreatePropertySuccess which handles the section update
        },
        onError: (error: any) => {
          console.error("Property creation failed:", error);
          setNotification({
            open: true,
            message: `Error creating property: ${error.message}`,
            severity: "error",
          });
          setIsSaveLoading(false);
        },
      });
    }
    // Organization Entity Submission
    else if (activePanelType === PanelType.ORGANIZATION) {
      const schemaId = organizationSchemas?.schemas?.[0]?.id || "";
      const schemaVersion = organizationSchemas?.schemas?.[0]?.version || 1;
      console.log("Submitting organization entity creation");
      createEntityMutation.mutate(
        createEntityFromValues(
          values,
          EntityType.ORGANIZATION,
          schemaId,
          schemaVersion
        ),
        {
          onSuccess: () => {
            handleCloseSidePanel();
          },
        }
      );
    }
  };

  const handleFormUpdate = (values: any) => {
    if (!editingEntity || !editingEntityId) {
      console.warn("No entity to update");
      return;
    }

    setIsUpdateLoading(true);

    const updateRequest = {
      entity: {
        id: editingEntity.id,
        data: values,
      },
    } as any;

    updateEntityMutation.mutate(updateRequest);
  };

  const handleSaveAndAddAnother = async (values: any) => {
    setIsSaveAndAddAnotherLoading(true);

    const onSuccess = () => {
      // Reset form and scroll to top
      if (panelContentRef.current) {
        panelContentRef.current.scrollToTop();
      }

      // Create a fresh form by forcing a remount of PanelContent
      setActivePanelType((prevType) => {
        const currentType = prevType;
        setActivePanelType(null);

        // Short timeout to ensure component remounts
        setTimeout(() => {
          setActivePanelType(currentType);
        }, FORM_RESET_TIMEOUT_MS);

        return null;
      });

      setIsSaveAndAddAnotherLoading(false);
    };

    const onError = (error: Error) => {
      // Don't reset the form on error
      setIsSaveAndAddAnotherLoading(false);
      setIsSaveLoading(false);
      setNotification({
        open: true,
        message: `Failed to save: ${error.message}`,
        severity: "error",
      });
    };

    // Route to appropriate entity creation based on panel type
    if (activePanelType === PanelType.PERSON) {
      const schemaId = personSchemas?.schemas?.[0]?.id || "";
      const schemaVersion = personSchemas?.schemas?.[0]?.version || 1;
      createEntityMutation.mutate(
        createEntityFromValues(
          values,
          EntityType.PERSON,
          schemaId,
          schemaVersion
        ),
        {
          onSuccess,
          onError,
        }
      );
    } else if (activePanelType === PanelType.VEHICLE) {
      const schemaId = vehicleSchemas?.schemas?.[0]?.id || "";
      const schemaVersion = vehicleSchemas?.schemas?.[0]?.version || 1;
      createEntityMutation.mutate(
        createEntityFromValues(
          values,
          EntityType.VEHICLE,
          schemaId,
          schemaVersion
        ),
        {
          onSuccess,
          onError,
        }
      );
    } else if (activePanelType === PanelType.PROPERTY) {
      createPropertyEntity({
        values,
        dispatcherAsset,
        createPropertyMutation,
        createEntityMutation,
        propertySchemas,
        onSuccess,
        onError,
      });
    }
  };

  const handleSaveStatusChange = (status: SaveStatus) => {
    return status;
  };

  const handleCloseNotification = () => {
    setNotification((prev: any) => ({ ...prev, open: false }));
  };

  // Return the complete set of form handlers
  return {
    handleFormSubmit,
    handleFormUpdate,
    handleSaveAndAddAnother,
    handleSaveStatusChange,
    handleCloseNotification,
  };
};
