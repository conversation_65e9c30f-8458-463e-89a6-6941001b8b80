import { ReportSection } from "proto/hero/reports/v2/reports_pb";
import { getEntityDisplayName } from "./utils";

// Process report sections to extract section IDs and entity references
export const processReportSections = (
  sections: ReportSection[] | undefined,
  setSectionCallbacks: {
    setPeopleListSectionId: (id: string | null) => void;
    setVehicleListSectionId: (id: string | null) => void;
    setPropertyListSectionId: (id: string | null) => void;
    setOrganizationListSectionId: (id: string | null) => void;
    setOffenseListSectionId: (id: string | null) => void;
    setSectionIdToType: (mapping: Record<string, string>) => void;
  }
) => {
  if (!sections) return { entityIds: [] };

  const allEntityIds: string[] = [];
  // Create mapping for section IDs to section types
  const sectionMapping: Record<string, string> = {};

  // First pass: collect all sections and prioritize SECTION_TYPE_PROPERTY over SECTION_TYPE_ENTITY_LIST_PROPERTIES
  const propertySections: { id: string; type: string }[] = [];
  const otherSections: { section: any; type: string; entityRefs?: Array<{ id: string }> }[] = [];

  sections.forEach((section) => {
    // @ts-expect-error TODO: Fix type issue
    const sectionTypeStr = section.type as string;
    let entityRefs: Array<{ id: string }> | undefined;

    // Store section ID to type mapping
    sectionMapping[section.id] = sectionTypeStr;

    // Special handling for property sections - collect them separately
    if (sectionTypeStr === "SECTION_TYPE_PROPERTY" || sectionTypeStr === "SECTION_TYPE_ENTITY_LIST_PROPERTIES") {
      propertySections.push({ id: section.id, type: sectionTypeStr });
      // Collect entity IDs from property sections
      if (sectionTypeStr === "SECTION_TYPE_ENTITY_LIST_PROPERTIES") {
        // @ts-expect-error TODO: Fix type issue
        entityRefs = section.entityList?.entityRefs;
        if (entityRefs && entityRefs.length > 0) {
          const ids = entityRefs.map((ref) => ref.id);
          allEntityIds.push(...ids);
        }
      } else if (sectionTypeStr === "SECTION_TYPE_PROPERTY") {
        // @ts-expect-error TODO: Fix type issue
        const propertyRefs = section.propertyList?.propertyRefs;
        if (propertyRefs && propertyRefs.length > 0) {
          const ids = propertyRefs.map((ref: any) => ref.id);
          allEntityIds.push(...ids);
        }
      }
    } else {
      // Handle other sections normally
      otherSections.push({ section, type: sectionTypeStr, entityRefs });
    }
  });

  // Process property sections with priority for SECTION_TYPE_PROPERTY
  const preferredPropertySection = propertySections.find(s => s.type === "SECTION_TYPE_PROPERTY") ||
    propertySections.find(s => s.type === "SECTION_TYPE_ENTITY_LIST_PROPERTIES");

  if (preferredPropertySection) {
    setSectionCallbacks.setPropertyListSectionId(preferredPropertySection.id);
  }

  // Process other sections
  otherSections.forEach(({ section, type }) => {
    let entityRefs: Array<{ id: string }> | undefined;

    switch (type) {
      case "SECTION_TYPE_ENTITY_LIST_PEOPLE":
        setSectionCallbacks.setPeopleListSectionId(section.id);
        // @ts-expect-error TODO: Fix type issue
        entityRefs = section.entityList?.entityRefs;
        break;
      case "SECTION_TYPE_ENTITY_LIST_VEHICLE":
        setSectionCallbacks.setVehicleListSectionId(section.id);
        // @ts-expect-error TODO: Fix type issue
        entityRefs = section.entityList?.entityRefs;
        break;
      case "SECTION_TYPE_ENTITY_LIST_ORGANIZATIONS":
        setSectionCallbacks.setOrganizationListSectionId(section.id);
        // @ts-expect-error TODO: Fix type issue
        entityRefs = section.entityList?.entityRefs;
        break;
      case "SECTION_TYPE_OFFENSE":
        setSectionCallbacks.setOffenseListSectionId(section.id);
        break;
    }

    // Collect entity IDs from entity list sections
    if (entityRefs && entityRefs.length > 0) {
      const ids = entityRefs.map((ref) => ref.id);
      allEntityIds.push(...ids);
    }
  });

  // Update section ID to type mapping
  setSectionCallbacks.setSectionIdToType(sectionMapping);

  // Return the unique entity IDs to fetch
  return {
    entityIds: [...new Set(allEntityIds)],
  };
};

// Helper: Update entity display name in section
export const updateEntityDisplayNameInSection = (
  entity: any,
  sectionId: string,
  reportId: string,
  reportSections: any,
  updateReportSectionMutation: any
) => {
  if (!reportId) return;
  const section = reportSections?.sections?.find(
    (s: any) => s.id === sectionId
  );
  const sectionTypeStr = section?.type as string;
  const isEntityListSection =
    sectionTypeStr === "SECTION_TYPE_ENTITY_LIST_PEOPLE" ||
    sectionTypeStr === "SECTION_TYPE_ENTITY_LIST_VEHICLE" ||
    sectionTypeStr === "SECTION_TYPE_ENTITY_LIST_PROPERTIES" ||
    sectionTypeStr === "SECTION_TYPE_ENTITY_LIST_ORGANIZATIONS";

  if (!section || !isEntityListSection) return;

  // Create a formatted display name for the entity
  const displayName = getEntityDisplayName(entity);

  const existingRefs = section.entityList?.entityRefs;
  const entityRefs = Array.isArray(existingRefs) ? [...existingRefs] : [];

  const existingRefIndex = entityRefs.findIndex((ref) => ref.id === entity.id);

  if (existingRefIndex >= 0) {
    // Update existing reference with updated display name
    entityRefs[existingRefIndex] = {
      ...entityRefs[existingRefIndex],
      displayName,
    };

    // Create the update request
    const updateRequest = {
      reportId,
      section: {
        ...section,
        entityList: {
          ...section.entityList,
          entityRefs,
        },
      },
    };
    updateReportSectionMutation.mutate(updateRequest);
  }
};

// Helper: Create or update entity list section in report
export const updateEntityListSection = (
  title: string,
  entity: any,
  existingSectionId: string | null,
  reportId: string,
  reportSections: any,
  updateReportSectionMutation: any,
  entities: {
    people: any[];
    vehicles: any[];
    properties: any[];
    organizations: any[];
  },
  allEntitiesOverride?: any[]
) => {
  if (!reportId) return;
  if (existingSectionId) {
    // Update existing section
    const section = reportSections?.sections?.find(
      (s: any) => s.id === existingSectionId
    );

    const sectionTypeStr = section?.type as string;
    const isEntityListSection =
      sectionTypeStr === "SECTION_TYPE_ENTITY_LIST_PEOPLE" ||
      sectionTypeStr === "SECTION_TYPE_ENTITY_LIST_VEHICLE" ||
      sectionTypeStr === "SECTION_TYPE_ENTITY_LIST_PROPERTIES" ||
      sectionTypeStr === "SECTION_TYPE_ENTITY_LIST_ORGANIZATIONS";

    if (section && isEntityListSection) {
      // Get all entities of this type from our local state or use the provided override
      let allEntities;
      // Define the type for entityRefs
      let entityRefs: Array<{
        id: string;
        type: string;
        version: number;
        displayName: string;
      }> = [];

      // Use override if provided, otherwise use the current state
      if (allEntitiesOverride) {
        allEntities = allEntitiesOverride;
      } else {
        // Build complete entity references based on our local state arrays
        switch (sectionTypeStr) {
          case "SECTION_TYPE_ENTITY_LIST_PEOPLE":
            allEntities = entities.people;
            break;
          case "SECTION_TYPE_ENTITY_LIST_VEHICLE":
            allEntities = entities.vehicles;
            break;
          case "SECTION_TYPE_ENTITY_LIST_PROPERTIES":
            allEntities = entities.properties;
            break;
          case "SECTION_TYPE_ENTITY_LIST_ORGANIZATIONS":
            allEntities = entities.organizations;
            break;
        }
      }

      // Create entity references for all entities in our local state
      if (allEntities && allEntities.length > 0) {
        entityRefs = allEntities.map((localEntity) => {
          const localDisplayName = getEntityDisplayName(localEntity);
          const localEntityType = localEntity.entityType.replace(
            "ENTITY_TYPE_",
            ""
          );

          return {
            id: localEntity.id,
            type: localEntityType,
            version: localEntity.version || 1,
            displayName: localDisplayName,
          };
        });
      }
      // Use shared helper to replace refs array
      replaceReportSectionRefs({
        listType: "entityList",
        title,
        refs: entityRefs,
        sectionId: existingSectionId,
        reportId,
        reportSections,
        updateReportSectionMutation,
      });
    }
  }
};

// Shared helper: replace entire refs array in a report section (entityList or propertyList)
export const replaceReportSectionRefs = (params: {
  listType: "entityList" | "propertyList";
  title?: string;
  refs: any[];
  sectionId: string;
  reportId: string;
  reportSections: any;
  updateReportSectionMutation: any;
}) => {
  const { listType, title, refs, sectionId, reportId, reportSections, updateReportSectionMutation } = params;
  if (!reportId || !sectionId) return;
  const section = reportSections?.sections?.find((s: any) => s.id === sectionId);
  if (!section) return;

  const updatedSection = {
    ...section,
    [listType]: {
      ...(section as any)[listType],
      ...(title ? { title } : {}),
      // entityList.entityRefs OR propertyList.propertyRefs
      [listType === "entityList" ? "entityRefs" : "propertyRefs"]: refs,
    },
  };

  updateReportSectionMutation.mutate({ reportId, section: updatedSection });
};

// Shared helper: upsert a single reference into refs array based on id
export const upsertReportSectionRef = (params: {
  listType: "entityList" | "propertyList";
  title?: string;
  newRef: any;
  sectionId: string;
  reportId: string;
  reportSections: any;
  updateReportSectionMutation: any;
}) => {
  const { listType, title, newRef, sectionId, reportId, reportSections, updateReportSectionMutation } = params;
  if (!reportId || !sectionId) return;
  const section = reportSections?.sections?.find((s: any) => s.id === sectionId);
  if (!section) return;

  const refsKey = listType === "entityList" ? "entityRefs" : "propertyRefs";
  const existingRefs: any[] = ((section as any)[listType]?.[refsKey] || []) as any[];
  const existingIndex = existingRefs.findIndex((ref: any) => ref.id === newRef.id);
  const updatedRefs = [...existingRefs];
  if (existingIndex >= 0) {
    updatedRefs[existingIndex] = newRef;
  } else {
    updatedRefs.push(newRef);
  }

  const updatedSection = {
    ...section,
    [listType]: {
      ...(section as any)[listType],
      ...(title ? { title } : {}),
      [refsKey]: updatedRefs,
    },
  };

  updateReportSectionMutation.mutate({ reportId, section: updatedSection });
};
