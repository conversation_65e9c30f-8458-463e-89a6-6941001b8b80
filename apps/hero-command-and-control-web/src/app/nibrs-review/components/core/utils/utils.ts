import { useMemo } from "react";
import {
  OrganizationData,
  PersonData,
  PropertyData,
  VehicleData,
} from "../../../../reports/ReportComponents/entities/EntityCard";
import { getReadablePropertyType } from "../../../../utils/propertyHelpers";
import { PanelType, getPanelTitle } from "../types";
import { getSchemaForPanelType } from "./schemaUtils";
import { scrollToSection as scrollToSectionUtil } from "./scrollUtils";

// Navigation items for sidebar
export const navItems = [
  { id: "incident-details", label: "Incident Details" },
  { id: "offenses", label: "Offenses" },
  { id: "people", label: "People" },
  { id: "vehicles", label: "Vehicles" },
  { id: "property", label: "Property" },
  { id: "narrative", label: "Narrative" },
  { id: "media", label: "Media" },
];

// getReadablePropertyType is now imported from shared helpers

// Get entity display name based on entity type
export const getEntityDisplayName = (entity: any): string => {
  if (!entity || !entity.data) return "Unknown Entity";

  switch (entity.entityType) {
    case "ENTITY_TYPE_PERSON": {
      const firstName = entity.data.classificationSection?.firstName || "";
      const lastName = entity.data.classificationSection?.lastName || "";
      return `${firstName} ${lastName}`.trim() || "Unnamed Person";
    }

    case "ENTITY_TYPE_VEHICLE": {
      const year = entity.data.vehicleInformationSection?.year || "";
      const make = entity.data.vehicleInformationSection?.make || "";
      const model = entity.data.vehicleInformationSection?.model || "";
      return `${year} ${make} ${model}`.trim() || "Unknown Vehicle";
    }

    case "ENTITY_TYPE_PROPERTY": {
      const desc = entity.data.propertyInformationSection?.description || entity.data.description || "";
      return desc || "Unknown Property";
    }

    case "ENTITY_TYPE_ORGANIZATION": {
      const name = entity.data.organizationInformationSection?.name || "";
      const type = entity.data.organizationInformationSection?.type || "";
      return `${name} - ${type}`.trim() || "Unknown Organization";
    }
    default:
      return "Unknown Entity";
  }
};

// Convert entities for card display
export const entitiesToPersonData = (entities: any[]): PersonData[] =>
  entities.map((entity) => {
    const d = entity.data || {};
    return {
      id: entity.id,
      name: `${d.classificationSection?.firstName || ""} ${d.classificationSection?.lastName || ""
        }`.trim(),
      sex: d.classificationSection?.sex || "",
      height: d.descriptorsSection?.height || "",
      hair: d.descriptorsSection?.hairColor || "",
      weight: d.descriptorsSection?.weight || "",
      eye: d.descriptorsSection?.eyeColor || "",
      dateOfBirth: d.classificationSection?.dateOfBirth || "",
    };
  });

export const entitiesToPropertyData = (entities: any[]): PropertyData[] =>
  entities.map((entity) => {
    const d = entity.data || {};
    const propertyType = d.propertyInformationSection?.propertyType || d.propertyType || "";
    return {
      id: entity.id,
      propertyType: getReadablePropertyType(propertyType),
      serialNumber: d.propertyInformationSection?.serialNumber || d.serialNumber || "",
      category: d.propertyInformationSection?.category || d.category || "",
      collectedValue: d.propertyInformationSection?.value || d.value || "",
      makeModelBrand: d.propertyInformationSection?.makeModelBrand || d.makeModelBrand || "",
      description: d.propertyInformationSection?.description || d.description || "",
    };
  });

export const entitiesToVehicleData = (entities: any[]): VehicleData[] =>
  entities.map((entity) => {
    const d = entity.data || {};
    return {
      id: entity.id,
      vIN: d.vehicleInformationSection?.vIN || "",
      year: d.vehicleInformationSection?.year || "",
      make: d.vehicleInformationSection?.make || "",
      model: d.vehicleInformationSection?.model || "",
      color: d.vehicleInformationSection?.color || "",
      ownerIfApplicable: d.vehicleInformationSection?.ownerIfApplicable || "",
    };
  });

export const entitiesToOrganizationData = (
  entities: any[]
): OrganizationData[] =>
  entities.map((entity) => {
    const d = entity.data || {};
    return {
      id: entity.id,
      name: d.organizationInformationSection?.name || "",
      type: d.organizationInformationSection?.type || "",
      streetAddress: d.organizationInformationSection?.streetAddress || "",
      state: d.organizationInformationSection?.state || "",
      zIP: d.organizationInformationSection?.zIP || "",
    };
  });

// Helper to create available navigation items
export const useAvailableNavItems = (
  peopleListSectionId: string | null,
  vehicleListSectionId: string | null,
  propertyListSectionId: string | null,
  offenseListSectionId: string | null
) => {
  return useMemo(() => {
    const sectionIdMapping = {
      people: peopleListSectionId,
      vehicles: vehicleListSectionId,
      property: propertyListSectionId,
      offenses: offenseListSectionId,
    };

    return navItems.filter(
      (item) => sectionIdMapping[item.id as keyof typeof sectionIdMapping]
    );
  }, [
    peopleListSectionId,
    vehicleListSectionId,
    propertyListSectionId,
    offenseListSectionId,
  ]);
};

// Helper to get current schema
export const useGetCurrentSchema = (
  activePanelType: PanelType | null,
  editingEntityId: string | null,
  editingEntity: any,
  specificSchema: any,
  personSchemas: any,
  propertySchemas: any,
  vehicleSchemas: any,
  organizationSchemas: any
) => {
  return () => {
    return getSchemaForPanelType(
      activePanelType,
      editingEntityId,
      editingEntity,
      specificSchema,
      personSchemas,
      propertySchemas,
      vehicleSchemas,
      organizationSchemas
    );
  };
};

// Helper to get contextual panel title
export const useGetContextualPanelTitle = (
  activePanelType: PanelType | null,
  activeOffenseRelation: any,
  activeVehicleOffenseContext: any,
  activePropertyOffenseContext: any,
  activeOrganizationOffenseContext: any,
  editingEntityId: string | null
) => {
  return () => {
    // Check if we have offense context for person entities
    if (
      activePanelType === PanelType.PERSON &&
      activeOffenseRelation &&
      !editingEntityId
    ) {
      return getPanelTitle(
        activePanelType,
        { relationType: activeOffenseRelation.relationType },
        !!editingEntityId
      );
    }

    // Check if we have offense context for vehicle entities
    if (
      activePanelType === PanelType.VEHICLE &&
      activeVehicleOffenseContext &&
      !editingEntityId
    ) {
      return getPanelTitle(activePanelType, {}, !!editingEntityId);
    }

    // Check if we have offense context for property entities
    if (
      activePanelType === PanelType.PROPERTY &&
      activePropertyOffenseContext &&
      !editingEntityId
    ) {
      return getPanelTitle(activePanelType, {}, !!editingEntityId);
    }

    // Check if we have offense context for organization entities
    if (
      activePanelType === PanelType.ORGANIZATION &&
      activeOrganizationOffenseContext &&
      !editingEntityId
    ) {
      return getPanelTitle(activePanelType, {}, !!editingEntityId);
    }

    // Default titles without offense context or in edit mode
    return getPanelTitle(activePanelType, undefined, !!editingEntityId);
  };
};

// Scroll to section helper
export const createScrollToSection = (
  scrollContainerRef: React.RefObject<HTMLDivElement | null>
) => {
  return (id: string) => {
    scrollToSectionUtil(id, scrollContainerRef.current);
  };
};

// Go back helper
export const createGoBack = (router: any) => {
  return () => {
    const currentPath = window.location.pathname;
    router.replace(currentPath);
  };
};
