import * as Sentry from "@sentry/react-native";
import {
  AuthSessionResult,
  DiscoveryDocument,
  exchangeCodeAsync,
  makeRedirectUri,
  revokeAsync,
  TokenResponse,
} from "expo-auth-session";
import * as WebBrowser from "expo-web-browser";
import { jwtDecode } from "jwt-decode";
import React, {
  createContext,
  PropsWithChildren,
  useContext,
  useEffect,
  useMemo,
  useState,
} from "react";
import { Alert } from "react-native";
import { getServiceUrl, setUserPoolUrlForApis } from "./apis/config";
import {
  setAccessGetter as setCommunicationsAccessGetter,
  setCommunicationsBaseUrl,
} from "./apis/services/communications/axiosInstance";
import {
  setAccessGetter as setWorkflowAccessGetter,
  setWorkflowBaseUrl,
} from "./apis/services/workflow/axiosInstance";
import { EnvironmentConfig } from "./services/discoveryService";

WebBrowser.maybeCompleteAuthSession();

const redirectUri = makeRedirectUri({
  native: "myapp://callback/",
  scheme: "myapp",
  path: "callback/",
});
console.log("redirectUri:", redirectUri);

/**
 * Create discovery document from environment configuration
 */
const createDiscoveryDocument = (
  config: EnvironmentConfig
): DiscoveryDocument => ({
  authorizationEndpoint: `${config.userPoolUrl}/oauth2/authorize`,
  tokenEndpoint: `${config.userPoolUrl}/oauth2/token`,
  revocationEndpoint: `${config.userPoolUrl}/oauth2/revoke`,
});

interface AuthContextValue {
  authTokens: TokenResponse | null;
  promptAsync: () => Promise<AuthSessionResult>;
  logout: () => Promise<void>;
  isLoading: boolean;
  isAuthReady: boolean;
}

interface DecodedToken {
  sub: string;
  email?: string;
  username?: string;
}

const AuthContext = createContext<AuthContextValue>({} as AuthContextValue);

// Helper function to set Sentry user context
const setSentryUserContext = (tokens: TokenResponse | null) => {
  if (tokens?.idToken) {
    try {
      const decoded = jwtDecode<DecodedToken>(tokens.idToken);
      Sentry.setUser({
        id: decoded.sub,
        email: decoded.email,
        username: decoded.username,
      });
    } catch (error) {
      console.error("Error decoding token for Sentry context:", error);
      Sentry.setUser(null);
    }
  } else {
    Sentry.setUser(null);
  }
};

interface AuthProviderProps extends PropsWithChildren {
  environmentConfig?: EnvironmentConfig | null;
  onClearEnvironmentConfig?: () => void;
}

/**
 * EPHEMERAL AUTH PROVIDER
 *
 * This provider implements ephemeral authentication:
 * - Tokens are only stored in memory (not persisted to SecureStore)
 * - Sessions do not persist between app launches
 * - Each app launch requires fresh authentication
 * - WebView cookies are cleared on logout and app launch
 */
export function AuthProvider({
  children,
  environmentConfig = null,
  onClearEnvironmentConfig,
}: AuthProviderProps) {
  const [authTokens, setAuthTokens] = useState<TokenResponse | null>(null);
  const [isAuthInProgress, setIsAuthInProgress] = useState(false);
  
  const config = environmentConfig;
  const discovery = config ? createDiscoveryDocument(config) : null;
  const isAuthReady = !!config && !!discovery;

  // Log when config changes
  if (config) {
    console.log("AuthProvider environmentConfig:", config.userPoolUrl);
  }

  // Configure API base URLs when environment changes
  useEffect(() => {
    if (!config) {
      setUserPoolUrlForApis(null);
      setWorkflowBaseUrl(null);
      setCommunicationsBaseUrl(null);
      return;
    }

    setUserPoolUrlForApis(config.userPoolUrl);
    setWorkflowBaseUrl(getServiceUrl("workflow"));
    setCommunicationsBaseUrl(getServiceUrl("communications"));
  }, [config?.userPoolUrl]);

  // Clear WebView cookies on mount to ensure clean state
  useEffect(() => {
    const clearAuthSession = async () => {
      try {
        WebBrowser.maybeCompleteAuthSession({ skipRedirectCheck: true });
        console.log("Cleared WebView session on mount");
      } catch (error) {
        console.error("Error clearing initial WebView session:", error);
      }
    };

    clearAuthSession();
  }, []);

  // Ephemeral promptAsync - uses WebBrowser directly with preferEphemeralSession
  const promptAsync = async (): Promise<AuthSessionResult> => {
    if (!config || !discovery) {
      throw new Error("Environment not configured");
    }

    if (isAuthInProgress) {
      console.log("Auth already in progress, skipping");
      return { type: "cancel" } as AuthSessionResult;
    }

    try {
      setIsAuthInProgress(true);
      
      // Clear any existing WebView cookies before prompting
      WebBrowser.maybeCompleteAuthSession({ skipRedirectCheck: true });

      // Build auth URL with ephemeral session requirements
      const authUrl = `${discovery.authorizationEndpoint}?` +
        new URLSearchParams({
          client_id: config.clientId,
          response_type: "code",
          redirect_uri: redirectUri,
          prompt: "login", // Force fresh login
          max_age: "0", // Force re-authentication
        }).toString();

      console.log("Opening ephemeral auth session");
      const result = await WebBrowser.openAuthSessionAsync(
        authUrl,
        redirectUri,
        {
          preferEphemeralSession: true, // Prevents iOS prompt
          dismissButtonStyle: "cancel",
        }
      );

      if (result.type === "success" && result.url) {
        const responseUrl = new URL(result.url);
        const code = responseUrl.searchParams.get("code");
        const error = responseUrl.searchParams.get("error");

        if (error) {
          const errorDescription = responseUrl.searchParams.get("error_description");
          Alert.alert("Authentication error", errorDescription || error);
          return { type: "error" } as AuthSessionResult;
        }

        if (code) {
          console.log("Token exchange starting");
          const exchangeResponse = await exchangeCodeAsync(
            {
              code,
              clientId: config.clientId,
              redirectUri,
            },
            discovery
          );

          setAuthTokens(exchangeResponse);
          setSentryUserContext(exchangeResponse);
          console.log("Authentication successful");
          
          return { type: "success" } as AuthSessionResult;
        }
      }

      return { type: result.type } as AuthSessionResult;
    } catch (error) {
      console.error("Error in ephemeral prompt:", error);
      Alert.alert("Error", "Authentication failed. Please try again.");
      return { type: "error" } as AuthSessionResult;
    } finally {
      setIsAuthInProgress(false);
    }
  };

  // Logout - clear everything
  const logout = async (): Promise<void> => {
    try {
      if (config && discovery && authTokens?.refreshToken) {
        // Open Cognito logout URL
        const logoutUrl = `${config.userPoolUrl}/logout?client_id=${
          config.clientId
        }&logout_uri=${encodeURIComponent(redirectUri)}`;

        console.log("Opening hosted UI logout");
        const logoutResult = await WebBrowser.openAuthSessionAsync(
          logoutUrl,
          redirectUri,
          { preferEphemeralSession: true }
        );

        if (logoutResult.type === "success" || logoutResult.type === "cancel") {
          try {
            await revokeAsync(
              {
                clientId: config.clientId,
                token: authTokens.refreshToken,
              },
              discovery
            );
            console.log("Refresh token revoked");
          } catch (revokeError) {
            console.error("Token revocation failed:", revokeError);
          }
        }
      }

      // Clear WebView cookies and local state
      WebBrowser.maybeCompleteAuthSession({ skipRedirectCheck: true });
      setAuthTokens(null);
      setSentryUserContext(null);
      onClearEnvironmentConfig?.();

      console.log("Ephemeral logout completed");
    } catch (error) {
      console.error("Logout error:", error);
      // Always clear local state
      setAuthTokens(null);
      setSentryUserContext(null);
      onClearEnvironmentConfig?.();
    }
  };

  // Set access getter functions for axios instances
  useEffect(() => {
    setWorkflowAccessGetter(() => authTokens?.accessToken);
    setCommunicationsAccessGetter(() => authTokens?.accessToken);
  }, [authTokens]);

  // Memoize context value (fixed dependencies)
  const value = useMemo<AuthContextValue>(
    () => ({
      authTokens,
      promptAsync,
      logout,
      isLoading: isAuthInProgress,
      isAuthReady,
    }),
    [authTokens, isAuthReady, isAuthInProgress]
  );

  if (!environmentConfig) {
    return (
      <AuthContext.Provider
        value={{
          authTokens: null,
          promptAsync: () =>
            Promise.reject(new Error("Environment not configured")),
          logout: async () => {},
          isLoading: false,
          isAuthReady: false,
        }}
      >
        {children}
      </AuthContext.Provider>
    );
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

/**
 * Custom hook to consume the AuthContext
 */
export function useAuth(): AuthContextValue {
  return useContext(AuthContext);
}