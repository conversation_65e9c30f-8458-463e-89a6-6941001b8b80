// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.3
// 	protoc        (unknown)
// source: hero/property/v1/property.proto

package property

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// PropertyType enumerates the types of property handling
type PropertyType int32

const (
	PropertyType_PROPERTY_TYPE_UNSPECIFIED PropertyType = 0
	PropertyType_PROPERTY_TYPE_FOUND       PropertyType = 1
	PropertyType_PROPERTY_TYPE_SEIZED      PropertyType = 2
	PropertyType_PROPERTY_TYPE_STOLEN      PropertyType = 3
	PropertyType_PROPERTY_TYPE_SAFEKEEPING PropertyType = 4
	PropertyType_PROPERTY_TYPE_MISSING     PropertyType = 5
	PropertyType_PROPERTY_TYPE_RECOVERED   PropertyType = 6
)

// Enum value maps for PropertyType.
var (
	PropertyType_name = map[int32]string{
		0: "PROPERTY_TYPE_UNSPECIFIED",
		1: "PROPERTY_TYPE_FOUND",
		2: "PROPERTY_TYPE_SEIZED",
		3: "PROPERTY_TYPE_STOLEN",
		4: "PROPERTY_TYPE_SAFEKEEPING",
		5: "PROPERTY_TYPE_MISSING",
		6: "PROPERTY_TYPE_RECOVERED",
	}
	PropertyType_value = map[string]int32{
		"PROPERTY_TYPE_UNSPECIFIED": 0,
		"PROPERTY_TYPE_FOUND":       1,
		"PROPERTY_TYPE_SEIZED":      2,
		"PROPERTY_TYPE_STOLEN":      3,
		"PROPERTY_TYPE_SAFEKEEPING": 4,
		"PROPERTY_TYPE_MISSING":     5,
		"PROPERTY_TYPE_RECOVERED":   6,
	}
)

func (x PropertyType) Enum() *PropertyType {
	p := new(PropertyType)
	*p = x
	return p
}

func (x PropertyType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PropertyType) Descriptor() protoreflect.EnumDescriptor {
	return file_hero_property_v1_property_proto_enumTypes[0].Descriptor()
}

func (PropertyType) Type() protoreflect.EnumType {
	return &file_hero_property_v1_property_proto_enumTypes[0]
}

func (x PropertyType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PropertyType.Descriptor instead.
func (PropertyType) EnumDescriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{0}
}

// PropertyStatus enumerates the status of property items
type PropertyStatus int32

const (
	PropertyStatus_PROPERTY_STATUS_UNSPECIFIED PropertyStatus = 0
	PropertyStatus_PROPERTY_STATUS_COLLECTED   PropertyStatus = 1
	PropertyStatus_PROPERTY_STATUS_IN_CUSTODY  PropertyStatus = 2
	PropertyStatus_PROPERTY_STATUS_CHECKED_OUT PropertyStatus = 3
	PropertyStatus_PROPERTY_STATUS_DISPOSED    PropertyStatus = 4
	PropertyStatus_PROPERTY_STATUS_MISSING     PropertyStatus = 5
	PropertyStatus_PROPERTY_STATUS_CLAIMED     PropertyStatus = 6
)

// Enum value maps for PropertyStatus.
var (
	PropertyStatus_name = map[int32]string{
		0: "PROPERTY_STATUS_UNSPECIFIED",
		1: "PROPERTY_STATUS_COLLECTED",
		2: "PROPERTY_STATUS_IN_CUSTODY",
		3: "PROPERTY_STATUS_CHECKED_OUT",
		4: "PROPERTY_STATUS_DISPOSED",
		5: "PROPERTY_STATUS_MISSING",
		6: "PROPERTY_STATUS_CLAIMED",
	}
	PropertyStatus_value = map[string]int32{
		"PROPERTY_STATUS_UNSPECIFIED": 0,
		"PROPERTY_STATUS_COLLECTED":   1,
		"PROPERTY_STATUS_IN_CUSTODY":  2,
		"PROPERTY_STATUS_CHECKED_OUT": 3,
		"PROPERTY_STATUS_DISPOSED":    4,
		"PROPERTY_STATUS_MISSING":     5,
		"PROPERTY_STATUS_CLAIMED":     6,
	}
)

func (x PropertyStatus) Enum() *PropertyStatus {
	p := new(PropertyStatus)
	*p = x
	return p
}

func (x PropertyStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PropertyStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_hero_property_v1_property_proto_enumTypes[1].Descriptor()
}

func (PropertyStatus) Type() protoreflect.EnumType {
	return &file_hero_property_v1_property_proto_enumTypes[1]
}

func (x PropertyStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PropertyStatus.Descriptor instead.
func (PropertyStatus) EnumDescriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{1}
}

// PropertyDisposalType enumerates how property was disposed
type PropertyDisposalType int32

const (
	PropertyDisposalType_PROPERTY_DISPOSAL_TYPE_UNSPECIFIED   PropertyDisposalType = 0
	PropertyDisposalType_PROPERTY_DISPOSAL_TYPE_RELEASED      PropertyDisposalType = 1
	PropertyDisposalType_PROPERTY_DISPOSAL_TYPE_DESTROYED     PropertyDisposalType = 2
	PropertyDisposalType_PROPERTY_DISPOSAL_TYPE_AUCTIONED     PropertyDisposalType = 3
	PropertyDisposalType_PROPERTY_DISPOSAL_TYPE_AGENCY_RETAIN PropertyDisposalType = 4
	PropertyDisposalType_PROPERTY_DISPOSAL_TYPE_TRANSFERRED   PropertyDisposalType = 5
)

// Enum value maps for PropertyDisposalType.
var (
	PropertyDisposalType_name = map[int32]string{
		0: "PROPERTY_DISPOSAL_TYPE_UNSPECIFIED",
		1: "PROPERTY_DISPOSAL_TYPE_RELEASED",
		2: "PROPERTY_DISPOSAL_TYPE_DESTROYED",
		3: "PROPERTY_DISPOSAL_TYPE_AUCTIONED",
		4: "PROPERTY_DISPOSAL_TYPE_AGENCY_RETAIN",
		5: "PROPERTY_DISPOSAL_TYPE_TRANSFERRED",
	}
	PropertyDisposalType_value = map[string]int32{
		"PROPERTY_DISPOSAL_TYPE_UNSPECIFIED":   0,
		"PROPERTY_DISPOSAL_TYPE_RELEASED":      1,
		"PROPERTY_DISPOSAL_TYPE_DESTROYED":     2,
		"PROPERTY_DISPOSAL_TYPE_AUCTIONED":     3,
		"PROPERTY_DISPOSAL_TYPE_AGENCY_RETAIN": 4,
		"PROPERTY_DISPOSAL_TYPE_TRANSFERRED":   5,
	}
)

func (x PropertyDisposalType) Enum() *PropertyDisposalType {
	p := new(PropertyDisposalType)
	*p = x
	return p
}

func (x PropertyDisposalType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PropertyDisposalType) Descriptor() protoreflect.EnumDescriptor {
	return file_hero_property_v1_property_proto_enumTypes[2].Descriptor()
}

func (PropertyDisposalType) Type() protoreflect.EnumType {
	return &file_hero_property_v1_property_proto_enumTypes[2]
}

func (x PropertyDisposalType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PropertyDisposalType.Descriptor instead.
func (PropertyDisposalType) EnumDescriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{2}
}

// CustodyActionType enumerates the types of custody changes
type CustodyActionType int32

const (
	CustodyActionType_CUSTODY_ACTION_TYPE_UNSPECIFIED CustodyActionType = 0
	CustodyActionType_CUSTODY_ACTION_TYPE_COLLECTED   CustodyActionType = 1
	CustodyActionType_CUSTODY_ACTION_TYPE_TRANSFERRED CustodyActionType = 2
	CustodyActionType_CUSTODY_ACTION_TYPE_RELEASED    CustodyActionType = 3
	CustodyActionType_CUSTODY_ACTION_TYPE_CHECKED_OUT CustodyActionType = 4
	CustodyActionType_CUSTODY_ACTION_TYPE_CHECKED_IN  CustodyActionType = 5
	CustodyActionType_CUSTODY_ACTION_TYPE_DISPOSED    CustodyActionType = 6
	CustodyActionType_CUSTODY_ACTION_TYPE_LOGGED      CustodyActionType = 7 // Property reported but not in physical custody
)

// Enum value maps for CustodyActionType.
var (
	CustodyActionType_name = map[int32]string{
		0: "CUSTODY_ACTION_TYPE_UNSPECIFIED",
		1: "CUSTODY_ACTION_TYPE_COLLECTED",
		2: "CUSTODY_ACTION_TYPE_TRANSFERRED",
		3: "CUSTODY_ACTION_TYPE_RELEASED",
		4: "CUSTODY_ACTION_TYPE_CHECKED_OUT",
		5: "CUSTODY_ACTION_TYPE_CHECKED_IN",
		6: "CUSTODY_ACTION_TYPE_DISPOSED",
		7: "CUSTODY_ACTION_TYPE_LOGGED",
	}
	CustodyActionType_value = map[string]int32{
		"CUSTODY_ACTION_TYPE_UNSPECIFIED": 0,
		"CUSTODY_ACTION_TYPE_COLLECTED":   1,
		"CUSTODY_ACTION_TYPE_TRANSFERRED": 2,
		"CUSTODY_ACTION_TYPE_RELEASED":    3,
		"CUSTODY_ACTION_TYPE_CHECKED_OUT": 4,
		"CUSTODY_ACTION_TYPE_CHECKED_IN":  5,
		"CUSTODY_ACTION_TYPE_DISPOSED":    6,
		"CUSTODY_ACTION_TYPE_LOGGED":      7,
	}
)

func (x CustodyActionType) Enum() *CustodyActionType {
	p := new(CustodyActionType)
	*p = x
	return p
}

func (x CustodyActionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CustodyActionType) Descriptor() protoreflect.EnumDescriptor {
	return file_hero_property_v1_property_proto_enumTypes[3].Descriptor()
}

func (CustodyActionType) Type() protoreflect.EnumType {
	return &file_hero_property_v1_property_proto_enumTypes[3]
}

func (x CustodyActionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CustodyActionType.Descriptor instead.
func (CustodyActionType) EnumDescriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{3}
}

// SearchOrderBy defines the ordering options for search results
type SearchOrderBy int32

const (
	SearchOrderBy_SEARCH_ORDER_BY_UNSPECIFIED SearchOrderBy = 0
	SearchOrderBy_SEARCH_ORDER_BY_RELEVANCE   SearchOrderBy = 1
	SearchOrderBy_SEARCH_ORDER_BY_CREATED_AT  SearchOrderBy = 2
	SearchOrderBy_SEARCH_ORDER_BY_UPDATED_AT  SearchOrderBy = 3
	SearchOrderBy_SEARCH_ORDER_BY_STATUS      SearchOrderBy = 4
)

// Enum value maps for SearchOrderBy.
var (
	SearchOrderBy_name = map[int32]string{
		0: "SEARCH_ORDER_BY_UNSPECIFIED",
		1: "SEARCH_ORDER_BY_RELEVANCE",
		2: "SEARCH_ORDER_BY_CREATED_AT",
		3: "SEARCH_ORDER_BY_UPDATED_AT",
		4: "SEARCH_ORDER_BY_STATUS",
	}
	SearchOrderBy_value = map[string]int32{
		"SEARCH_ORDER_BY_UNSPECIFIED": 0,
		"SEARCH_ORDER_BY_RELEVANCE":   1,
		"SEARCH_ORDER_BY_CREATED_AT":  2,
		"SEARCH_ORDER_BY_UPDATED_AT":  3,
		"SEARCH_ORDER_BY_STATUS":      4,
	}
)

func (x SearchOrderBy) Enum() *SearchOrderBy {
	p := new(SearchOrderBy)
	*p = x
	return p
}

func (x SearchOrderBy) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SearchOrderBy) Descriptor() protoreflect.EnumDescriptor {
	return file_hero_property_v1_property_proto_enumTypes[4].Descriptor()
}

func (SearchOrderBy) Type() protoreflect.EnumType {
	return &file_hero_property_v1_property_proto_enumTypes[4]
}

func (x SearchOrderBy) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SearchOrderBy.Descriptor instead.
func (SearchOrderBy) EnumDescriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{4}
}

// CustodyEvent represents a single chain of custody entry
type CustodyEvent struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	Timestamp          string                 `protobuf:"bytes,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`                                                              // ISO8601 timestamp of the custody change
	TransferringUserId string                 `protobuf:"bytes,2,opt,name=transferring_user_id,json=transferringUserId,proto3" json:"transferring_user_id,omitempty"`                // User/Officer transferring the item
	TransferringAgency string                 `protobuf:"bytes,3,opt,name=transferring_agency,json=transferringAgency,proto3" json:"transferring_agency,omitempty"`                  // Agency transferring the item
	ReceivingUserId    string                 `protobuf:"bytes,4,opt,name=receiving_user_id,json=receivingUserId,proto3" json:"receiving_user_id,omitempty"`                         // User/Officer/Agency receiving the item (optional for disposal)
	ReceivingAgency    string                 `protobuf:"bytes,5,opt,name=receiving_agency,json=receivingAgency,proto3" json:"receiving_agency,omitempty"`                           // Agency receiving the item (if different from current org)
	NewLocation        string                 `protobuf:"bytes,6,opt,name=new_location,json=newLocation,proto3" json:"new_location,omitempty"`                                       // New location (e.g., "Evidence Room A-12", "Lab XYZ")
	ActionType         CustodyActionType      `protobuf:"varint,7,opt,name=action_type,json=actionType,proto3,enum=hero.property.v1.CustodyActionType" json:"action_type,omitempty"` // Type of custody action
	Notes              string                 `protobuf:"bytes,8,opt,name=notes,proto3" json:"notes,omitempty"`                                                                      // Reason/purpose for the change
	CaseNumber         string                 `protobuf:"bytes,9,opt,name=case_number,json=caseNumber,proto3" json:"case_number,omitempty"`                                          // Associated case number
	EvidenceNumber     string                 `protobuf:"bytes,10,opt,name=evidence_number,json=evidenceNumber,proto3" json:"evidence_number,omitempty"`                             // Evidence tracking number
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *CustodyEvent) Reset() {
	*x = CustodyEvent{}
	mi := &file_hero_property_v1_property_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustodyEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustodyEvent) ProtoMessage() {}

func (x *CustodyEvent) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustodyEvent.ProtoReflect.Descriptor instead.
func (*CustodyEvent) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{0}
}

func (x *CustodyEvent) GetTimestamp() string {
	if x != nil {
		return x.Timestamp
	}
	return ""
}

func (x *CustodyEvent) GetTransferringUserId() string {
	if x != nil {
		return x.TransferringUserId
	}
	return ""
}

func (x *CustodyEvent) GetTransferringAgency() string {
	if x != nil {
		return x.TransferringAgency
	}
	return ""
}

func (x *CustodyEvent) GetReceivingUserId() string {
	if x != nil {
		return x.ReceivingUserId
	}
	return ""
}

func (x *CustodyEvent) GetReceivingAgency() string {
	if x != nil {
		return x.ReceivingAgency
	}
	return ""
}

func (x *CustodyEvent) GetNewLocation() string {
	if x != nil {
		return x.NewLocation
	}
	return ""
}

func (x *CustodyEvent) GetActionType() CustodyActionType {
	if x != nil {
		return x.ActionType
	}
	return CustodyActionType_CUSTODY_ACTION_TYPE_UNSPECIFIED
}

func (x *CustodyEvent) GetNotes() string {
	if x != nil {
		return x.Notes
	}
	return ""
}

func (x *CustodyEvent) GetCaseNumber() string {
	if x != nil {
		return x.CaseNumber
	}
	return ""
}

func (x *CustodyEvent) GetEvidenceNumber() string {
	if x != nil {
		return x.EvidenceNumber
	}
	return ""
}

// PropertySchema contains detailed property information fields
type PropertySchema struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Description   string                 `protobuf:"bytes,1,opt,name=description,proto3" json:"description,omitempty"`                                                           // Detailed description of the property
	Quantity      string                 `protobuf:"bytes,2,opt,name=quantity,proto3" json:"quantity,omitempty"`                                                                 // Quantity of items
	Category      string                 `protobuf:"bytes,3,opt,name=category,proto3" json:"category,omitempty"`                                                                 // Category classification
	Identifiers   string                 `protobuf:"bytes,4,opt,name=identifiers,proto3" json:"identifiers,omitempty"`                                                           // Make, model, or brand information
	Owner         string                 `protobuf:"bytes,5,opt,name=owner,proto3" json:"owner,omitempty"`                                                                       // Owner information if applicable
	Condition     string                 `protobuf:"bytes,6,opt,name=condition,proto3" json:"condition,omitempty"`                                                               // Condition of the property
	SerialNumber  string                 `protobuf:"bytes,7,opt,name=serial_number,json=serialNumber,proto3" json:"serial_number,omitempty"`                                     // Serial number or unique identifier
	Value         string                 `protobuf:"bytes,8,opt,name=value,proto3" json:"value,omitempty"`                                                                       // Monetary value of the property
	PropertyType  PropertyType           `protobuf:"varint,9,opt,name=property_type,json=propertyType,proto3,enum=hero.property.v1.PropertyType" json:"property_type,omitempty"` // Type of property (moved from Property struct)
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PropertySchema) Reset() {
	*x = PropertySchema{}
	mi := &file_hero_property_v1_property_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PropertySchema) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PropertySchema) ProtoMessage() {}

func (x *PropertySchema) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PropertySchema.ProtoReflect.Descriptor instead.
func (*PropertySchema) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{1}
}

func (x *PropertySchema) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *PropertySchema) GetQuantity() string {
	if x != nil {
		return x.Quantity
	}
	return ""
}

func (x *PropertySchema) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *PropertySchema) GetIdentifiers() string {
	if x != nil {
		return x.Identifiers
	}
	return ""
}

func (x *PropertySchema) GetOwner() string {
	if x != nil {
		return x.Owner
	}
	return ""
}

func (x *PropertySchema) GetCondition() string {
	if x != nil {
		return x.Condition
	}
	return ""
}

func (x *PropertySchema) GetSerialNumber() string {
	if x != nil {
		return x.SerialNumber
	}
	return ""
}

func (x *PropertySchema) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *PropertySchema) GetPropertyType() PropertyType {
	if x != nil {
		return x.PropertyType
	}
	return PropertyType_PROPERTY_TYPE_UNSPECIFIED
}

// Property represents a property entity
type Property struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Id               string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                                                                                     // Unique identifier for the property
	OrgId            int32                  `protobuf:"varint,2,opt,name=org_id,json=orgId,proto3" json:"org_id,omitempty"`                                                                 // Organization identifier
	PropertyNumber   string                 `protobuf:"bytes,3,opt,name=property_number,json=propertyNumber,proto3" json:"property_number,omitempty"`                                       // Property/Evidence number for tracking
	PropertyStatus   PropertyStatus         `protobuf:"varint,4,opt,name=property_status,json=propertyStatus,proto3,enum=hero.property.v1.PropertyStatus" json:"property_status,omitempty"` // Current status of the property
	IsEvidence       bool                   `protobuf:"varint,5,opt,name=is_evidence,json=isEvidence,proto3" json:"is_evidence,omitempty"`                                                  // Flag indicating whether this is evidence
	RetentionPeriod  string                 `protobuf:"bytes,6,opt,name=retention_period,json=retentionPeriod,proto3" json:"retention_period,omitempty"`                                    // Retention period for evidence
	DisposalType     PropertyDisposalType   `protobuf:"varint,7,opt,name=disposal_type,json=disposalType,proto3,enum=hero.property.v1.PropertyDisposalType" json:"disposal_type,omitempty"` // How property was disposed
	Notes            string                 `protobuf:"bytes,8,opt,name=notes,proto3" json:"notes,omitempty"`                                                                               // Additional notes about the property
	CurrentCustodian string                 `protobuf:"bytes,9,opt,name=current_custodian,json=currentCustodian,proto3" json:"current_custodian,omitempty"`                                 // Current person/agency with custody
	CurrentLocation  string                 `protobuf:"bytes,10,opt,name=current_location,json=currentLocation,proto3" json:"current_location,omitempty"`                                   // Current location of the property
	CustodyChain     []*CustodyEvent        `protobuf:"bytes,11,rep,name=custody_chain,json=custodyChain,proto3" json:"custody_chain,omitempty"`                                            // Complete chain of custody history
	PropertySchema   *PropertySchema        `protobuf:"bytes,12,opt,name=property_schema,json=propertySchema,proto3" json:"property_schema,omitempty"`                                      // Detailed property information
	CreateTime       string                 `protobuf:"bytes,16,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`                                                  // ISO8601 timestamp when the property was created
	UpdateTime       string                 `protobuf:"bytes,17,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`                                                  // ISO8601 timestamp when the property was last updated
	CreatedBy        string                 `protobuf:"bytes,18,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`                                                     // User ID of the creator
	UpdatedBy        string                 `protobuf:"bytes,19,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`                                                     // User ID of the last updater
	Version          int32                  `protobuf:"varint,20,opt,name=version,proto3" json:"version,omitempty"`                                                                         // Version number (for audit/history purposes)
	Status           int32                  `protobuf:"varint,21,opt,name=status,proto3" json:"status,omitempty"`                                                                           // Current operational status
	ResourceType     string                 `protobuf:"bytes,22,opt,name=resource_type,json=resourceType,proto3" json:"resource_type,omitempty"`                                            // Fixed value "PROPERTY"
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *Property) Reset() {
	*x = Property{}
	mi := &file_hero_property_v1_property_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Property) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Property) ProtoMessage() {}

func (x *Property) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Property.ProtoReflect.Descriptor instead.
func (*Property) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{2}
}

func (x *Property) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Property) GetOrgId() int32 {
	if x != nil {
		return x.OrgId
	}
	return 0
}

func (x *Property) GetPropertyNumber() string {
	if x != nil {
		return x.PropertyNumber
	}
	return ""
}

func (x *Property) GetPropertyStatus() PropertyStatus {
	if x != nil {
		return x.PropertyStatus
	}
	return PropertyStatus_PROPERTY_STATUS_UNSPECIFIED
}

func (x *Property) GetIsEvidence() bool {
	if x != nil {
		return x.IsEvidence
	}
	return false
}

func (x *Property) GetRetentionPeriod() string {
	if x != nil {
		return x.RetentionPeriod
	}
	return ""
}

func (x *Property) GetDisposalType() PropertyDisposalType {
	if x != nil {
		return x.DisposalType
	}
	return PropertyDisposalType_PROPERTY_DISPOSAL_TYPE_UNSPECIFIED
}

func (x *Property) GetNotes() string {
	if x != nil {
		return x.Notes
	}
	return ""
}

func (x *Property) GetCurrentCustodian() string {
	if x != nil {
		return x.CurrentCustodian
	}
	return ""
}

func (x *Property) GetCurrentLocation() string {
	if x != nil {
		return x.CurrentLocation
	}
	return ""
}

func (x *Property) GetCustodyChain() []*CustodyEvent {
	if x != nil {
		return x.CustodyChain
	}
	return nil
}

func (x *Property) GetPropertySchema() *PropertySchema {
	if x != nil {
		return x.PropertySchema
	}
	return nil
}

func (x *Property) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *Property) GetUpdateTime() string {
	if x != nil {
		return x.UpdateTime
	}
	return ""
}

func (x *Property) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *Property) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *Property) GetVersion() int32 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *Property) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *Property) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

// DateRange represents a time range for filtering search results
type DateRange struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	From          string                 `protobuf:"bytes,1,opt,name=from,proto3" json:"from,omitempty"` // Start time in RFC3339 format (inclusive)
	To            string                 `protobuf:"bytes,2,opt,name=to,proto3" json:"to,omitempty"`     // End time in RFC3339 format (inclusive)
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DateRange) Reset() {
	*x = DateRange{}
	mi := &file_hero_property_v1_property_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DateRange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DateRange) ProtoMessage() {}

func (x *DateRange) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DateRange.ProtoReflect.Descriptor instead.
func (*DateRange) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{3}
}

func (x *DateRange) GetFrom() string {
	if x != nil {
		return x.From
	}
	return ""
}

func (x *DateRange) GetTo() string {
	if x != nil {
		return x.To
	}
	return ""
}

// FieldQuery represents a field-specific search query
type FieldQuery struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Field         string                 `protobuf:"bytes,1,opt,name=field,proto3" json:"field,omitempty"` // Field to search in (id, notes, location, current_custodian, current_location)
	Query         string                 `protobuf:"bytes,2,opt,name=query,proto3" json:"query,omitempty"` // Search term for this specific field
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FieldQuery) Reset() {
	*x = FieldQuery{}
	mi := &file_hero_property_v1_property_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FieldQuery) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FieldQuery) ProtoMessage() {}

func (x *FieldQuery) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FieldQuery.ProtoReflect.Descriptor instead.
func (*FieldQuery) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{4}
}

func (x *FieldQuery) GetField() string {
	if x != nil {
		return x.Field
	}
	return ""
}

func (x *FieldQuery) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

// HighlightResult represents highlighted search results for a property
type HighlightResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Field         string                 `protobuf:"bytes,1,opt,name=field,proto3" json:"field,omitempty"`         // Field name that had a match
	Fragments     []string               `protobuf:"bytes,2,rep,name=fragments,proto3" json:"fragments,omitempty"` // Highlighted fragments with matched terms
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HighlightResult) Reset() {
	*x = HighlightResult{}
	mi := &file_hero_property_v1_property_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HighlightResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HighlightResult) ProtoMessage() {}

func (x *HighlightResult) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HighlightResult.ProtoReflect.Descriptor instead.
func (*HighlightResult) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{5}
}

func (x *HighlightResult) GetField() string {
	if x != nil {
		return x.Field
	}
	return ""
}

func (x *HighlightResult) GetFragments() []string {
	if x != nil {
		return x.Fragments
	}
	return nil
}

// Request/Response Messages for CreateProperty
type CreatePropertyRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Property      *Property              `protobuf:"bytes,1,opt,name=property,proto3" json:"property,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreatePropertyRequest) Reset() {
	*x = CreatePropertyRequest{}
	mi := &file_hero_property_v1_property_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreatePropertyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePropertyRequest) ProtoMessage() {}

func (x *CreatePropertyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePropertyRequest.ProtoReflect.Descriptor instead.
func (*CreatePropertyRequest) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{6}
}

func (x *CreatePropertyRequest) GetProperty() *Property {
	if x != nil {
		return x.Property
	}
	return nil
}

type CreatePropertyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Property      *Property              `protobuf:"bytes,1,opt,name=property,proto3" json:"property,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreatePropertyResponse) Reset() {
	*x = CreatePropertyResponse{}
	mi := &file_hero_property_v1_property_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreatePropertyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePropertyResponse) ProtoMessage() {}

func (x *CreatePropertyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePropertyResponse.ProtoReflect.Descriptor instead.
func (*CreatePropertyResponse) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{7}
}

func (x *CreatePropertyResponse) GetProperty() *Property {
	if x != nil {
		return x.Property
	}
	return nil
}

// Request/Response Messages for GetProperty
type GetPropertyRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPropertyRequest) Reset() {
	*x = GetPropertyRequest{}
	mi := &file_hero_property_v1_property_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPropertyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPropertyRequest) ProtoMessage() {}

func (x *GetPropertyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPropertyRequest.ProtoReflect.Descriptor instead.
func (*GetPropertyRequest) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{8}
}

func (x *GetPropertyRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type GetPropertyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Property      *Property              `protobuf:"bytes,1,opt,name=property,proto3" json:"property,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPropertyResponse) Reset() {
	*x = GetPropertyResponse{}
	mi := &file_hero_property_v1_property_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPropertyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPropertyResponse) ProtoMessage() {}

func (x *GetPropertyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPropertyResponse.ProtoReflect.Descriptor instead.
func (*GetPropertyResponse) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{9}
}

func (x *GetPropertyResponse) GetProperty() *Property {
	if x != nil {
		return x.Property
	}
	return nil
}

// Request/Response Messages for ListProperties
type ListPropertiesRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	PageSize       int32                  `protobuf:"varint,1,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	PageToken      string                 `protobuf:"bytes,2,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	PropertyType   PropertyType           `protobuf:"varint,3,opt,name=property_type,json=propertyType,proto3,enum=hero.property.v1.PropertyType" json:"property_type,omitempty"`
	PropertyStatus PropertyStatus         `protobuf:"varint,4,opt,name=property_status,json=propertyStatus,proto3,enum=hero.property.v1.PropertyStatus" json:"property_status,omitempty"`
	OrderBy        string                 `protobuf:"bytes,5,opt,name=order_by,json=orderBy,proto3" json:"order_by,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ListPropertiesRequest) Reset() {
	*x = ListPropertiesRequest{}
	mi := &file_hero_property_v1_property_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListPropertiesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPropertiesRequest) ProtoMessage() {}

func (x *ListPropertiesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPropertiesRequest.ProtoReflect.Descriptor instead.
func (*ListPropertiesRequest) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{10}
}

func (x *ListPropertiesRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListPropertiesRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListPropertiesRequest) GetPropertyType() PropertyType {
	if x != nil {
		return x.PropertyType
	}
	return PropertyType_PROPERTY_TYPE_UNSPECIFIED
}

func (x *ListPropertiesRequest) GetPropertyStatus() PropertyStatus {
	if x != nil {
		return x.PropertyStatus
	}
	return PropertyStatus_PROPERTY_STATUS_UNSPECIFIED
}

func (x *ListPropertiesRequest) GetOrderBy() string {
	if x != nil {
		return x.OrderBy
	}
	return ""
}

type ListPropertiesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Properties    []*Property            `protobuf:"bytes,1,rep,name=properties,proto3" json:"properties,omitempty"`
	NextPageToken string                 `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListPropertiesResponse) Reset() {
	*x = ListPropertiesResponse{}
	mi := &file_hero_property_v1_property_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListPropertiesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPropertiesResponse) ProtoMessage() {}

func (x *ListPropertiesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPropertiesResponse.ProtoReflect.Descriptor instead.
func (*ListPropertiesResponse) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{11}
}

func (x *ListPropertiesResponse) GetProperties() []*Property {
	if x != nil {
		return x.Properties
	}
	return nil
}

func (x *ListPropertiesResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

// Request/Response Messages for UpdateProperty
type UpdatePropertyRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Property      *Property              `protobuf:"bytes,1,opt,name=property,proto3" json:"property,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdatePropertyRequest) Reset() {
	*x = UpdatePropertyRequest{}
	mi := &file_hero_property_v1_property_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdatePropertyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePropertyRequest) ProtoMessage() {}

func (x *UpdatePropertyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePropertyRequest.ProtoReflect.Descriptor instead.
func (*UpdatePropertyRequest) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{12}
}

func (x *UpdatePropertyRequest) GetProperty() *Property {
	if x != nil {
		return x.Property
	}
	return nil
}

type UpdatePropertyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Property      *Property              `protobuf:"bytes,1,opt,name=property,proto3" json:"property,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdatePropertyResponse) Reset() {
	*x = UpdatePropertyResponse{}
	mi := &file_hero_property_v1_property_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdatePropertyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePropertyResponse) ProtoMessage() {}

func (x *UpdatePropertyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePropertyResponse.ProtoReflect.Descriptor instead.
func (*UpdatePropertyResponse) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{13}
}

func (x *UpdatePropertyResponse) GetProperty() *Property {
	if x != nil {
		return x.Property
	}
	return nil
}

// Request/Response Messages for DeleteProperty
type DeletePropertyRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeletePropertyRequest) Reset() {
	*x = DeletePropertyRequest{}
	mi := &file_hero_property_v1_property_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeletePropertyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePropertyRequest) ProtoMessage() {}

func (x *DeletePropertyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePropertyRequest.ProtoReflect.Descriptor instead.
func (*DeletePropertyRequest) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{14}
}

func (x *DeletePropertyRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type DeletePropertyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeletePropertyResponse) Reset() {
	*x = DeletePropertyResponse{}
	mi := &file_hero_property_v1_property_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeletePropertyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePropertyResponse) ProtoMessage() {}

func (x *DeletePropertyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePropertyResponse.ProtoReflect.Descriptor instead.
func (*DeletePropertyResponse) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{15}
}

// Request/Response Messages for SearchProperties
type SearchPropertiesRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Query          string                 `protobuf:"bytes,1,opt,name=query,proto3" json:"query,omitempty"`
	FieldQueries   []*FieldQuery          `protobuf:"bytes,2,rep,name=field_queries,json=fieldQueries,proto3" json:"field_queries,omitempty"`
	DateRange      *DateRange             `protobuf:"bytes,3,opt,name=date_range,json=dateRange,proto3" json:"date_range,omitempty"`
	PropertyType   PropertyType           `protobuf:"varint,4,opt,name=property_type,json=propertyType,proto3,enum=hero.property.v1.PropertyType" json:"property_type,omitempty"`
	PropertyStatus PropertyStatus         `protobuf:"varint,5,opt,name=property_status,json=propertyStatus,proto3,enum=hero.property.v1.PropertyStatus" json:"property_status,omitempty"`
	OrderBy        SearchOrderBy          `protobuf:"varint,6,opt,name=order_by,json=orderBy,proto3,enum=hero.property.v1.SearchOrderBy" json:"order_by,omitempty"`
	PageSize       int32                  `protobuf:"varint,7,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	PageToken      string                 `protobuf:"bytes,8,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *SearchPropertiesRequest) Reset() {
	*x = SearchPropertiesRequest{}
	mi := &file_hero_property_v1_property_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchPropertiesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchPropertiesRequest) ProtoMessage() {}

func (x *SearchPropertiesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchPropertiesRequest.ProtoReflect.Descriptor instead.
func (*SearchPropertiesRequest) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{16}
}

func (x *SearchPropertiesRequest) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *SearchPropertiesRequest) GetFieldQueries() []*FieldQuery {
	if x != nil {
		return x.FieldQueries
	}
	return nil
}

func (x *SearchPropertiesRequest) GetDateRange() *DateRange {
	if x != nil {
		return x.DateRange
	}
	return nil
}

func (x *SearchPropertiesRequest) GetPropertyType() PropertyType {
	if x != nil {
		return x.PropertyType
	}
	return PropertyType_PROPERTY_TYPE_UNSPECIFIED
}

func (x *SearchPropertiesRequest) GetPropertyStatus() PropertyStatus {
	if x != nil {
		return x.PropertyStatus
	}
	return PropertyStatus_PROPERTY_STATUS_UNSPECIFIED
}

func (x *SearchPropertiesRequest) GetOrderBy() SearchOrderBy {
	if x != nil {
		return x.OrderBy
	}
	return SearchOrderBy_SEARCH_ORDER_BY_UNSPECIFIED
}

func (x *SearchPropertiesRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *SearchPropertiesRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

type SearchPropertiesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Properties    []*Property            `protobuf:"bytes,1,rep,name=properties,proto3" json:"properties,omitempty"`
	Highlights    []*HighlightResult     `protobuf:"bytes,2,rep,name=highlights,proto3" json:"highlights,omitempty"`
	NextPageToken string                 `protobuf:"bytes,3,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	TotalCount    int32                  `protobuf:"varint,4,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchPropertiesResponse) Reset() {
	*x = SearchPropertiesResponse{}
	mi := &file_hero_property_v1_property_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchPropertiesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchPropertiesResponse) ProtoMessage() {}

func (x *SearchPropertiesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchPropertiesResponse.ProtoReflect.Descriptor instead.
func (*SearchPropertiesResponse) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{17}
}

func (x *SearchPropertiesResponse) GetProperties() []*Property {
	if x != nil {
		return x.Properties
	}
	return nil
}

func (x *SearchPropertiesResponse) GetHighlights() []*HighlightResult {
	if x != nil {
		return x.Highlights
	}
	return nil
}

func (x *SearchPropertiesResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *SearchPropertiesResponse) GetTotalCount() int32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

// Request/Response Messages for BatchGetProperties
type BatchGetPropertiesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ids           []string               `protobuf:"bytes,1,rep,name=ids,proto3" json:"ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchGetPropertiesRequest) Reset() {
	*x = BatchGetPropertiesRequest{}
	mi := &file_hero_property_v1_property_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchGetPropertiesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetPropertiesRequest) ProtoMessage() {}

func (x *BatchGetPropertiesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetPropertiesRequest.ProtoReflect.Descriptor instead.
func (*BatchGetPropertiesRequest) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{18}
}

func (x *BatchGetPropertiesRequest) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

type BatchGetPropertiesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Properties    []*Property            `protobuf:"bytes,1,rep,name=properties,proto3" json:"properties,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchGetPropertiesResponse) Reset() {
	*x = BatchGetPropertiesResponse{}
	mi := &file_hero_property_v1_property_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchGetPropertiesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetPropertiesResponse) ProtoMessage() {}

func (x *BatchGetPropertiesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetPropertiesResponse.ProtoReflect.Descriptor instead.
func (*BatchGetPropertiesResponse) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{19}
}

func (x *BatchGetPropertiesResponse) GetProperties() []*Property {
	if x != nil {
		return x.Properties
	}
	return nil
}

// Request/Response Messages for AddCustodyEvent
type AddCustodyEventRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PropertyId    string                 `protobuf:"bytes,1,opt,name=property_id,json=propertyId,proto3" json:"property_id,omitempty"`
	CustodyEvent  *CustodyEvent          `protobuf:"bytes,2,opt,name=custody_event,json=custodyEvent,proto3" json:"custody_event,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddCustodyEventRequest) Reset() {
	*x = AddCustodyEventRequest{}
	mi := &file_hero_property_v1_property_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddCustodyEventRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddCustodyEventRequest) ProtoMessage() {}

func (x *AddCustodyEventRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddCustodyEventRequest.ProtoReflect.Descriptor instead.
func (*AddCustodyEventRequest) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{20}
}

func (x *AddCustodyEventRequest) GetPropertyId() string {
	if x != nil {
		return x.PropertyId
	}
	return ""
}

func (x *AddCustodyEventRequest) GetCustodyEvent() *CustodyEvent {
	if x != nil {
		return x.CustodyEvent
	}
	return nil
}

type AddCustodyEventResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddCustodyEventResponse) Reset() {
	*x = AddCustodyEventResponse{}
	mi := &file_hero_property_v1_property_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddCustodyEventResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddCustodyEventResponse) ProtoMessage() {}

func (x *AddCustodyEventResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddCustodyEventResponse.ProtoReflect.Descriptor instead.
func (*AddCustodyEventResponse) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{21}
}

// Request/Response Messages for GetCustodyChain
type GetCustodyChainRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PropertyId    string                 `protobuf:"bytes,1,opt,name=property_id,json=propertyId,proto3" json:"property_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCustodyChainRequest) Reset() {
	*x = GetCustodyChainRequest{}
	mi := &file_hero_property_v1_property_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCustodyChainRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustodyChainRequest) ProtoMessage() {}

func (x *GetCustodyChainRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustodyChainRequest.ProtoReflect.Descriptor instead.
func (*GetCustodyChainRequest) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{22}
}

func (x *GetCustodyChainRequest) GetPropertyId() string {
	if x != nil {
		return x.PropertyId
	}
	return ""
}

type GetCustodyChainResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CustodyChain  []*CustodyEvent        `protobuf:"bytes,1,rep,name=custody_chain,json=custodyChain,proto3" json:"custody_chain,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCustodyChainResponse) Reset() {
	*x = GetCustodyChainResponse{}
	mi := &file_hero_property_v1_property_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCustodyChainResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustodyChainResponse) ProtoMessage() {}

func (x *GetCustodyChainResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustodyChainResponse.ProtoReflect.Descriptor instead.
func (*GetCustodyChainResponse) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{23}
}

func (x *GetCustodyChainResponse) GetCustodyChain() []*CustodyEvent {
	if x != nil {
		return x.CustodyChain
	}
	return nil
}

// Request/Response Messages for ListPropertyFileAttachments
type ListPropertyFileAttachmentsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PropertyId    string                 `protobuf:"bytes,1,opt,name=property_id,json=propertyId,proto3" json:"property_id,omitempty"`
	FileCategory  string                 `protobuf:"bytes,2,opt,name=file_category,json=fileCategory,proto3" json:"file_category,omitempty"`
	PageSize      int32                  `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	PageToken     string                 `protobuf:"bytes,4,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListPropertyFileAttachmentsRequest) Reset() {
	*x = ListPropertyFileAttachmentsRequest{}
	mi := &file_hero_property_v1_property_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListPropertyFileAttachmentsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPropertyFileAttachmentsRequest) ProtoMessage() {}

func (x *ListPropertyFileAttachmentsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPropertyFileAttachmentsRequest.ProtoReflect.Descriptor instead.
func (*ListPropertyFileAttachmentsRequest) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{24}
}

func (x *ListPropertyFileAttachmentsRequest) GetPropertyId() string {
	if x != nil {
		return x.PropertyId
	}
	return ""
}

func (x *ListPropertyFileAttachmentsRequest) GetFileCategory() string {
	if x != nil {
		return x.FileCategory
	}
	return ""
}

func (x *ListPropertyFileAttachmentsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListPropertyFileAttachmentsRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

type ListPropertyFileAttachmentsResponse struct {
	state           protoimpl.MessageState   `protogen:"open.v1"`
	FileAttachments []*PropertyFileReference `protobuf:"bytes,1,rep,name=file_attachments,json=fileAttachments,proto3" json:"file_attachments,omitempty"`
	NextPageToken   string                   `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ListPropertyFileAttachmentsResponse) Reset() {
	*x = ListPropertyFileAttachmentsResponse{}
	mi := &file_hero_property_v1_property_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListPropertyFileAttachmentsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPropertyFileAttachmentsResponse) ProtoMessage() {}

func (x *ListPropertyFileAttachmentsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPropertyFileAttachmentsResponse.ProtoReflect.Descriptor instead.
func (*ListPropertyFileAttachmentsResponse) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{25}
}

func (x *ListPropertyFileAttachmentsResponse) GetFileAttachments() []*PropertyFileReference {
	if x != nil {
		return x.FileAttachments
	}
	return nil
}

func (x *ListPropertyFileAttachmentsResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

// PropertyFileReference represents a file attachment for a property
type PropertyFileReference struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	PropertyId    string                 `protobuf:"bytes,2,opt,name=property_id,json=propertyId,proto3" json:"property_id,omitempty"`
	FileId        string                 `protobuf:"bytes,3,opt,name=file_id,json=fileId,proto3" json:"file_id,omitempty"`
	Caption       string                 `protobuf:"bytes,4,opt,name=caption,proto3" json:"caption,omitempty"`
	DisplayName   string                 `protobuf:"bytes,5,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	DisplayOrder  int32                  `protobuf:"varint,6,opt,name=display_order,json=displayOrder,proto3" json:"display_order,omitempty"`
	FileCategory  string                 `protobuf:"bytes,7,opt,name=file_category,json=fileCategory,proto3" json:"file_category,omitempty"`
	Metadata      *structpb.Struct       `protobuf:"bytes,8,opt,name=metadata,proto3" json:"metadata,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PropertyFileReference) Reset() {
	*x = PropertyFileReference{}
	mi := &file_hero_property_v1_property_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PropertyFileReference) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PropertyFileReference) ProtoMessage() {}

func (x *PropertyFileReference) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PropertyFileReference.ProtoReflect.Descriptor instead.
func (*PropertyFileReference) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{26}
}

func (x *PropertyFileReference) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *PropertyFileReference) GetPropertyId() string {
	if x != nil {
		return x.PropertyId
	}
	return ""
}

func (x *PropertyFileReference) GetFileId() string {
	if x != nil {
		return x.FileId
	}
	return ""
}

func (x *PropertyFileReference) GetCaption() string {
	if x != nil {
		return x.Caption
	}
	return ""
}

func (x *PropertyFileReference) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *PropertyFileReference) GetDisplayOrder() int32 {
	if x != nil {
		return x.DisplayOrder
	}
	return 0
}

func (x *PropertyFileReference) GetFileCategory() string {
	if x != nil {
		return x.FileCategory
	}
	return ""
}

func (x *PropertyFileReference) GetMetadata() *structpb.Struct {
	if x != nil {
		return x.Metadata
	}
	return nil
}

// Request/Response Messages for AddPropertyFileAttachment
type AddPropertyFileAttachmentRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	PropertyId     string                 `protobuf:"bytes,1,opt,name=property_id,json=propertyId,proto3" json:"property_id,omitempty"`
	FileAttachment *PropertyFileReference `protobuf:"bytes,2,opt,name=file_attachment,json=fileAttachment,proto3" json:"file_attachment,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *AddPropertyFileAttachmentRequest) Reset() {
	*x = AddPropertyFileAttachmentRequest{}
	mi := &file_hero_property_v1_property_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddPropertyFileAttachmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddPropertyFileAttachmentRequest) ProtoMessage() {}

func (x *AddPropertyFileAttachmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddPropertyFileAttachmentRequest.ProtoReflect.Descriptor instead.
func (*AddPropertyFileAttachmentRequest) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{27}
}

func (x *AddPropertyFileAttachmentRequest) GetPropertyId() string {
	if x != nil {
		return x.PropertyId
	}
	return ""
}

func (x *AddPropertyFileAttachmentRequest) GetFileAttachment() *PropertyFileReference {
	if x != nil {
		return x.FileAttachment
	}
	return nil
}

type AddPropertyFileAttachmentResponse struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	FileAttachment *PropertyFileReference `protobuf:"bytes,1,opt,name=file_attachment,json=fileAttachment,proto3" json:"file_attachment,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *AddPropertyFileAttachmentResponse) Reset() {
	*x = AddPropertyFileAttachmentResponse{}
	mi := &file_hero_property_v1_property_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddPropertyFileAttachmentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddPropertyFileAttachmentResponse) ProtoMessage() {}

func (x *AddPropertyFileAttachmentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddPropertyFileAttachmentResponse.ProtoReflect.Descriptor instead.
func (*AddPropertyFileAttachmentResponse) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{28}
}

func (x *AddPropertyFileAttachmentResponse) GetFileAttachment() *PropertyFileReference {
	if x != nil {
		return x.FileAttachment
	}
	return nil
}

// Request/Response Messages for RemovePropertyFileAttachment
type RemovePropertyFileAttachmentRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PropertyId    string                 `protobuf:"bytes,1,opt,name=property_id,json=propertyId,proto3" json:"property_id,omitempty"`
	AttachmentId  string                 `protobuf:"bytes,2,opt,name=attachment_id,json=attachmentId,proto3" json:"attachment_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RemovePropertyFileAttachmentRequest) Reset() {
	*x = RemovePropertyFileAttachmentRequest{}
	mi := &file_hero_property_v1_property_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RemovePropertyFileAttachmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemovePropertyFileAttachmentRequest) ProtoMessage() {}

func (x *RemovePropertyFileAttachmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemovePropertyFileAttachmentRequest.ProtoReflect.Descriptor instead.
func (*RemovePropertyFileAttachmentRequest) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{29}
}

func (x *RemovePropertyFileAttachmentRequest) GetPropertyId() string {
	if x != nil {
		return x.PropertyId
	}
	return ""
}

func (x *RemovePropertyFileAttachmentRequest) GetAttachmentId() string {
	if x != nil {
		return x.AttachmentId
	}
	return ""
}

type RemovePropertyFileAttachmentResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RemovePropertyFileAttachmentResponse) Reset() {
	*x = RemovePropertyFileAttachmentResponse{}
	mi := &file_hero_property_v1_property_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RemovePropertyFileAttachmentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemovePropertyFileAttachmentResponse) ProtoMessage() {}

func (x *RemovePropertyFileAttachmentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemovePropertyFileAttachmentResponse.ProtoReflect.Descriptor instead.
func (*RemovePropertyFileAttachmentResponse) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{30}
}

var File_hero_property_v1_property_proto protoreflect.FileDescriptor

var file_hero_property_v1_property_proto_rawDesc = []byte{
	0x0a, 0x1f, 0x68, 0x65, 0x72, 0x6f, 0x2f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2f,
	0x76, 0x31, 0x2f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x10, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79,
	0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0xaf, 0x03, 0x0a, 0x0c, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x64, 0x79, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x12, 0x30, 0x0a, 0x14, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x72, 0x69, 0x6e, 0x67,
	0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x55, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x2f, 0x0a, 0x13, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x72, 0x69,
	0x6e, 0x67, 0x5f, 0x61, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x12, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x41, 0x67, 0x65,
	0x6e, 0x63, 0x79, 0x12, 0x2a, 0x0a, 0x11, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x69, 0x6e, 0x67,
	0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x69, 0x6e, 0x67, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x29, 0x0a, 0x10, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x67, 0x65,
	0x6e, 0x63, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x72, 0x65, 0x63, 0x65, 0x69,
	0x76, 0x69, 0x6e, 0x67, 0x41, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x21, 0x0a, 0x0c, 0x6e, 0x65,
	0x77, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x6e, 0x65, 0x77, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x44, 0x0a,
	0x0b, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x23, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x64, 0x79, 0x41, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x73,
	0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x63, 0x61, 0x73, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x27, 0x0a, 0x0f, 0x65, 0x76,
	0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x65, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x22, 0xc0, 0x02, 0x0a, 0x0e, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79,
	0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x71, 0x75, 0x61, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x71, 0x75, 0x61, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x12, 0x20, 0x0a, 0x0b, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x73, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65,
	0x72, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6e,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73,
	0x65, 0x72, 0x69, 0x61, 0x6c, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x12, 0x43, 0x0a, 0x0d, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e,
	0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x70,
	0x65, 0x72, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x22, 0x93, 0x06, 0x0a, 0x08, 0x50, 0x72, 0x6f, 0x70, 0x65,
	0x72, 0x74, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x6f, 0x72, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x6f, 0x72, 0x67, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x70, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x12, 0x49, 0x0a, 0x0f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x68,
	0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e,
	0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0e,
	0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f,
	0x0a, 0x0b, 0x69, 0x73, 0x5f, 0x65, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0a, 0x69, 0x73, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x12,
	0x29, 0x0a, 0x10, 0x72, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x65, 0x72,
	0x69, 0x6f, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x72, 0x65, 0x74, 0x65, 0x6e,
	0x74, 0x69, 0x6f, 0x6e, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x12, 0x4b, 0x0a, 0x0d, 0x64, 0x69,
	0x73, 0x70, 0x6f, 0x73, 0x61, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x26, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x79, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x44, 0x69, 0x73,
	0x70, 0x6f, 0x73, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6f,
	0x73, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x12, 0x2b, 0x0a,
	0x11, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x64, 0x69,
	0x61, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x64, 0x69, 0x61, 0x6e, 0x12, 0x29, 0x0a, 0x10, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x4c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x43, 0x0a, 0x0d, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x64, 0x79,
	0x5f, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x68,
	0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x64, 0x79, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x0c, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x64, 0x79, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x49, 0x0a, 0x0f, 0x70, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65,
	0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x53,
	0x63, 0x68, 0x65, 0x6d, 0x61, 0x52, 0x0e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x53,
	0x63, 0x68, 0x65, 0x6d, 0x61, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x62, 0x79, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x14, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x15, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x22, 0x2f, 0x0a, 0x09,
	0x44, 0x61, 0x74, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x72, 0x6f,
	0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x12, 0x0e, 0x0a,
	0x02, 0x74, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x74, 0x6f, 0x22, 0x38, 0x0a,
	0x0a, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x66,
	0x69, 0x65, 0x6c, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x66, 0x69, 0x65, 0x6c,
	0x64, 0x12, 0x14, 0x0a, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x22, 0x45, 0x0a, 0x0f, 0x48, 0x69, 0x67, 0x68, 0x6c,
	0x69, 0x67, 0x68, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x66, 0x69,
	0x65, 0x6c, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x66, 0x69, 0x65, 0x6c, 0x64,
	0x12, 0x1c, 0x0a, 0x09, 0x66, 0x72, 0x61, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x09, 0x66, 0x72, 0x61, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x22, 0x4f,
	0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x36, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x70, 0x65,
	0x72, 0x74, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x68, 0x65, 0x72, 0x6f,
	0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x22,
	0x50, 0x0a, 0x16, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x36, 0x0a, 0x08, 0x70, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x68, 0x65,
	0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x50,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x79, 0x22, 0x24, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x4d, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x50, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x36,
	0x0a, 0x08, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79,
	0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x08, 0x70, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x22, 0xfe, 0x01, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x50,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x43, 0x0a, 0x0d,
	0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65,
	0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x0c, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x49, 0x0a, 0x0f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x68, 0x65, 0x72,
	0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0e, 0x70, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x19, 0x0a, 0x08,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x22, 0x7c, 0x0a, 0x16, 0x4c, 0x69, 0x73, 0x74, 0x50,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x3a, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x79, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x12, 0x26, 0x0a,
	0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50, 0x61, 0x67, 0x65,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x4f, 0x0a, 0x15, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x36,
	0x0a, 0x08, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79,
	0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x08, 0x70, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x22, 0x50, 0x0a, 0x16, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x36, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x08,
	0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x22, 0x27, 0x0a, 0x15, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x22, 0x18, 0x0a, 0x16, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65,
	0x72, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xb6, 0x03, 0x0a, 0x17,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x12, 0x41, 0x0a,
	0x0d, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x71, 0x75, 0x65, 0x72, 0x69, 0x65, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70,
	0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x52, 0x0c, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x51, 0x75, 0x65, 0x72, 0x69, 0x65, 0x73,
	0x12, 0x3a, 0x0a, 0x0a, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70,
	0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x61, 0x6e, 0x67,
	0x65, 0x52, 0x09, 0x64, 0x61, 0x74, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x43, 0x0a, 0x0d,
	0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65,
	0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x0c, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x49, 0x0a, 0x0f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x68, 0x65, 0x72,
	0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0e, 0x70, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3a, 0x0a, 0x08,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f,
	0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x52,
	0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65,
	0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67,
	0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x22, 0xe2, 0x01, 0x0a, 0x18, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x50,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x3a, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x79, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x12, 0x41, 0x0a,
	0x0a, 0x68, 0x69, 0x67, 0x68, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x79, 0x2e, 0x76, 0x31, 0x2e, 0x48, 0x69, 0x67, 0x68, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x52, 0x0a, 0x68, 0x69, 0x67, 0x68, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x73,
	0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50,
	0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x2d, 0x0a, 0x19, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0x58, 0x0a, 0x1a, 0x42, 0x61, 0x74, 0x63,
	0x68, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3a, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x68, 0x65, 0x72,
	0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69,
	0x65, 0x73, 0x22, 0x7e, 0x0a, 0x16, 0x41, 0x64, 0x64, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x64, 0x79,
	0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b,
	0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x49, 0x64, 0x12, 0x43, 0x0a,
	0x0d, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x64, 0x79, 0x5f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70,
	0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x64, 0x79, 0x45,
	0x76, 0x65, 0x6e, 0x74, 0x52, 0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x64, 0x79, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x22, 0x19, 0x0a, 0x17, 0x41, 0x64, 0x64, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x64, 0x79,
	0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x39, 0x0a,
	0x16, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x64, 0x79, 0x43, 0x68, 0x61, 0x69, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x70, 0x65,
	0x72, 0x74, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x49, 0x64, 0x22, 0x5e, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x64, 0x79, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x43, 0x0a, 0x0d, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x64, 0x79, 0x5f, 0x63,
	0x68, 0x61, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x68, 0x65, 0x72,
	0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x64, 0x79, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x0c, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x64, 0x79, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x22, 0xa6, 0x01, 0x0a, 0x22, 0x4c, 0x69, 0x73,
	0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74,
	0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x1f, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x49, 0x64,
	0x12, 0x23, 0x0a, 0x0d, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x66, 0x69, 0x6c, 0x65, 0x43, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69,
	0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69,
	0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x22, 0xa1, 0x01, 0x0a, 0x23, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x79, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x52, 0x0a, 0x10, 0x66, 0x69, 0x6c,
	0x65, 0x5f, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65,
	0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x46,
	0x69, 0x6c, 0x65, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x0f, 0x66, 0x69,
	0x6c, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x26, 0x0a,
	0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50, 0x61, 0x67, 0x65,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x9d, 0x02, 0x0a, 0x15, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x79, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x1f, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x49, 0x64,
	0x12, 0x17, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x61, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x61, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61,
	0x79, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x64,
	0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x23, 0x0a, 0x0d, 0x66,
	0x69, 0x6c, 0x65, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x66, 0x69, 0x6c, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x12, 0x33, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x08, 0x6d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x22, 0x95, 0x01, 0x0a, 0x20, 0x41, 0x64, 0x64, 0x50, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x79, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x49, 0x64, 0x12, 0x50, 0x0a, 0x0f, 0x66,
	0x69, 0x6c, 0x65, 0x5f, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70,
	0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79,
	0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x0e, 0x66,
	0x69, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0x75, 0x0a,
	0x21, 0x41, 0x64, 0x64, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x46, 0x69, 0x6c, 0x65,
	0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x50, 0x0a, 0x0f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x61, 0x74, 0x74, 0x61, 0x63,
	0x68, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x68, 0x65,
	0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x50,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x52, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68,
	0x6d, 0x65, 0x6e, 0x74, 0x22, 0x6b, 0x0a, 0x23, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x50, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x70,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d,
	0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x49,
	0x64, 0x22, 0x26, 0x0a, 0x24, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65,
	0x72, 0x74, 0x79, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2a, 0xd1, 0x01, 0x0a, 0x0c, 0x50, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x19, 0x50, 0x52,
	0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x50, 0x52, 0x4f,
	0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44,
	0x10, 0x01, 0x12, 0x18, 0x0a, 0x14, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x53, 0x45, 0x49, 0x5a, 0x45, 0x44, 0x10, 0x02, 0x12, 0x18, 0x0a, 0x14,
	0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x54,
	0x4f, 0x4c, 0x45, 0x4e, 0x10, 0x03, 0x12, 0x1d, 0x0a, 0x19, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52,
	0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x41, 0x46, 0x45, 0x4b, 0x45, 0x45, 0x50,
	0x49, 0x4e, 0x47, 0x10, 0x04, 0x12, 0x19, 0x0a, 0x15, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54,
	0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4e, 0x47, 0x10, 0x05,
	0x12, 0x1b, 0x0a, 0x17, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x52, 0x45, 0x43, 0x4f, 0x56, 0x45, 0x52, 0x45, 0x44, 0x10, 0x06, 0x2a, 0xe9, 0x01,
	0x0a, 0x0e, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x1f, 0x0a, 0x1b, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x1d, 0x0a, 0x19, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x4f, 0x4c, 0x4c, 0x45, 0x43, 0x54, 0x45, 0x44, 0x10, 0x01,
	0x12, 0x1e, 0x0a, 0x1a, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x44, 0x59, 0x10, 0x02,
	0x12, 0x1f, 0x0a, 0x1b, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x45, 0x44, 0x5f, 0x4f, 0x55, 0x54, 0x10,
	0x03, 0x12, 0x1c, 0x0a, 0x18, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x4f, 0x53, 0x45, 0x44, 0x10, 0x04, 0x12,
	0x1b, 0x0a, 0x17, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4e, 0x47, 0x10, 0x05, 0x12, 0x1b, 0x0a, 0x17,
	0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x43, 0x4c, 0x41, 0x49, 0x4d, 0x45, 0x44, 0x10, 0x06, 0x2a, 0x81, 0x02, 0x0a, 0x14, 0x50, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x44, 0x69, 0x73, 0x70, 0x6f, 0x73, 0x61, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x26, 0x0a, 0x22, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x44,
	0x49, 0x53, 0x50, 0x4f, 0x53, 0x41, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x23, 0x0a, 0x1f, 0x50, 0x52,
	0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x4f, 0x53, 0x41, 0x4c, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x45, 0x4c, 0x45, 0x41, 0x53, 0x45, 0x44, 0x10, 0x01, 0x12,
	0x24, 0x0a, 0x20, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x44, 0x49, 0x53, 0x50,
	0x4f, 0x53, 0x41, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x45, 0x53, 0x54, 0x52, 0x4f,
	0x59, 0x45, 0x44, 0x10, 0x02, 0x12, 0x24, 0x0a, 0x20, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54,
	0x59, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x4f, 0x53, 0x41, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x41, 0x55, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x45, 0x44, 0x10, 0x03, 0x12, 0x28, 0x0a, 0x24, 0x50,
	0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x4f, 0x53, 0x41, 0x4c,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x47, 0x45, 0x4e, 0x43, 0x59, 0x5f, 0x52, 0x45, 0x54,
	0x41, 0x49, 0x4e, 0x10, 0x04, 0x12, 0x26, 0x0a, 0x22, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54,
	0x59, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x4f, 0x53, 0x41, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x54, 0x52, 0x41, 0x4e, 0x53, 0x46, 0x45, 0x52, 0x52, 0x45, 0x44, 0x10, 0x05, 0x2a, 0xad, 0x02,
	0x0a, 0x11, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x64, 0x79, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x23, 0x0a, 0x1f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x44, 0x59, 0x5f, 0x41,
	0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x21, 0x0a, 0x1d, 0x43, 0x55, 0x53, 0x54,
	0x4f, 0x44, 0x59, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x43, 0x4f, 0x4c, 0x4c, 0x45, 0x43, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x23, 0x0a, 0x1f, 0x43,
	0x55, 0x53, 0x54, 0x4f, 0x44, 0x59, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x46, 0x45, 0x52, 0x52, 0x45, 0x44, 0x10, 0x02,
	0x12, 0x20, 0x0a, 0x1c, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x44, 0x59, 0x5f, 0x41, 0x43, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x45, 0x4c, 0x45, 0x41, 0x53, 0x45, 0x44,
	0x10, 0x03, 0x12, 0x23, 0x0a, 0x1f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x44, 0x59, 0x5f, 0x41, 0x43,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x45,
	0x44, 0x5f, 0x4f, 0x55, 0x54, 0x10, 0x04, 0x12, 0x22, 0x0a, 0x1e, 0x43, 0x55, 0x53, 0x54, 0x4f,
	0x44, 0x59, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43,
	0x48, 0x45, 0x43, 0x4b, 0x45, 0x44, 0x5f, 0x49, 0x4e, 0x10, 0x05, 0x12, 0x20, 0x0a, 0x1c, 0x43,
	0x55, 0x53, 0x54, 0x4f, 0x44, 0x59, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x4f, 0x53, 0x45, 0x44, 0x10, 0x06, 0x12, 0x1e, 0x0a,
	0x1a, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x44, 0x59, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x4c, 0x4f, 0x47, 0x47, 0x45, 0x44, 0x10, 0x07, 0x2a, 0xab, 0x01,
	0x0a, 0x0d, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x12,
	0x1f, 0x0a, 0x1b, 0x53, 0x45, 0x41, 0x52, 0x43, 0x48, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f,
	0x42, 0x59, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x1d, 0x0a, 0x19, 0x53, 0x45, 0x41, 0x52, 0x43, 0x48, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52,
	0x5f, 0x42, 0x59, 0x5f, 0x52, 0x45, 0x4c, 0x45, 0x56, 0x41, 0x4e, 0x43, 0x45, 0x10, 0x01, 0x12,
	0x1e, 0x0a, 0x1a, 0x53, 0x45, 0x41, 0x52, 0x43, 0x48, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f,
	0x42, 0x59, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x02, 0x12,
	0x1e, 0x0a, 0x1a, 0x53, 0x45, 0x41, 0x52, 0x43, 0x48, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f,
	0x42, 0x59, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x03, 0x12,
	0x1a, 0x0a, 0x16, 0x53, 0x45, 0x41, 0x52, 0x43, 0x48, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f,
	0x42, 0x59, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x04, 0x32, 0xd1, 0x0a, 0x0a, 0x0f,
	0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12,
	0x63, 0x0a, 0x0e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x79, 0x12, 0x27, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x79, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65,
	0x72, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x68, 0x65, 0x72,
	0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5a, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65,
	0x72, 0x74, 0x79, 0x12, 0x24, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65,
	0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x68, 0x65, 0x72, 0x6f,
	0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x63, 0x0a, 0x0e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69,
	0x65, 0x73, 0x12, 0x27, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x68, 0x65,
	0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x63, 0x0a, 0x0e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x12, 0x27, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x28, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79,
	0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x63, 0x0a, 0x0e, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x12, 0x27, 0x2e, 0x68,
	0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x69, 0x0a, 0x10, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x69, 0x65, 0x73, 0x12, 0x29, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65,
	0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x50, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a,
	0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69,
	0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6f, 0x0a, 0x12, 0x42, 0x61,
	0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73,
	0x12, 0x2b, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79,
	0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x70,
	0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e,
	0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31,
	0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x66, 0x0a, 0x0f, 0x41,
	0x64, 0x64, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x64, 0x79, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x28,
	0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76,
	0x31, 0x2e, 0x41, 0x64, 0x64, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x64, 0x79, 0x45, 0x76, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e,
	0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x64, 0x79, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x66, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x64,
	0x79, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x28, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x64, 0x79, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x29, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x64, 0x79, 0x43, 0x68,
	0x61, 0x69, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8a, 0x01, 0x0a, 0x1b,
	0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x46, 0x69, 0x6c, 0x65,
	0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x34, 0x2e, 0x68, 0x65,
	0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x46, 0x69, 0x6c, 0x65, 0x41,
	0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x35, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x79, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x79, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x84, 0x01, 0x0a, 0x19, 0x41, 0x64, 0x64,
	0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x61,
	0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x32, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x50, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x79, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e, 0x68, 0x65, 0x72,
	0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64,
	0x64, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74,
	0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x8d, 0x01, 0x0a, 0x1c, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x79, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74,
	0x12, 0x35, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79,
	0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x79, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76,
	0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74,
	0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42,
	0x21, 0x5a, 0x1f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x68, 0x65, 0x72, 0x6f, 0x2f, 0x70, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2f, 0x76, 0x31, 0x3b, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x79, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_hero_property_v1_property_proto_rawDescOnce sync.Once
	file_hero_property_v1_property_proto_rawDescData = file_hero_property_v1_property_proto_rawDesc
)

func file_hero_property_v1_property_proto_rawDescGZIP() []byte {
	file_hero_property_v1_property_proto_rawDescOnce.Do(func() {
		file_hero_property_v1_property_proto_rawDescData = protoimpl.X.CompressGZIP(file_hero_property_v1_property_proto_rawDescData)
	})
	return file_hero_property_v1_property_proto_rawDescData
}

var file_hero_property_v1_property_proto_enumTypes = make([]protoimpl.EnumInfo, 5)
var file_hero_property_v1_property_proto_msgTypes = make([]protoimpl.MessageInfo, 31)
var file_hero_property_v1_property_proto_goTypes = []any{
	(PropertyType)(0),                            // 0: hero.property.v1.PropertyType
	(PropertyStatus)(0),                          // 1: hero.property.v1.PropertyStatus
	(PropertyDisposalType)(0),                    // 2: hero.property.v1.PropertyDisposalType
	(CustodyActionType)(0),                       // 3: hero.property.v1.CustodyActionType
	(SearchOrderBy)(0),                           // 4: hero.property.v1.SearchOrderBy
	(*CustodyEvent)(nil),                         // 5: hero.property.v1.CustodyEvent
	(*PropertySchema)(nil),                       // 6: hero.property.v1.PropertySchema
	(*Property)(nil),                             // 7: hero.property.v1.Property
	(*DateRange)(nil),                            // 8: hero.property.v1.DateRange
	(*FieldQuery)(nil),                           // 9: hero.property.v1.FieldQuery
	(*HighlightResult)(nil),                      // 10: hero.property.v1.HighlightResult
	(*CreatePropertyRequest)(nil),                // 11: hero.property.v1.CreatePropertyRequest
	(*CreatePropertyResponse)(nil),               // 12: hero.property.v1.CreatePropertyResponse
	(*GetPropertyRequest)(nil),                   // 13: hero.property.v1.GetPropertyRequest
	(*GetPropertyResponse)(nil),                  // 14: hero.property.v1.GetPropertyResponse
	(*ListPropertiesRequest)(nil),                // 15: hero.property.v1.ListPropertiesRequest
	(*ListPropertiesResponse)(nil),               // 16: hero.property.v1.ListPropertiesResponse
	(*UpdatePropertyRequest)(nil),                // 17: hero.property.v1.UpdatePropertyRequest
	(*UpdatePropertyResponse)(nil),               // 18: hero.property.v1.UpdatePropertyResponse
	(*DeletePropertyRequest)(nil),                // 19: hero.property.v1.DeletePropertyRequest
	(*DeletePropertyResponse)(nil),               // 20: hero.property.v1.DeletePropertyResponse
	(*SearchPropertiesRequest)(nil),              // 21: hero.property.v1.SearchPropertiesRequest
	(*SearchPropertiesResponse)(nil),             // 22: hero.property.v1.SearchPropertiesResponse
	(*BatchGetPropertiesRequest)(nil),            // 23: hero.property.v1.BatchGetPropertiesRequest
	(*BatchGetPropertiesResponse)(nil),           // 24: hero.property.v1.BatchGetPropertiesResponse
	(*AddCustodyEventRequest)(nil),               // 25: hero.property.v1.AddCustodyEventRequest
	(*AddCustodyEventResponse)(nil),              // 26: hero.property.v1.AddCustodyEventResponse
	(*GetCustodyChainRequest)(nil),               // 27: hero.property.v1.GetCustodyChainRequest
	(*GetCustodyChainResponse)(nil),              // 28: hero.property.v1.GetCustodyChainResponse
	(*ListPropertyFileAttachmentsRequest)(nil),   // 29: hero.property.v1.ListPropertyFileAttachmentsRequest
	(*ListPropertyFileAttachmentsResponse)(nil),  // 30: hero.property.v1.ListPropertyFileAttachmentsResponse
	(*PropertyFileReference)(nil),                // 31: hero.property.v1.PropertyFileReference
	(*AddPropertyFileAttachmentRequest)(nil),     // 32: hero.property.v1.AddPropertyFileAttachmentRequest
	(*AddPropertyFileAttachmentResponse)(nil),    // 33: hero.property.v1.AddPropertyFileAttachmentResponse
	(*RemovePropertyFileAttachmentRequest)(nil),  // 34: hero.property.v1.RemovePropertyFileAttachmentRequest
	(*RemovePropertyFileAttachmentResponse)(nil), // 35: hero.property.v1.RemovePropertyFileAttachmentResponse
	(*structpb.Struct)(nil),                      // 36: google.protobuf.Struct
}
var file_hero_property_v1_property_proto_depIdxs = []int32{
	3,  // 0: hero.property.v1.CustodyEvent.action_type:type_name -> hero.property.v1.CustodyActionType
	0,  // 1: hero.property.v1.PropertySchema.property_type:type_name -> hero.property.v1.PropertyType
	1,  // 2: hero.property.v1.Property.property_status:type_name -> hero.property.v1.PropertyStatus
	2,  // 3: hero.property.v1.Property.disposal_type:type_name -> hero.property.v1.PropertyDisposalType
	5,  // 4: hero.property.v1.Property.custody_chain:type_name -> hero.property.v1.CustodyEvent
	6,  // 5: hero.property.v1.Property.property_schema:type_name -> hero.property.v1.PropertySchema
	7,  // 6: hero.property.v1.CreatePropertyRequest.property:type_name -> hero.property.v1.Property
	7,  // 7: hero.property.v1.CreatePropertyResponse.property:type_name -> hero.property.v1.Property
	7,  // 8: hero.property.v1.GetPropertyResponse.property:type_name -> hero.property.v1.Property
	0,  // 9: hero.property.v1.ListPropertiesRequest.property_type:type_name -> hero.property.v1.PropertyType
	1,  // 10: hero.property.v1.ListPropertiesRequest.property_status:type_name -> hero.property.v1.PropertyStatus
	7,  // 11: hero.property.v1.ListPropertiesResponse.properties:type_name -> hero.property.v1.Property
	7,  // 12: hero.property.v1.UpdatePropertyRequest.property:type_name -> hero.property.v1.Property
	7,  // 13: hero.property.v1.UpdatePropertyResponse.property:type_name -> hero.property.v1.Property
	9,  // 14: hero.property.v1.SearchPropertiesRequest.field_queries:type_name -> hero.property.v1.FieldQuery
	8,  // 15: hero.property.v1.SearchPropertiesRequest.date_range:type_name -> hero.property.v1.DateRange
	0,  // 16: hero.property.v1.SearchPropertiesRequest.property_type:type_name -> hero.property.v1.PropertyType
	1,  // 17: hero.property.v1.SearchPropertiesRequest.property_status:type_name -> hero.property.v1.PropertyStatus
	4,  // 18: hero.property.v1.SearchPropertiesRequest.order_by:type_name -> hero.property.v1.SearchOrderBy
	7,  // 19: hero.property.v1.SearchPropertiesResponse.properties:type_name -> hero.property.v1.Property
	10, // 20: hero.property.v1.SearchPropertiesResponse.highlights:type_name -> hero.property.v1.HighlightResult
	7,  // 21: hero.property.v1.BatchGetPropertiesResponse.properties:type_name -> hero.property.v1.Property
	5,  // 22: hero.property.v1.AddCustodyEventRequest.custody_event:type_name -> hero.property.v1.CustodyEvent
	5,  // 23: hero.property.v1.GetCustodyChainResponse.custody_chain:type_name -> hero.property.v1.CustodyEvent
	31, // 24: hero.property.v1.ListPropertyFileAttachmentsResponse.file_attachments:type_name -> hero.property.v1.PropertyFileReference
	36, // 25: hero.property.v1.PropertyFileReference.metadata:type_name -> google.protobuf.Struct
	31, // 26: hero.property.v1.AddPropertyFileAttachmentRequest.file_attachment:type_name -> hero.property.v1.PropertyFileReference
	31, // 27: hero.property.v1.AddPropertyFileAttachmentResponse.file_attachment:type_name -> hero.property.v1.PropertyFileReference
	11, // 28: hero.property.v1.PropertyService.CreateProperty:input_type -> hero.property.v1.CreatePropertyRequest
	13, // 29: hero.property.v1.PropertyService.GetProperty:input_type -> hero.property.v1.GetPropertyRequest
	15, // 30: hero.property.v1.PropertyService.ListProperties:input_type -> hero.property.v1.ListPropertiesRequest
	17, // 31: hero.property.v1.PropertyService.UpdateProperty:input_type -> hero.property.v1.UpdatePropertyRequest
	19, // 32: hero.property.v1.PropertyService.DeleteProperty:input_type -> hero.property.v1.DeletePropertyRequest
	21, // 33: hero.property.v1.PropertyService.SearchProperties:input_type -> hero.property.v1.SearchPropertiesRequest
	23, // 34: hero.property.v1.PropertyService.BatchGetProperties:input_type -> hero.property.v1.BatchGetPropertiesRequest
	25, // 35: hero.property.v1.PropertyService.AddCustodyEvent:input_type -> hero.property.v1.AddCustodyEventRequest
	27, // 36: hero.property.v1.PropertyService.GetCustodyChain:input_type -> hero.property.v1.GetCustodyChainRequest
	29, // 37: hero.property.v1.PropertyService.ListPropertyFileAttachments:input_type -> hero.property.v1.ListPropertyFileAttachmentsRequest
	32, // 38: hero.property.v1.PropertyService.AddPropertyFileAttachment:input_type -> hero.property.v1.AddPropertyFileAttachmentRequest
	34, // 39: hero.property.v1.PropertyService.RemovePropertyFileAttachment:input_type -> hero.property.v1.RemovePropertyFileAttachmentRequest
	12, // 40: hero.property.v1.PropertyService.CreateProperty:output_type -> hero.property.v1.CreatePropertyResponse
	14, // 41: hero.property.v1.PropertyService.GetProperty:output_type -> hero.property.v1.GetPropertyResponse
	16, // 42: hero.property.v1.PropertyService.ListProperties:output_type -> hero.property.v1.ListPropertiesResponse
	18, // 43: hero.property.v1.PropertyService.UpdateProperty:output_type -> hero.property.v1.UpdatePropertyResponse
	20, // 44: hero.property.v1.PropertyService.DeleteProperty:output_type -> hero.property.v1.DeletePropertyResponse
	22, // 45: hero.property.v1.PropertyService.SearchProperties:output_type -> hero.property.v1.SearchPropertiesResponse
	24, // 46: hero.property.v1.PropertyService.BatchGetProperties:output_type -> hero.property.v1.BatchGetPropertiesResponse
	26, // 47: hero.property.v1.PropertyService.AddCustodyEvent:output_type -> hero.property.v1.AddCustodyEventResponse
	28, // 48: hero.property.v1.PropertyService.GetCustodyChain:output_type -> hero.property.v1.GetCustodyChainResponse
	30, // 49: hero.property.v1.PropertyService.ListPropertyFileAttachments:output_type -> hero.property.v1.ListPropertyFileAttachmentsResponse
	33, // 50: hero.property.v1.PropertyService.AddPropertyFileAttachment:output_type -> hero.property.v1.AddPropertyFileAttachmentResponse
	35, // 51: hero.property.v1.PropertyService.RemovePropertyFileAttachment:output_type -> hero.property.v1.RemovePropertyFileAttachmentResponse
	40, // [40:52] is the sub-list for method output_type
	28, // [28:40] is the sub-list for method input_type
	28, // [28:28] is the sub-list for extension type_name
	28, // [28:28] is the sub-list for extension extendee
	0,  // [0:28] is the sub-list for field type_name
}

func init() { file_hero_property_v1_property_proto_init() }
func file_hero_property_v1_property_proto_init() {
	if File_hero_property_v1_property_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_hero_property_v1_property_proto_rawDesc,
			NumEnums:      5,
			NumMessages:   31,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_hero_property_v1_property_proto_goTypes,
		DependencyIndexes: file_hero_property_v1_property_proto_depIdxs,
		EnumInfos:         file_hero_property_v1_property_proto_enumTypes,
		MessageInfos:      file_hero_property_v1_property_proto_msgTypes,
	}.Build()
	File_hero_property_v1_property_proto = out.File
	file_hero_property_v1_property_proto_rawDesc = nil
	file_hero_property_v1_property_proto_goTypes = nil
	file_hero_property_v1_property_proto_depIdxs = nil
}
