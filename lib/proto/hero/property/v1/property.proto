syntax = "proto3";

package hero.property.v1;

option go_package = "proto/hero/property/v1;property";

import "google/protobuf/struct.proto";





// -----------------------------------------------------------------------------
// Property-Specific Metadata and Chain of Custody
// -----------------------------------------------------------------------------

// PropertyType enumerates the types of property handling
enum PropertyType {
  PROPERTY_TYPE_UNSPECIFIED = 0;
  PROPERTY_TYPE_FOUND = 1;
  PROPERTY_TYPE_SEIZED = 2;
  PROPERTY_TYPE_STOLEN = 3;
  PROPERTY_TYPE_SAFEKEEPING = 4;
  PROPERTY_TYPE_MISSING = 5;
  PROPERTY_TYPE_RECOVERED = 6;
}

// PropertyStatus enumerates the status of property items
enum PropertyStatus {
  PROPERTY_STATUS_UNSPECIFIED = 0;
  PROPERTY_STATUS_COLLECTED = 1;
  PROPERTY_STATUS_IN_CUSTODY = 2;
  PROPERTY_STATUS_CHECKED_OUT = 3;
  PROPERTY_STATUS_DISPOSED = 4;
  PROPERTY_STATUS_MISSING = 5;
  PROPERTY_STATUS_CLAIMED = 6;
}

// PropertyDisposalType enumerates how property was disposed
enum PropertyDisposalType {
  PROPERTY_DISPOSAL_TYPE_UNSPECIFIED = 0;
  PROPERTY_DISPOSAL_TYPE_RELEASED = 1;
  PROPERTY_DISPOSAL_TYPE_DESTROYED = 2;
  PROPERTY_DISPOSAL_TYPE_AUCTIONED = 3;
  PROPERTY_DISPOSAL_TYPE_AGENCY_RETAIN = 4;
  PROPERTY_DISPOSAL_TYPE_TRANSFERRED = 5;
}

// CustodyActionType enumerates the types of custody changes
enum CustodyActionType {
  CUSTODY_ACTION_TYPE_UNSPECIFIED = 0;
  CUSTODY_ACTION_TYPE_COLLECTED = 1;
  CUSTODY_ACTION_TYPE_TRANSFERRED = 2;
  CUSTODY_ACTION_TYPE_RELEASED = 3;
  CUSTODY_ACTION_TYPE_CHECKED_OUT = 4;
  CUSTODY_ACTION_TYPE_CHECKED_IN = 5;
  CUSTODY_ACTION_TYPE_DISPOSED = 6;
  CUSTODY_ACTION_TYPE_LOGGED = 7;      // Property reported but not in physical custody
}

// CustodyEvent represents a single chain of custody entry
message CustodyEvent {
  string timestamp = 1;                    // ISO8601 timestamp of the custody change
  string transferring_user_id = 2;         // User/Officer transferring the item
  string transferring_agency = 3;          // Agency transferring the item
  string receiving_user_id = 4;            // User/Officer/Agency receiving the item (optional for disposal)
  string receiving_agency = 5;             // Agency receiving the item (if different from current org)
  string new_location = 6;                 // New location (e.g., "Evidence Room A-12", "Lab XYZ")
  CustodyActionType action_type = 7;       // Type of custody action
  string notes = 8;                        // Reason/purpose for the change
  string case_number = 9;                  // Associated case number
  string evidence_number = 10;             // Evidence tracking number
}

// PropertySchema contains detailed property information fields
message PropertySchema {
  string description = 1;           // Detailed description of the property
  string quantity = 2;              // Quantity of items
  string category = 3;              // Category classification
  string identifiers = 4;           // Make, model, or brand information
  string owner = 5;                 // Owner information if applicable
  string condition = 6;             // Condition of the property
  string serial_number = 7;         // Serial number or unique identifier
  string value = 8;                 // Monetary value of the property
  PropertyType property_type = 9;   // Type of property (moved from Property struct)
}

// -----------------------------------------------------------------------------
// Property Service Definition
// -----------------------------------------------------------------------------

// Property represents a property entity
message Property {
  string id = 1;                           // Unique identifier for the property
  int32 org_id = 2;                        // Organization identifier
  string property_number = 3;              // Property/Evidence number for tracking
  PropertyStatus property_status = 4;       // Current status of the property
  bool is_evidence = 5;                    // Flag indicating whether this is evidence
  string retention_period = 6;             // Retention period for evidence
  PropertyDisposalType disposal_type = 7;  // How property was disposed
  string notes = 8;                        // Additional notes about the property 
  string current_custodian = 9;           // Current person/agency with custody
  string current_location = 10;            // Current location of the property
  repeated CustodyEvent custody_chain = 11; // Complete chain of custody history
  PropertySchema property_schema = 12;     // Detailed property information
  string create_time = 16;                 // ISO8601 timestamp when the property was created
  string update_time = 17;                 // ISO8601 timestamp when the property was last updated
  string created_by = 18;                  // User ID of the creator
  string updated_by = 19;                  // User ID of the last updater
  int32 version = 20;                      // Version number (for audit/history purposes)
  int32 status = 21;                       // Current operational status
  string resource_type = 22;               // Fixed value "PROPERTY"
}

// DateRange represents a time range for filtering search results
message DateRange {
  string from = 1;  // Start time in RFC3339 format (inclusive)
  string to = 2;    // End time in RFC3339 format (inclusive)
}

// FieldQuery represents a field-specific search query
message FieldQuery {
  string field = 1;  // Field to search in (id, notes, location, current_custodian, current_location)
  string query = 2;  // Search term for this specific field
}

// HighlightResult represents highlighted search results for a property
message HighlightResult {
  string field = 1;                    // Field name that had a match
  repeated string fragments = 2;        // Highlighted fragments with matched terms
}

// SearchOrderBy defines the ordering options for search results
enum SearchOrderBy {
  SEARCH_ORDER_BY_UNSPECIFIED = 0;
  SEARCH_ORDER_BY_RELEVANCE = 1;
  SEARCH_ORDER_BY_CREATED_AT = 2;
  SEARCH_ORDER_BY_UPDATED_AT = 3;
  SEARCH_ORDER_BY_STATUS = 4;
}

// Request/Response Messages for CreateProperty
message CreatePropertyRequest {
  Property property = 1;
}

message CreatePropertyResponse {
  Property property = 1;
}

// Request/Response Messages for GetProperty
message GetPropertyRequest {
  string id = 1;
}

message GetPropertyResponse {
  Property property = 1;
}



// Request/Response Messages for ListProperties
message ListPropertiesRequest {
  int32 page_size = 1;
  string page_token = 2;
  PropertyType property_type = 3;
  PropertyStatus property_status = 4;
  string order_by = 5;
}

message ListPropertiesResponse {
  repeated Property properties = 1;
  string next_page_token = 2;
}

// Request/Response Messages for UpdateProperty
message UpdatePropertyRequest {
  Property property = 1;
}

message UpdatePropertyResponse {
  Property property = 1;
}

// Request/Response Messages for DeleteProperty
message DeletePropertyRequest {
  string id = 1;
}

message DeletePropertyResponse {
}

// Request/Response Messages for SearchProperties
message SearchPropertiesRequest {
  string query = 1;
  repeated FieldQuery field_queries = 2;
  DateRange date_range = 3;
  PropertyType property_type = 4;
  PropertyStatus property_status = 5;
  SearchOrderBy order_by = 6;
  int32 page_size = 7;
  string page_token = 8;
}

message SearchPropertiesResponse {
  repeated Property properties = 1;
  repeated HighlightResult highlights = 2;
  string next_page_token = 3;
  int32 total_count = 4;
}

// Request/Response Messages for BatchGetProperties
message BatchGetPropertiesRequest {
  repeated string ids = 1;
}

message BatchGetPropertiesResponse {
  repeated Property properties = 1;
}

// Request/Response Messages for AddCustodyEvent
message AddCustodyEventRequest {
  string property_id = 1;
  CustodyEvent custody_event = 2;
}

message AddCustodyEventResponse {
}

// Request/Response Messages for GetCustodyChain
message GetCustodyChainRequest {
  string property_id = 1;
}

message GetCustodyChainResponse {
  repeated CustodyEvent custody_chain = 1;
}

// Request/Response Messages for ListPropertyFileAttachments
message ListPropertyFileAttachmentsRequest {
  string property_id = 1;
  string file_category = 2;
  int32 page_size = 3;
  string page_token = 4;
}

message ListPropertyFileAttachmentsResponse {
  repeated PropertyFileReference file_attachments = 1;
  string next_page_token = 2;
}

// PropertyFileReference represents a file attachment for a property
message PropertyFileReference {
  string id = 1;
  string property_id = 2;
  string file_id = 3;
  string caption = 4;
  string display_name = 5;
  int32 display_order = 6;
  string file_category = 7;
  google.protobuf.Struct metadata = 8;
}

// Request/Response Messages for AddPropertyFileAttachment
message AddPropertyFileAttachmentRequest {
  string property_id = 1;
  PropertyFileReference file_attachment = 2;
}

message AddPropertyFileAttachmentResponse {
  PropertyFileReference file_attachment = 1;
}

// Request/Response Messages for RemovePropertyFileAttachment
message RemovePropertyFileAttachmentRequest {
  string property_id = 1;
  string attachment_id = 2;
}

message RemovePropertyFileAttachmentResponse {
}







// -----------------------------------------------------------------------------
// Property Service Definition
// -----------------------------------------------------------------------------

// PropertyService defines operations for managing property and evidence items.
// This service handles chain of custody, property status tracking, and evidence management.
service PropertyService {
  // CreateProperty creates a new property
  rpc CreateProperty(CreatePropertyRequest) returns (CreatePropertyResponse);
  
  // GetProperty retrieves a property by its ID
  rpc GetProperty(GetPropertyRequest) returns (GetPropertyResponse);
  

  
  // ListProperties lists properties with pagination and filtering
  rpc ListProperties(ListPropertiesRequest) returns (ListPropertiesResponse);
  
  // UpdateProperty updates an existing property
  rpc UpdateProperty(UpdatePropertyRequest) returns (UpdatePropertyResponse);
  
  // DeleteProperty deletes a property
  rpc DeleteProperty(DeletePropertyRequest) returns (DeletePropertyResponse);
  
  // SearchProperties performs advanced search on properties
  rpc SearchProperties(SearchPropertiesRequest) returns (SearchPropertiesResponse);
  
  // BatchGetProperties retrieves multiple properties by their IDs
  rpc BatchGetProperties(BatchGetPropertiesRequest) returns (BatchGetPropertiesResponse);
  
  // AddCustodyEvent adds a new custody event to a property's chain of custody
  rpc AddCustodyEvent(AddCustodyEventRequest) returns (AddCustodyEventResponse);
  
  // GetCustodyChain returns the complete chain of custody for a property
  rpc GetCustodyChain(GetCustodyChainRequest) returns (GetCustodyChainResponse);
  
  // ListPropertyFileAttachments lists all file attachments for a property
  rpc ListPropertyFileAttachments(ListPropertyFileAttachmentsRequest) returns (ListPropertyFileAttachmentsResponse);
  
  // AddPropertyFileAttachment adds a file attachment to a property
  rpc AddPropertyFileAttachment(AddPropertyFileAttachmentRequest) returns (AddPropertyFileAttachmentResponse);
  
  // RemovePropertyFileAttachment removes a file attachment from a property
  rpc RemovePropertyFileAttachment(RemovePropertyFileAttachmentRequest) returns (RemovePropertyFileAttachmentResponse);
}
