// @generated by protoc-gen-es v2.6.3 with parameter "target=ts"
// @generated from file hero/property/v1/property.proto (package hero.property.v1, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv2";
import { file_google_protobuf_struct } from "@bufbuild/protobuf/wkt";
import type { JsonObject, Message } from "@bufbuild/protobuf";

/**
 * Describes the file hero/property/v1/property.proto.
 */
export const file_hero_property_v1_property: GenFile = /*@__PURE__*/
  fileDesc("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", [file_google_protobuf_struct]);

/**
 * CustodyEvent represents a single chain of custody entry
 *
 * @generated from message hero.property.v1.CustodyEvent
 */
export type CustodyEvent = Message<"hero.property.v1.CustodyEvent"> & {
  /**
   * ISO8601 timestamp of the custody change
   *
   * @generated from field: string timestamp = 1;
   */
  timestamp: string;

  /**
   * User/Officer transferring the item
   *
   * @generated from field: string transferring_user_id = 2;
   */
  transferringUserId: string;

  /**
   * Agency transferring the item
   *
   * @generated from field: string transferring_agency = 3;
   */
  transferringAgency: string;

  /**
   * User/Officer/Agency receiving the item (optional for disposal)
   *
   * @generated from field: string receiving_user_id = 4;
   */
  receivingUserId: string;

  /**
   * Agency receiving the item (if different from current org)
   *
   * @generated from field: string receiving_agency = 5;
   */
  receivingAgency: string;

  /**
   * New location (e.g., "Evidence Room A-12", "Lab XYZ")
   *
   * @generated from field: string new_location = 6;
   */
  newLocation: string;

  /**
   * Type of custody action
   *
   * @generated from field: hero.property.v1.CustodyActionType action_type = 7;
   */
  actionType: CustodyActionType;

  /**
   * Reason/purpose for the change
   *
   * @generated from field: string notes = 8;
   */
  notes: string;

  /**
   * Associated case number
   *
   * @generated from field: string case_number = 9;
   */
  caseNumber: string;

  /**
   * Evidence tracking number
   *
   * @generated from field: string evidence_number = 10;
   */
  evidenceNumber: string;
};

/**
 * Describes the message hero.property.v1.CustodyEvent.
 * Use `create(CustodyEventSchema)` to create a new message.
 */
export const CustodyEventSchema: GenMessage<CustodyEvent> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 0);

/**
 * PropertySchema contains detailed property information fields
 *
 * @generated from message hero.property.v1.PropertySchema
 */
export type PropertySchema = Message<"hero.property.v1.PropertySchema"> & {
  /**
   * Detailed description of the property
   *
   * @generated from field: string description = 1;
   */
  description: string;

  /**
   * Quantity of items
   *
   * @generated from field: string quantity = 2;
   */
  quantity: string;

  /**
   * Category classification
   *
   * @generated from field: string category = 3;
   */
  category: string;

  /**
   * Make, model, or brand information
   *
   * @generated from field: string identifiers = 4;
   */
  identifiers: string;

  /**
   * Owner information if applicable
   *
   * @generated from field: string owner = 5;
   */
  owner: string;

  /**
   * Condition of the property
   *
   * @generated from field: string condition = 6;
   */
  condition: string;

  /**
   * Serial number or unique identifier
   *
   * @generated from field: string serial_number = 7;
   */
  serialNumber: string;

  /**
   * Monetary value of the property
   *
   * @generated from field: string value = 8;
   */
  value: string;

  /**
   * Type of property (moved from Property struct)
   *
   * @generated from field: hero.property.v1.PropertyType property_type = 9;
   */
  propertyType: PropertyType;
};

/**
 * Describes the message hero.property.v1.PropertySchema.
 * Use `create(PropertySchemaSchema)` to create a new message.
 */
export const PropertySchemaSchema: GenMessage<PropertySchema> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 1);

/**
 * Property represents a property entity
 *
 * @generated from message hero.property.v1.Property
 */
export type Property = Message<"hero.property.v1.Property"> & {
  /**
   * Unique identifier for the property
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * Organization identifier
   *
   * @generated from field: int32 org_id = 2;
   */
  orgId: number;

  /**
   * Property/Evidence number for tracking
   *
   * @generated from field: string property_number = 3;
   */
  propertyNumber: string;

  /**
   * Current status of the property
   *
   * @generated from field: hero.property.v1.PropertyStatus property_status = 4;
   */
  propertyStatus: PropertyStatus;

  /**
   * Flag indicating whether this is evidence
   *
   * @generated from field: bool is_evidence = 5;
   */
  isEvidence: boolean;

  /**
   * Retention period for evidence
   *
   * @generated from field: string retention_period = 6;
   */
  retentionPeriod: string;

  /**
   * How property was disposed
   *
   * @generated from field: hero.property.v1.PropertyDisposalType disposal_type = 7;
   */
  disposalType: PropertyDisposalType;

  /**
   * Additional notes about the property 
   *
   * @generated from field: string notes = 8;
   */
  notes: string;

  /**
   * Current person/agency with custody
   *
   * @generated from field: string current_custodian = 9;
   */
  currentCustodian: string;

  /**
   * Current location of the property
   *
   * @generated from field: string current_location = 10;
   */
  currentLocation: string;

  /**
   * Complete chain of custody history
   *
   * @generated from field: repeated hero.property.v1.CustodyEvent custody_chain = 11;
   */
  custodyChain: CustodyEvent[];

  /**
   * Detailed property information
   *
   * @generated from field: hero.property.v1.PropertySchema property_schema = 12;
   */
  propertySchema?: PropertySchema;

  /**
   * ISO8601 timestamp when the property was created
   *
   * @generated from field: string create_time = 16;
   */
  createTime: string;

  /**
   * ISO8601 timestamp when the property was last updated
   *
   * @generated from field: string update_time = 17;
   */
  updateTime: string;

  /**
   * User ID of the creator
   *
   * @generated from field: string created_by = 18;
   */
  createdBy: string;

  /**
   * User ID of the last updater
   *
   * @generated from field: string updated_by = 19;
   */
  updatedBy: string;

  /**
   * Version number (for audit/history purposes)
   *
   * @generated from field: int32 version = 20;
   */
  version: number;

  /**
   * Current operational status
   *
   * @generated from field: int32 status = 21;
   */
  status: number;

  /**
   * Fixed value "PROPERTY"
   *
   * @generated from field: string resource_type = 22;
   */
  resourceType: string;
};

/**
 * Describes the message hero.property.v1.Property.
 * Use `create(PropertySchema$)` to create a new message.
 */
export const PropertySchema$: GenMessage<Property> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 2);

/**
 * DateRange represents a time range for filtering search results
 *
 * @generated from message hero.property.v1.DateRange
 */
export type DateRange = Message<"hero.property.v1.DateRange"> & {
  /**
   * Start time in RFC3339 format (inclusive)
   *
   * @generated from field: string from = 1;
   */
  from: string;

  /**
   * End time in RFC3339 format (inclusive)
   *
   * @generated from field: string to = 2;
   */
  to: string;
};

/**
 * Describes the message hero.property.v1.DateRange.
 * Use `create(DateRangeSchema)` to create a new message.
 */
export const DateRangeSchema: GenMessage<DateRange> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 3);

/**
 * FieldQuery represents a field-specific search query
 *
 * @generated from message hero.property.v1.FieldQuery
 */
export type FieldQuery = Message<"hero.property.v1.FieldQuery"> & {
  /**
   * Field to search in (id, notes, location, current_custodian, current_location)
   *
   * @generated from field: string field = 1;
   */
  field: string;

  /**
   * Search term for this specific field
   *
   * @generated from field: string query = 2;
   */
  query: string;
};

/**
 * Describes the message hero.property.v1.FieldQuery.
 * Use `create(FieldQuerySchema)` to create a new message.
 */
export const FieldQuerySchema: GenMessage<FieldQuery> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 4);

/**
 * HighlightResult represents highlighted search results for a property
 *
 * @generated from message hero.property.v1.HighlightResult
 */
export type HighlightResult = Message<"hero.property.v1.HighlightResult"> & {
  /**
   * Field name that had a match
   *
   * @generated from field: string field = 1;
   */
  field: string;

  /**
   * Highlighted fragments with matched terms
   *
   * @generated from field: repeated string fragments = 2;
   */
  fragments: string[];
};

/**
 * Describes the message hero.property.v1.HighlightResult.
 * Use `create(HighlightResultSchema)` to create a new message.
 */
export const HighlightResultSchema: GenMessage<HighlightResult> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 5);

/**
 * Request/Response Messages for CreateProperty
 *
 * @generated from message hero.property.v1.CreatePropertyRequest
 */
export type CreatePropertyRequest = Message<"hero.property.v1.CreatePropertyRequest"> & {
  /**
   * @generated from field: hero.property.v1.Property property = 1;
   */
  property?: Property;
};

/**
 * Describes the message hero.property.v1.CreatePropertyRequest.
 * Use `create(CreatePropertyRequestSchema)` to create a new message.
 */
export const CreatePropertyRequestSchema: GenMessage<CreatePropertyRequest> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 6);

/**
 * @generated from message hero.property.v1.CreatePropertyResponse
 */
export type CreatePropertyResponse = Message<"hero.property.v1.CreatePropertyResponse"> & {
  /**
   * @generated from field: hero.property.v1.Property property = 1;
   */
  property?: Property;
};

/**
 * Describes the message hero.property.v1.CreatePropertyResponse.
 * Use `create(CreatePropertyResponseSchema)` to create a new message.
 */
export const CreatePropertyResponseSchema: GenMessage<CreatePropertyResponse> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 7);

/**
 * Request/Response Messages for GetProperty
 *
 * @generated from message hero.property.v1.GetPropertyRequest
 */
export type GetPropertyRequest = Message<"hero.property.v1.GetPropertyRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;
};

/**
 * Describes the message hero.property.v1.GetPropertyRequest.
 * Use `create(GetPropertyRequestSchema)` to create a new message.
 */
export const GetPropertyRequestSchema: GenMessage<GetPropertyRequest> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 8);

/**
 * @generated from message hero.property.v1.GetPropertyResponse
 */
export type GetPropertyResponse = Message<"hero.property.v1.GetPropertyResponse"> & {
  /**
   * @generated from field: hero.property.v1.Property property = 1;
   */
  property?: Property;
};

/**
 * Describes the message hero.property.v1.GetPropertyResponse.
 * Use `create(GetPropertyResponseSchema)` to create a new message.
 */
export const GetPropertyResponseSchema: GenMessage<GetPropertyResponse> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 9);

/**
 * Request/Response Messages for ListProperties
 *
 * @generated from message hero.property.v1.ListPropertiesRequest
 */
export type ListPropertiesRequest = Message<"hero.property.v1.ListPropertiesRequest"> & {
  /**
   * @generated from field: int32 page_size = 1;
   */
  pageSize: number;

  /**
   * @generated from field: string page_token = 2;
   */
  pageToken: string;

  /**
   * @generated from field: hero.property.v1.PropertyType property_type = 3;
   */
  propertyType: PropertyType;

  /**
   * @generated from field: hero.property.v1.PropertyStatus property_status = 4;
   */
  propertyStatus: PropertyStatus;

  /**
   * @generated from field: string order_by = 5;
   */
  orderBy: string;
};

/**
 * Describes the message hero.property.v1.ListPropertiesRequest.
 * Use `create(ListPropertiesRequestSchema)` to create a new message.
 */
export const ListPropertiesRequestSchema: GenMessage<ListPropertiesRequest> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 10);

/**
 * @generated from message hero.property.v1.ListPropertiesResponse
 */
export type ListPropertiesResponse = Message<"hero.property.v1.ListPropertiesResponse"> & {
  /**
   * @generated from field: repeated hero.property.v1.Property properties = 1;
   */
  properties: Property[];

  /**
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;
};

/**
 * Describes the message hero.property.v1.ListPropertiesResponse.
 * Use `create(ListPropertiesResponseSchema)` to create a new message.
 */
export const ListPropertiesResponseSchema: GenMessage<ListPropertiesResponse> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 11);

/**
 * Request/Response Messages for UpdateProperty
 *
 * @generated from message hero.property.v1.UpdatePropertyRequest
 */
export type UpdatePropertyRequest = Message<"hero.property.v1.UpdatePropertyRequest"> & {
  /**
   * @generated from field: hero.property.v1.Property property = 1;
   */
  property?: Property;
};

/**
 * Describes the message hero.property.v1.UpdatePropertyRequest.
 * Use `create(UpdatePropertyRequestSchema)` to create a new message.
 */
export const UpdatePropertyRequestSchema: GenMessage<UpdatePropertyRequest> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 12);

/**
 * @generated from message hero.property.v1.UpdatePropertyResponse
 */
export type UpdatePropertyResponse = Message<"hero.property.v1.UpdatePropertyResponse"> & {
  /**
   * @generated from field: hero.property.v1.Property property = 1;
   */
  property?: Property;
};

/**
 * Describes the message hero.property.v1.UpdatePropertyResponse.
 * Use `create(UpdatePropertyResponseSchema)` to create a new message.
 */
export const UpdatePropertyResponseSchema: GenMessage<UpdatePropertyResponse> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 13);

/**
 * Request/Response Messages for DeleteProperty
 *
 * @generated from message hero.property.v1.DeletePropertyRequest
 */
export type DeletePropertyRequest = Message<"hero.property.v1.DeletePropertyRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;
};

/**
 * Describes the message hero.property.v1.DeletePropertyRequest.
 * Use `create(DeletePropertyRequestSchema)` to create a new message.
 */
export const DeletePropertyRequestSchema: GenMessage<DeletePropertyRequest> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 14);

/**
 * @generated from message hero.property.v1.DeletePropertyResponse
 */
export type DeletePropertyResponse = Message<"hero.property.v1.DeletePropertyResponse"> & {
};

/**
 * Describes the message hero.property.v1.DeletePropertyResponse.
 * Use `create(DeletePropertyResponseSchema)` to create a new message.
 */
export const DeletePropertyResponseSchema: GenMessage<DeletePropertyResponse> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 15);

/**
 * Request/Response Messages for SearchProperties
 *
 * @generated from message hero.property.v1.SearchPropertiesRequest
 */
export type SearchPropertiesRequest = Message<"hero.property.v1.SearchPropertiesRequest"> & {
  /**
   * @generated from field: string query = 1;
   */
  query: string;

  /**
   * @generated from field: repeated hero.property.v1.FieldQuery field_queries = 2;
   */
  fieldQueries: FieldQuery[];

  /**
   * @generated from field: hero.property.v1.DateRange date_range = 3;
   */
  dateRange?: DateRange;

  /**
   * @generated from field: hero.property.v1.PropertyType property_type = 4;
   */
  propertyType: PropertyType;

  /**
   * @generated from field: hero.property.v1.PropertyStatus property_status = 5;
   */
  propertyStatus: PropertyStatus;

  /**
   * @generated from field: hero.property.v1.SearchOrderBy order_by = 6;
   */
  orderBy: SearchOrderBy;

  /**
   * @generated from field: int32 page_size = 7;
   */
  pageSize: number;

  /**
   * @generated from field: string page_token = 8;
   */
  pageToken: string;
};

/**
 * Describes the message hero.property.v1.SearchPropertiesRequest.
 * Use `create(SearchPropertiesRequestSchema)` to create a new message.
 */
export const SearchPropertiesRequestSchema: GenMessage<SearchPropertiesRequest> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 16);

/**
 * @generated from message hero.property.v1.SearchPropertiesResponse
 */
export type SearchPropertiesResponse = Message<"hero.property.v1.SearchPropertiesResponse"> & {
  /**
   * @generated from field: repeated hero.property.v1.Property properties = 1;
   */
  properties: Property[];

  /**
   * @generated from field: repeated hero.property.v1.HighlightResult highlights = 2;
   */
  highlights: HighlightResult[];

  /**
   * @generated from field: string next_page_token = 3;
   */
  nextPageToken: string;

  /**
   * @generated from field: int32 total_count = 4;
   */
  totalCount: number;
};

/**
 * Describes the message hero.property.v1.SearchPropertiesResponse.
 * Use `create(SearchPropertiesResponseSchema)` to create a new message.
 */
export const SearchPropertiesResponseSchema: GenMessage<SearchPropertiesResponse> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 17);

/**
 * Request/Response Messages for BatchGetProperties
 *
 * @generated from message hero.property.v1.BatchGetPropertiesRequest
 */
export type BatchGetPropertiesRequest = Message<"hero.property.v1.BatchGetPropertiesRequest"> & {
  /**
   * @generated from field: repeated string ids = 1;
   */
  ids: string[];
};

/**
 * Describes the message hero.property.v1.BatchGetPropertiesRequest.
 * Use `create(BatchGetPropertiesRequestSchema)` to create a new message.
 */
export const BatchGetPropertiesRequestSchema: GenMessage<BatchGetPropertiesRequest> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 18);

/**
 * @generated from message hero.property.v1.BatchGetPropertiesResponse
 */
export type BatchGetPropertiesResponse = Message<"hero.property.v1.BatchGetPropertiesResponse"> & {
  /**
   * @generated from field: repeated hero.property.v1.Property properties = 1;
   */
  properties: Property[];
};

/**
 * Describes the message hero.property.v1.BatchGetPropertiesResponse.
 * Use `create(BatchGetPropertiesResponseSchema)` to create a new message.
 */
export const BatchGetPropertiesResponseSchema: GenMessage<BatchGetPropertiesResponse> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 19);

/**
 * Request/Response Messages for AddCustodyEvent
 *
 * @generated from message hero.property.v1.AddCustodyEventRequest
 */
export type AddCustodyEventRequest = Message<"hero.property.v1.AddCustodyEventRequest"> & {
  /**
   * @generated from field: string property_id = 1;
   */
  propertyId: string;

  /**
   * @generated from field: hero.property.v1.CustodyEvent custody_event = 2;
   */
  custodyEvent?: CustodyEvent;
};

/**
 * Describes the message hero.property.v1.AddCustodyEventRequest.
 * Use `create(AddCustodyEventRequestSchema)` to create a new message.
 */
export const AddCustodyEventRequestSchema: GenMessage<AddCustodyEventRequest> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 20);

/**
 * @generated from message hero.property.v1.AddCustodyEventResponse
 */
export type AddCustodyEventResponse = Message<"hero.property.v1.AddCustodyEventResponse"> & {
};

/**
 * Describes the message hero.property.v1.AddCustodyEventResponse.
 * Use `create(AddCustodyEventResponseSchema)` to create a new message.
 */
export const AddCustodyEventResponseSchema: GenMessage<AddCustodyEventResponse> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 21);

/**
 * Request/Response Messages for GetCustodyChain
 *
 * @generated from message hero.property.v1.GetCustodyChainRequest
 */
export type GetCustodyChainRequest = Message<"hero.property.v1.GetCustodyChainRequest"> & {
  /**
   * @generated from field: string property_id = 1;
   */
  propertyId: string;
};

/**
 * Describes the message hero.property.v1.GetCustodyChainRequest.
 * Use `create(GetCustodyChainRequestSchema)` to create a new message.
 */
export const GetCustodyChainRequestSchema: GenMessage<GetCustodyChainRequest> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 22);

/**
 * @generated from message hero.property.v1.GetCustodyChainResponse
 */
export type GetCustodyChainResponse = Message<"hero.property.v1.GetCustodyChainResponse"> & {
  /**
   * @generated from field: repeated hero.property.v1.CustodyEvent custody_chain = 1;
   */
  custodyChain: CustodyEvent[];
};

/**
 * Describes the message hero.property.v1.GetCustodyChainResponse.
 * Use `create(GetCustodyChainResponseSchema)` to create a new message.
 */
export const GetCustodyChainResponseSchema: GenMessage<GetCustodyChainResponse> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 23);

/**
 * Request/Response Messages for ListPropertyFileAttachments
 *
 * @generated from message hero.property.v1.ListPropertyFileAttachmentsRequest
 */
export type ListPropertyFileAttachmentsRequest = Message<"hero.property.v1.ListPropertyFileAttachmentsRequest"> & {
  /**
   * @generated from field: string property_id = 1;
   */
  propertyId: string;

  /**
   * @generated from field: string file_category = 2;
   */
  fileCategory: string;

  /**
   * @generated from field: int32 page_size = 3;
   */
  pageSize: number;

  /**
   * @generated from field: string page_token = 4;
   */
  pageToken: string;
};

/**
 * Describes the message hero.property.v1.ListPropertyFileAttachmentsRequest.
 * Use `create(ListPropertyFileAttachmentsRequestSchema)` to create a new message.
 */
export const ListPropertyFileAttachmentsRequestSchema: GenMessage<ListPropertyFileAttachmentsRequest> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 24);

/**
 * @generated from message hero.property.v1.ListPropertyFileAttachmentsResponse
 */
export type ListPropertyFileAttachmentsResponse = Message<"hero.property.v1.ListPropertyFileAttachmentsResponse"> & {
  /**
   * @generated from field: repeated hero.property.v1.PropertyFileReference file_attachments = 1;
   */
  fileAttachments: PropertyFileReference[];

  /**
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;
};

/**
 * Describes the message hero.property.v1.ListPropertyFileAttachmentsResponse.
 * Use `create(ListPropertyFileAttachmentsResponseSchema)` to create a new message.
 */
export const ListPropertyFileAttachmentsResponseSchema: GenMessage<ListPropertyFileAttachmentsResponse> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 25);

/**
 * PropertyFileReference represents a file attachment for a property
 *
 * @generated from message hero.property.v1.PropertyFileReference
 */
export type PropertyFileReference = Message<"hero.property.v1.PropertyFileReference"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string property_id = 2;
   */
  propertyId: string;

  /**
   * @generated from field: string file_id = 3;
   */
  fileId: string;

  /**
   * @generated from field: string caption = 4;
   */
  caption: string;

  /**
   * @generated from field: string display_name = 5;
   */
  displayName: string;

  /**
   * @generated from field: int32 display_order = 6;
   */
  displayOrder: number;

  /**
   * @generated from field: string file_category = 7;
   */
  fileCategory: string;

  /**
   * @generated from field: google.protobuf.Struct metadata = 8;
   */
  metadata?: JsonObject;
};

/**
 * Describes the message hero.property.v1.PropertyFileReference.
 * Use `create(PropertyFileReferenceSchema)` to create a new message.
 */
export const PropertyFileReferenceSchema: GenMessage<PropertyFileReference> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 26);

/**
 * Request/Response Messages for AddPropertyFileAttachment
 *
 * @generated from message hero.property.v1.AddPropertyFileAttachmentRequest
 */
export type AddPropertyFileAttachmentRequest = Message<"hero.property.v1.AddPropertyFileAttachmentRequest"> & {
  /**
   * @generated from field: string property_id = 1;
   */
  propertyId: string;

  /**
   * @generated from field: hero.property.v1.PropertyFileReference file_attachment = 2;
   */
  fileAttachment?: PropertyFileReference;
};

/**
 * Describes the message hero.property.v1.AddPropertyFileAttachmentRequest.
 * Use `create(AddPropertyFileAttachmentRequestSchema)` to create a new message.
 */
export const AddPropertyFileAttachmentRequestSchema: GenMessage<AddPropertyFileAttachmentRequest> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 27);

/**
 * @generated from message hero.property.v1.AddPropertyFileAttachmentResponse
 */
export type AddPropertyFileAttachmentResponse = Message<"hero.property.v1.AddPropertyFileAttachmentResponse"> & {
  /**
   * @generated from field: hero.property.v1.PropertyFileReference file_attachment = 1;
   */
  fileAttachment?: PropertyFileReference;
};

/**
 * Describes the message hero.property.v1.AddPropertyFileAttachmentResponse.
 * Use `create(AddPropertyFileAttachmentResponseSchema)` to create a new message.
 */
export const AddPropertyFileAttachmentResponseSchema: GenMessage<AddPropertyFileAttachmentResponse> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 28);

/**
 * Request/Response Messages for RemovePropertyFileAttachment
 *
 * @generated from message hero.property.v1.RemovePropertyFileAttachmentRequest
 */
export type RemovePropertyFileAttachmentRequest = Message<"hero.property.v1.RemovePropertyFileAttachmentRequest"> & {
  /**
   * @generated from field: string property_id = 1;
   */
  propertyId: string;

  /**
   * @generated from field: string attachment_id = 2;
   */
  attachmentId: string;
};

/**
 * Describes the message hero.property.v1.RemovePropertyFileAttachmentRequest.
 * Use `create(RemovePropertyFileAttachmentRequestSchema)` to create a new message.
 */
export const RemovePropertyFileAttachmentRequestSchema: GenMessage<RemovePropertyFileAttachmentRequest> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 29);

/**
 * @generated from message hero.property.v1.RemovePropertyFileAttachmentResponse
 */
export type RemovePropertyFileAttachmentResponse = Message<"hero.property.v1.RemovePropertyFileAttachmentResponse"> & {
};

/**
 * Describes the message hero.property.v1.RemovePropertyFileAttachmentResponse.
 * Use `create(RemovePropertyFileAttachmentResponseSchema)` to create a new message.
 */
export const RemovePropertyFileAttachmentResponseSchema: GenMessage<RemovePropertyFileAttachmentResponse> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 30);

/**
 * PropertyType enumerates the types of property handling
 *
 * @generated from enum hero.property.v1.PropertyType
 */
export enum PropertyType {
  /**
   * @generated from enum value: PROPERTY_TYPE_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: PROPERTY_TYPE_FOUND = 1;
   */
  FOUND = 1,

  /**
   * @generated from enum value: PROPERTY_TYPE_SEIZED = 2;
   */
  SEIZED = 2,

  /**
   * @generated from enum value: PROPERTY_TYPE_STOLEN = 3;
   */
  STOLEN = 3,

  /**
   * @generated from enum value: PROPERTY_TYPE_SAFEKEEPING = 4;
   */
  SAFEKEEPING = 4,

  /**
   * @generated from enum value: PROPERTY_TYPE_MISSING = 5;
   */
  MISSING = 5,

  /**
   * @generated from enum value: PROPERTY_TYPE_RECOVERED = 6;
   */
  RECOVERED = 6,
}

/**
 * Describes the enum hero.property.v1.PropertyType.
 */
export const PropertyTypeSchema: GenEnum<PropertyType> = /*@__PURE__*/
  enumDesc(file_hero_property_v1_property, 0);

/**
 * PropertyStatus enumerates the status of property items
 *
 * @generated from enum hero.property.v1.PropertyStatus
 */
export enum PropertyStatus {
  /**
   * @generated from enum value: PROPERTY_STATUS_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: PROPERTY_STATUS_COLLECTED = 1;
   */
  COLLECTED = 1,

  /**
   * @generated from enum value: PROPERTY_STATUS_IN_CUSTODY = 2;
   */
  IN_CUSTODY = 2,

  /**
   * @generated from enum value: PROPERTY_STATUS_CHECKED_OUT = 3;
   */
  CHECKED_OUT = 3,

  /**
   * @generated from enum value: PROPERTY_STATUS_DISPOSED = 4;
   */
  DISPOSED = 4,

  /**
   * @generated from enum value: PROPERTY_STATUS_MISSING = 5;
   */
  MISSING = 5,

  /**
   * @generated from enum value: PROPERTY_STATUS_CLAIMED = 6;
   */
  CLAIMED = 6,
}

/**
 * Describes the enum hero.property.v1.PropertyStatus.
 */
export const PropertyStatusSchema: GenEnum<PropertyStatus> = /*@__PURE__*/
  enumDesc(file_hero_property_v1_property, 1);

/**
 * PropertyDisposalType enumerates how property was disposed
 *
 * @generated from enum hero.property.v1.PropertyDisposalType
 */
export enum PropertyDisposalType {
  /**
   * @generated from enum value: PROPERTY_DISPOSAL_TYPE_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: PROPERTY_DISPOSAL_TYPE_RELEASED = 1;
   */
  RELEASED = 1,

  /**
   * @generated from enum value: PROPERTY_DISPOSAL_TYPE_DESTROYED = 2;
   */
  DESTROYED = 2,

  /**
   * @generated from enum value: PROPERTY_DISPOSAL_TYPE_AUCTIONED = 3;
   */
  AUCTIONED = 3,

  /**
   * @generated from enum value: PROPERTY_DISPOSAL_TYPE_AGENCY_RETAIN = 4;
   */
  AGENCY_RETAIN = 4,

  /**
   * @generated from enum value: PROPERTY_DISPOSAL_TYPE_TRANSFERRED = 5;
   */
  TRANSFERRED = 5,
}

/**
 * Describes the enum hero.property.v1.PropertyDisposalType.
 */
export const PropertyDisposalTypeSchema: GenEnum<PropertyDisposalType> = /*@__PURE__*/
  enumDesc(file_hero_property_v1_property, 2);

/**
 * CustodyActionType enumerates the types of custody changes
 *
 * @generated from enum hero.property.v1.CustodyActionType
 */
export enum CustodyActionType {
  /**
   * @generated from enum value: CUSTODY_ACTION_TYPE_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: CUSTODY_ACTION_TYPE_COLLECTED = 1;
   */
  COLLECTED = 1,

  /**
   * @generated from enum value: CUSTODY_ACTION_TYPE_TRANSFERRED = 2;
   */
  TRANSFERRED = 2,

  /**
   * @generated from enum value: CUSTODY_ACTION_TYPE_RELEASED = 3;
   */
  RELEASED = 3,

  /**
   * @generated from enum value: CUSTODY_ACTION_TYPE_CHECKED_OUT = 4;
   */
  CHECKED_OUT = 4,

  /**
   * @generated from enum value: CUSTODY_ACTION_TYPE_CHECKED_IN = 5;
   */
  CHECKED_IN = 5,

  /**
   * @generated from enum value: CUSTODY_ACTION_TYPE_DISPOSED = 6;
   */
  DISPOSED = 6,

  /**
   * Property reported but not in physical custody
   *
   * @generated from enum value: CUSTODY_ACTION_TYPE_LOGGED = 7;
   */
  LOGGED = 7,
}

/**
 * Describes the enum hero.property.v1.CustodyActionType.
 */
export const CustodyActionTypeSchema: GenEnum<CustodyActionType> = /*@__PURE__*/
  enumDesc(file_hero_property_v1_property, 3);

/**
 * SearchOrderBy defines the ordering options for search results
 *
 * @generated from enum hero.property.v1.SearchOrderBy
 */
export enum SearchOrderBy {
  /**
   * @generated from enum value: SEARCH_ORDER_BY_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: SEARCH_ORDER_BY_RELEVANCE = 1;
   */
  RELEVANCE = 1,

  /**
   * @generated from enum value: SEARCH_ORDER_BY_CREATED_AT = 2;
   */
  CREATED_AT = 2,

  /**
   * @generated from enum value: SEARCH_ORDER_BY_UPDATED_AT = 3;
   */
  UPDATED_AT = 3,

  /**
   * @generated from enum value: SEARCH_ORDER_BY_STATUS = 4;
   */
  STATUS = 4,
}

/**
 * Describes the enum hero.property.v1.SearchOrderBy.
 */
export const SearchOrderBySchema: GenEnum<SearchOrderBy> = /*@__PURE__*/
  enumDesc(file_hero_property_v1_property, 4);

/**
 * PropertyService defines operations for managing property and evidence items.
 * This service handles chain of custody, property status tracking, and evidence management.
 *
 * @generated from service hero.property.v1.PropertyService
 */
export const PropertyService: GenService<{
  /**
   * CreateProperty creates a new property
   *
   * @generated from rpc hero.property.v1.PropertyService.CreateProperty
   */
  createProperty: {
    methodKind: "unary";
    input: typeof CreatePropertyRequestSchema;
    output: typeof CreatePropertyResponseSchema;
  },
  /**
   * GetProperty retrieves a property by its ID
   *
   * @generated from rpc hero.property.v1.PropertyService.GetProperty
   */
  getProperty: {
    methodKind: "unary";
    input: typeof GetPropertyRequestSchema;
    output: typeof GetPropertyResponseSchema;
  },
  /**
   * ListProperties lists properties with pagination and filtering
   *
   * @generated from rpc hero.property.v1.PropertyService.ListProperties
   */
  listProperties: {
    methodKind: "unary";
    input: typeof ListPropertiesRequestSchema;
    output: typeof ListPropertiesResponseSchema;
  },
  /**
   * UpdateProperty updates an existing property
   *
   * @generated from rpc hero.property.v1.PropertyService.UpdateProperty
   */
  updateProperty: {
    methodKind: "unary";
    input: typeof UpdatePropertyRequestSchema;
    output: typeof UpdatePropertyResponseSchema;
  },
  /**
   * DeleteProperty deletes a property
   *
   * @generated from rpc hero.property.v1.PropertyService.DeleteProperty
   */
  deleteProperty: {
    methodKind: "unary";
    input: typeof DeletePropertyRequestSchema;
    output: typeof DeletePropertyResponseSchema;
  },
  /**
   * SearchProperties performs advanced search on properties
   *
   * @generated from rpc hero.property.v1.PropertyService.SearchProperties
   */
  searchProperties: {
    methodKind: "unary";
    input: typeof SearchPropertiesRequestSchema;
    output: typeof SearchPropertiesResponseSchema;
  },
  /**
   * BatchGetProperties retrieves multiple properties by their IDs
   *
   * @generated from rpc hero.property.v1.PropertyService.BatchGetProperties
   */
  batchGetProperties: {
    methodKind: "unary";
    input: typeof BatchGetPropertiesRequestSchema;
    output: typeof BatchGetPropertiesResponseSchema;
  },
  /**
   * AddCustodyEvent adds a new custody event to a property's chain of custody
   *
   * @generated from rpc hero.property.v1.PropertyService.AddCustodyEvent
   */
  addCustodyEvent: {
    methodKind: "unary";
    input: typeof AddCustodyEventRequestSchema;
    output: typeof AddCustodyEventResponseSchema;
  },
  /**
   * GetCustodyChain returns the complete chain of custody for a property
   *
   * @generated from rpc hero.property.v1.PropertyService.GetCustodyChain
   */
  getCustodyChain: {
    methodKind: "unary";
    input: typeof GetCustodyChainRequestSchema;
    output: typeof GetCustodyChainResponseSchema;
  },
  /**
   * ListPropertyFileAttachments lists all file attachments for a property
   *
   * @generated from rpc hero.property.v1.PropertyService.ListPropertyFileAttachments
   */
  listPropertyFileAttachments: {
    methodKind: "unary";
    input: typeof ListPropertyFileAttachmentsRequestSchema;
    output: typeof ListPropertyFileAttachmentsResponseSchema;
  },
  /**
   * AddPropertyFileAttachment adds a file attachment to a property
   *
   * @generated from rpc hero.property.v1.PropertyService.AddPropertyFileAttachment
   */
  addPropertyFileAttachment: {
    methodKind: "unary";
    input: typeof AddPropertyFileAttachmentRequestSchema;
    output: typeof AddPropertyFileAttachmentResponseSchema;
  },
  /**
   * RemovePropertyFileAttachment removes a file attachment from a property
   *
   * @generated from rpc hero.property.v1.PropertyService.RemovePropertyFileAttachment
   */
  removePropertyFileAttachment: {
    methodKind: "unary";
    input: typeof RemovePropertyFileAttachmentRequestSchema;
    output: typeof RemovePropertyFileAttachmentResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_hero_property_v1_property, 0);

