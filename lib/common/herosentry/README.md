# Hero Sentry SDK

A simplified Sentry integration SDK for Hero Core services that automatically captures rich context and enables distributed tracing across microservices.

## Table of Contents

- [Features](#features)
- [Traces vs Spans: Quick Primer](#traces-vs-spans-quick-primer)
- [Quick Start](#quick-start)
  - [First Time Setup](#first-time-setup-one-time-configuration)
  - [Daily Use](#daily-use-what-you-do-every-day)
- [What Gets Auto-Captured](#what-gets-auto-captured)
  - [Database Connection Pool Monitoring](#database-connection-pool-monitoring)
- [API Reference](#api-reference)
  - [Initialization Functions](#initialization-functions)
  - [Span Functions](#span-functions)
  - [Error Handling](#error-handling)
  - [Interceptors](#interceptors)
- [Searching in Sentry](#searching-in-sentry)
  - [Finding Your Traces](#finding-your-traces)
  - [Narrowing Down Issues](#narrowing-down-issues)
  - [Example: Debugging a Failed CreateSituation](#example-debugging-a-failed-createsituation)
- [Distributed Tracing](#distributed-tracing)
  - [How It Works](#how-it-works)
  - [Visual Example](#visual-example)
- [Configuration](#configuration)
  - [Disabling Sentry](#disabling-sentry)
  - [Automatic Sampling Rates](#automatic-sampling-rates)
  - [Custom Configuration](#custom-configuration)
  - [Error Filtering Configuration](#error-filtering-configuration)
  - [Custom Sampling Rules](#custom-sampling-rules)
- [Best Practices](#best-practices)
- [Troubleshooting](#troubleshooting)

## Features

- **Automatic Context Capture**: Automatically captures user, organization, IP address, request ID, and code location
- **Distributed Tracing**: Seamlessly propagates traces across service boundaries with single unified traces
- **Zero Configuration**: Smart defaults based on environment
- **Clean API**: Only 8 public functions to learn
- **Type-Safe**: Idiomatic Go with clear interfaces
- **Middleware Integration**: TraceContextMiddleware ensures proper trace propagation
- **Intelligent Trace Handling**: Prevents duplicate transactions when using multiple middleware
- **Baggage Support**: Properly handles Dynamic Sampling Context (DSC) for accurate sampling
- **Enhanced Error Formatting**: Errors appear with service name and descriptive titles instead of generic type names
- **Comprehensive Panic Handling**: Panics are captured with proper formatting and flushing across all interceptor paths
- **Automatic Error Deduplication**: Prevents the same error from being captured multiple times within a single request/trace
- **Configurable Error Filtering**: Control which error types are sent to Sentry to reduce noise from expected errors

## Traces vs Spans: Quick Primer

**Trace**: The entire journey of a request across all services
- Think of it as a "story" of what happened from start to finish
- Has a unique trace ID that follows the request everywhere
- Contains multiple spans from different services

**Span**: A single operation within that journey
- Think of it as a "chapter" in the trace story
- Has timing info (start/end), tags, and custom data
- Can have child spans (sub-operations)

**Example**: 
- **Trace**: "A situation status change creates an order and notify the user" (the full story)
  - **Span 1**: "SituationService.UpdateStatus" (one chapter)
  - **Span 2**: "OrderService.CreateOrder" (another chapter)
    - **Span 1.1**: "validate order" (sub-chapter)
    - **Span 1.2**: "check asset eligibility to do the order" (sub-chapter)
    - **Span 1.3**: "save to database" (sub-chapter)
  - **Span 3** Not Implemented notification service doing some work
 

**With herosentry**: Interceptors create traces automatically. You just create spans!

## Quick Start

### First Time Setup (One-time Configuration)

If your service has no setup for sentry these are the steps you need to follow to set it up so that it can actually send the data to sentry. 

#### 1. Import the SDK

The SDK is already available in the Hero Core monorepo:

```go
import "common/herosentry"
```

#### 2. Set Environment Variables

Make sure these following environment variables are somehow injected into your service 

| Variable | Required | Default | Description |
|----------|----------|---------|-------------|
| `SENTRY_DSN` | Yes* | None | Sentry project DSN. If not set, SDK runs in no-op mode |
| `ENVIRONMENT` | No | `"development"` | Deployment environment (e.g., `prod-1`, `demo-1`, `staging`) |
| `APP_VERSION` | No | None | Application version for release tracking |

*Without `SENTRY_DSN`, the service will have no observability.

#### 3. Initialize in main.go

```go
func main() {
    // Initialize Sentry with automatic configuration
    if err := herosentry.Init("workflow-service"); err != nil {
        log.Printf("Failed to initialize Sentry: %v", err)
    }
    defer herosentry.Flush() // Ensure all events are sent before shutdown
    
    // Your service setup...
}
```

#### Default Initialization Values

When using `herosentry.Init("service-name")` without custom config, these defaults apply:

| Setting | Default Value | Description |
|---------|---------------|-------------|
| **DSN** | `SENTRY_DSN` env var | Data Source Name for Sentry project |
| **Environment** | `ENVIRONMENT` env var or `"development"` | Deployment environment |
| **Sample Rate (dev)** | `100%` (1.0) | All transactions captured in development |
| **Sample Rate (prod)** | `10%` (0.1) | 1 in 10 transactions captured in production |
| **Release** | `APP_VERSION` env var or none | Version tracking |
| **Send PII** | `false` | No personally identifiable information sent |
| **Enable Tracing** | `true` | Distributed tracing enabled |
| **Flush Timeout** | `2 seconds` | Time to wait for events on shutdown |

### No-Op Mode

If `SENTRY_DSN` is not set, the SDK will:
- Print a warning: `WARNING: SENTRY_DSN not set. Herosentry will operate in no-op mode.`
- Continue running without errors
- Make all operations no-ops (no data sent to Sentry)

This allows services to run in development or testing without Sentry configuration.


#### 4. Set Up Middleware Chain (CRITICAL for Distributed Tracing)

For distributed tracing to work correctly across services, middleware must be applied in the correct order. The Hero Core middleware package handles this automatically:

```go
// In your main.go, use the provided server setup:
srv := middleware.NewServerWithHealth(
    ":8080",
    mux,          // Your main handler
    healthMux,    // Health check endpoints
    true,         // Enable permissions
)

// This automatically applies middleware in the correct order:
// 1. TraceContextMiddleware (runs FIRST) - Extracts trace headers
// 2. AuthMiddleware - Authenticates and sets user context
// 3. PermissionsMiddleware - Checks permissions
// 4. Your handlers (run LAST)
```

**Why this order matters**: The trace context must be extracted BEFORE any other middleware runs, otherwise traces will split between services.

#### 5. Add Interceptors to Your Routes

Interceptors automatically handle distributed tracing without any manual work. Choose based on your endpoint type:

##### For RPC Services (Incoming Requests)
```go
// Add to your service routes to automatically:
// - Create root transactions for new requests
// - Continue traces from upstream services  
// - Capture service/method names, errors, and duration
// - Extract user context from JWT tokens

mux.Handle(orderPath,
    connect.NewHandler(
        orderService,
        connect.WithInterceptors(herosentry.RPCServiceInterceptor()),
    ),
)
```

##### For RPC Clients (Outgoing Calls)
```go
// Add to your service clients to automatically:
// - Create child spans for outgoing calls
// - Propagate trace headers to downstream services
// - Track external service performance
// - Capture failures and latency

assetClient := asset.NewClient(
    httpClient,
    url,
    connect.WithInterceptors(herosentry.RPCClientInterceptor()),
)

// Now all calls through this client are automatically traced!
asset, err := assetClient.GetAsset(ctx, &GetAssetRequest{Id: "123"})
```

##### For HTTP Endpoints (Webhooks, Health Checks)
```go
// Add to HTTP handlers to automatically:
// - Create transactions for HTTP requests
// - Capture method, path, and status codes
// - Handle panics gracefully
// - Extract client IP addresses

mux.Handle("/webhook/twilio",
    herosentry.HTTPMiddleware(
        http.HandlerFunc(handleTwilioWebhook),
    ),
)

mux.Handle("/health",
    herosentry.HTTPMiddleware(
        http.HandlerFunc(healthCheck),
    ),
)
```

### Daily Use (What You Do Every Day)

Once your service has the setup, these are the steps to capture any peace of event happening in your code. 

#### 1. Track Operations with Spans

```go
// In your business logic, just add spans where you want visibility
func CreateOrder(ctx context.Context, req *CreateOrderRequest) (*Order, error) {
    // Track this operation
    ctx, span, finish := herosentry.StartSpan(ctx, "OrderUseCase.CreateOrder")
    defer finish()
    
    // Add business context
    span.SetTag("order.type", req.Type)
    span.SetTag("order.priority", req.Priority)
    
    // Your code...
    return order, nil
}
```

#### 2. Capture Errors with Context

```go
if err := validateOrder(req); err != nil {
    // Automatically includes user, org, request ID, stack trace
    herosentry.CaptureException(ctx, err, "Order validation failed")
    return nil, err
}
```

#### 3. Add Custom Tags and Data

```go
// Tags are searchable strings
span.SetTag("payment.method", "stripe")
span.SetTag("feature.flag", "new-checkout")

// Data can be complex objects
span.SetData("order.items", orderItems)
span.SetData("validation.errors", validationResult)
```

That's it! The interceptors handle all the complex distributed tracing automatically.

## What Gets Auto-Captured

### User Context
- **Username**: From JWT token (e.g., "cognito:abc123" or "bot:service-name")
- **Organization ID**: From JWT claims
- **IP Address**: From request headers (X-Forwarded-For, etc.)

### Code Location
- **File**: Relative path to the file
- **Line**: Line number where span was created
- **Function**: Function name

### Request Context
- **Request ID**: From X-Request-ID or similar headers
- **Environment**: From ENVIRONMENT env var
- **Service Name**: From Init() call

### RPC Endpoints (Automatically Captured)
- **Service Name**: Extracted from procedure (e.g., "OrderService")
- **Method Name**: Extracted from procedure (e.g., "CreateOrder")
- **Transaction Name**: Combined as "OrderService.CreateOrder"
- **RPC Type**: "server" for incoming, "client" for outgoing
- **Error Status**: Automatically set on failures

### HTTP Endpoints (Automatically Captured)
- **HTTP Method**: GET, POST, PUT, DELETE, etc.
- **HTTP Path**: The request path (e.g., "/webhook/twilio")
- **Transaction Name**: Combined as "POST /webhook/twilio"
- **Status Code**: HTTP response status
- **Full URL**: Complete request URL

### Database Connection Pool Monitoring

The SDK automatically captures database connection pool metrics when configured. These metrics help identify connection bottlenecks and optimize pool configurations.

#### Setting Up Pool Monitoring

In your service's `main.go`, wrap your handler with the database pool middleware:

```go
func main() {
    // Initialize database
    postGresDB, err := sql.Open("postgres", databaseURL)
    if err != nil {
        log.Fatal(err)
    }
    
    // Configure connection pool
    postGresDB.SetMaxOpenConns(100)
    postGresDB.SetMaxIdleConns(25)
    postGresDB.SetConnMaxLifetime(5 * time.Minute)
    
    // Create your base handler
    baseMux := http.NewServeMux()
    // ... register routes ...
    
    // Wrap with database pool monitoring
    mux := herosentry.DBPoolMiddleware(postGresDB)(baseMux)
    
    // Use the wrapped mux in your server
    srv := middleware.NewServerWithHealth(mux, healthMux, true)
}
```

#### Metrics Captured

**IMPORTANT**: These metrics are PER-SERVICE, not database-wide. Each service maintains its own independent connection pool.

| Metric | Description | What It Indicates |
|--------|-------------|-------------------|
| `db.pool.open_connections` | Total connections from THIS SERVICE (idle + in-use) | Current pool size, should be ≤ MaxOpenConns |
| `db.pool.in_use` | Connections actively executing queries | Service load on database |
| `db.pool.idle` | Connections ready for use | Available capacity |
| `db.pool.wait_count` | Times service waited for a connection | Pool saturation frequency |
| `db.pool.wait_duration_ms` | Total milliseconds spent waiting | Connection starvation severity |
| `db.pool.max_idle_closed` | Connections closed for exceeding MaxIdleConns | Pool shrinking activity |
| `db.pool.max_lifetime_closed` | Connections closed for age | Normal recycling rate |

#### Interpreting the Metrics

**Example from a single service (e.g., workflow-service):**
```
db.pool.open_connections: 50   // This service has 50 connections
db.pool.in_use: 45            // 45 are running queries
db.pool.idle: 5               // 5 are available
db.pool.wait_count: 1000      // Waited 1000 times
db.pool.wait_duration_ms: 5000 // Total 5 seconds waiting
```

This indicates:
- **High utilization** (90%) - consider increasing MaxOpenConns
- **Connection waiting** - pool is undersized for workload
- **Average wait** - 5ms per wait (5000ms / 1000 waits)

**Multi-Service Example:**
- Perms service: 75/80 connections (94% utilization) 
- Workflow service: 20/100 connections (20% utilization)
- Communications: 45/50 connections (90% utilization)
- **Database total**: 140+ connections across all services

#### Pool Tuning Guidelines

| Symptom | Metrics Pattern | Solution |
|---------|-----------------|----------|
| Slow requests | High `wait_count` and `wait_duration_ms` | Increase `MaxOpenConns` |
| Connection errors | `in_use` equals `open_connections` frequently | Increase `MaxOpenConns` |
| Wasted resources | High `idle` count consistently | Decrease `MaxIdleConns` |
| Many reconnections | High `max_lifetime_closed` | Increase `ConnMaxLifetime` |
| Idle connection churn | High `max_idle_closed` | Increase `MaxIdleConns` or decrease `ConnMaxIdleTime` |

#### Service-Specific Pool Sizes

Different services need different pool configurations based on their workload:

```go
// High-traffic service (e.g., perms - every request needs auth)
postGresDB.SetMaxOpenConns(80)
postGresDB.SetMaxIdleConns(20)  // 25% idle ratio

// Medium-traffic service (e.g., workflow)
postGresDB.SetMaxOpenConns(100)
postGresDB.SetMaxIdleConns(25)  // 25% idle ratio

// Low-traffic service (e.g., featureflags - mostly cached)
postGresDB.SetMaxOpenConns(25)
postGresDB.SetMaxIdleConns(5)   // 20% idle ratio

// Real-time service (e.g., communications)
postGresDB.SetMaxOpenConns(50)
postGresDB.SetMaxIdleConns(15)  // 30% idle ratio for quick response
```

### Example Sentry Output

**Transaction View:**
```
Transaction: OrderService.CreateOrder
├─ trace.id: d4cda95b652f4a1592b449d5929fda1b
├─ span.id: 6e0c63257de34c92
├─ user.username: cognito:abc123-def456
├─ org.id: 42
├─ user.ip_address: *************
├─ code.file: services/workflow/internal/orders/usecase.go
├─ code.line: 45
├─ code.function: (*OrderUsecase).CreateOrder
├─ environment: production
├─ service: workflow-service
├─ rpc.service: OrderService
├─ rpc.method: CreateOrder
├─ rpc.type: server
├─ span.kind: server
├─ order.type: express (custom tag)
├─ order.amount: 99.99 (custom data)
└─ duration: 234ms
```

**Error View with Enhanced Formatting:**
```
Title: [workflow-service] Failed to process payment for order ORD-12345
Type: [workflow-service] Failed to process payment for order ORD-12345

Tags:
├─ service: workflow-service
├─ error.type: *pq.Error
├─ error.file: services/workflow/internal/orders/usecase.go
├─ error.line: 145
├─ error.function: (*OrderUsecase).ProcessPayment
├─ user.username: cognito:abc123
├─ org.id: 42
└─ request.id: 550e8400-e29b-41d4

Extra Context:
├─ original_error_type: *pq.Error
└─ message: Failed to process payment for order ORD-12345
```

**Panic View:**
```
Title: [featureflags-service] Panic in FeatureFlagsService.IsEnabled
Type: [featureflags-service] Panic in FeatureFlagsService.IsEnabled

Tags:
├─ service: featureflags-service
├─ error: true
├─ error.type: panic
├─ rpc.service: FeatureFlagsService
├─ rpc.method: IsEnabled
└─ org.id: 99999
```

## API Reference

The herosentry SDK provides a minimal public API - everything else is handled automatically!

**For Application Code:** Use only these functions:
- `Init()` - Initialize once in main()
- `Flush()` - Call before shutdown
- `StartSpan()` - Track operations (automatically handles transactions)
- `CurrentSpan()` - Add data to existing span
- `CaptureException()` - Report errors

**For Middleware/Interceptors:** Additional functions:
- `RPCServiceInterceptor()` - For incoming RPC
- `RPCClientInterceptor()` - For outgoing RPC  
- `HTTPMiddleware()` - For HTTP endpoints
- `TraceContextMiddleware()` - For trace extraction

Note: `StartTransaction()` is internal - application code should always use `StartSpan()`.

### Initialization Functions

#### `Init(serviceName string, config ...Config) error`
Initializes Sentry for the service. Call once in main().

```go
// Basic initialization
err := herosentry.Init("my-service")

// With custom configuration
err := herosentry.Init("my-service", herosentry.Config{
    Environment:           "staging",
    DevelopmentSampleRate: &customDevRate,
    ProductionSampleRate:  &customProdRate,
})
```

#### `Flush()`
Flushes all pending events to Sentry. Call before shutdown.

```go
defer herosentry.Flush()  // Always call before shutdown
```

### Span Functions

#### `StartSpan(ctx context.Context, operationName string) (context.Context, Span, FinishFunc)`
Starts a new span. Automatically creates child spans or transactions as needed. Returns the span for immediate use.

```go
ctx, span, finish := herosentry.StartSpan(ctx, "database.query")
defer finish()
span.SetTag("db.table", "orders")
```

#### `CurrentSpan(ctx context.Context) Span`
Gets the current span to add custom data without creating a new one.

```go
if span := herosentry.CurrentSpan(ctx); span != nil {
    span.SetTag("db.table", "orders")
    span.SetData("query", sqlQuery)
}
```

### Error Handling

#### `CaptureException(ctx context.Context, err error, message ...string)`
Captures an error with full context and enhanced formatting. Optionally accepts a custom message.

**Enhanced Error Formatting**: Errors now appear in Sentry with descriptive titles instead of generic type names:
- **Before**: `*connect.Error` or `*errors.errorString`
- **After**: `[service-name] Error message` or `[service-name] Custom message`

**Automatic Deduplication**: The SDK automatically prevents duplicate error captures within a single request/trace:
- When the same error flows through multiple layers (e.g., usecase → connect), it's only sent to Sentry once
- The first capture point preserves the original context where the error occurred
- Different errors are still captured separately
- This reduces Sentry noise by ~50% without losing any unique error information

```go
// Basic usage
if err := db.Query(ctx, query); err != nil {
    herosentry.CaptureException(ctx, err)
    // Appears in Sentry as: [workflow-service] pq: relation "orders" does not exist
    return err
}

// With custom message
herosentry.CaptureException(ctx, err, "Failed to process payment for order " + orderID)
// Appears in Sentry as: [workflow-service] Failed to process payment for order ORD-12345

// Example: Error deduplication in action
func (uc *UseCase) CreateOrder(ctx context.Context, ...) error {
    if err := validateOrder(order); err != nil {
        herosentry.CaptureException(ctx, err, "Order validation failed")  // ✅ Sent to Sentry
        return err
    }
}

func (s *Server) CreateOrder(ctx context.Context, ...) {
    order, err := uc.CreateOrder(ctx, ...)
    if err != nil {
        // This uses AsConnectError which calls CaptureException internally
        return connecthelper.AsConnectError(ctx, err, "CreateOrder")  // ❌ Skipped (duplicate)
    }
}
// Result: Only ONE Sentry event for this error, not two!
```

The error handling features:
1. Extracts the service name from initialization
2. Uses custom message if provided, otherwise uses error message
3. Preserves original error type in `event.Extra["original_error_type"]`
4. Maintains full stack trace for debugging
5. Tracks captured errors per request to prevent duplicates

### Middleware

#### `TraceContextMiddleware(handler http.Handler) http.Handler`
Extracts trace headers from incoming HTTP requests and stores them in context. **MUST be the first middleware in the chain** for distributed tracing to work correctly.

```go
// Applied automatically by middleware.NewServerWithHealth()
// Or manually:
handler = herosentry.TraceContextMiddleware(handler)
handler = AuthMiddleware(handler)  // Auth AFTER trace context
```

### Interceptors

#### `RPCServiceInterceptor() connect.Interceptor`
For incoming RPC requests (server-side). This interceptor:
- Creates root transactions for new requests or continues existing traces
- Automatically names transactions as "ServiceName.MethodName"
- Captures all errors and panics with full context across all execution paths
- Extracts user/org context from JWT tokens
- Sets `rpc.service`, `rpc.method`, `rpc.type: "server"` tags

**Panic Handling**: The interceptor captures panics in all three execution paths:
1. When middleware already created a span (e.g., permissions middleware)
2. When continuing a distributed trace from another service
3. When creating a new root transaction

Panics are:
- Captured with formatted titles: `[service-name] Panic in ServiceName.MethodName`
- Flushed to Sentry with a 2-second timeout to ensure delivery
- Re-thrown to maintain Go's panic behavior

```go
mux.Handle(orderPath,
    connect.NewHandler(
        orderService,
        connect.WithInterceptors(herosentry.RPCServiceInterceptor()),
    ),
)
```

#### `RPCClientInterceptor() connect.Interceptor`
For outgoing RPC calls (client-side). This interceptor:
- Creates child spans named "calling ServiceName.MethodName"
- Propagates trace context via `sentry-trace` header
- Tracks call duration and success/failure
- Sets `rpc.service`, `rpc.method`, `rpc.type: "client"` tags
- Enables distributed tracing across services

```go
client := asset.NewClient(
    httpClient,
    url,
    connect.WithInterceptors(herosentry.RPCClientInterceptor()),
)
```

#### `HTTPMiddleware(handler http.Handler) http.Handler`
For HTTP endpoints (webhooks, health checks, etc.). This middleware:
- Creates transactions named "METHOD /path"
- Captures HTTP method, path, status code, and URL
- Handles panics with enhanced formatting and proper flushing
- Extracts client IP from headers
- Sets `http.method`, `http.path`, `http.status_code` tags

**Panic Handling**: 
- Captures panics as `[service-name] HTTP Panic in METHOD /path`
- Flushes to Sentry before re-panicking
- Returns 500 status code if response not already written

```go
http.Handle("/webhook/twilio", 
    herosentry.HTTPMiddleware(webhookHandler),
)
```

## Searching in Sentry

### Finding Your Traces

When debugging a specific operation like `CreateSituation`, here's how to find it in Sentry:

#### 1. Search by Transaction Name
```
transaction:SituationService.CreateSituation
```

#### 2. Search by Service
```
service:workflow-service
```

#### 3. Search by User
```
user.username:cognito:abc123-def456
```

#### 4. Search by Custom Tags
```
situation.type:INCIDENT
situation.priority:1
```

#### 5. Search by Error
```
error:true
```

#### 6. Combine Filters
```
transaction:*CreateSituation* environment:production error:true
```

### Narrowing Down Issues

1. **Time Range**: Use the time picker to focus on when the issue occurred
2. **Filter by Environment**: `environment:production` vs `environment:development`
3. **Filter by Organization**: `org.id:42`
4. **Trace View**: Click on a transaction to see the full distributed trace
5. **Breadcrumbs**: Check the timeline of events leading to an error

### Example: Debugging a Failed CreateSituation

1. Go to Sentry → Performance → Transactions
2. Search: `transaction:SituationService.CreateSituation error:true`
3. Click on a failed transaction
4. View the trace to see:
   - Which step failed (validation, database, external service?)
   - User context (who triggered it?)
   - Custom data (what situation type?)
   - Full stack trace if an exception occurred

## Distributed Tracing

### Architecture Overview

The SDK implements a sophisticated distributed tracing system that ensures single, unified traces across all services:

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│ Workflow Service│────▶│ Perms Service   │────▶│ Asset Service   │
└─────────────────┘     └─────────────────┘     └─────────────────┘
        │                       │                       │
        └───────────────────────┴───────────────────────┘
                    Single Unified Trace
```

### Key Components

1. **TraceContextMiddleware** - Extracts trace headers from incoming requests BEFORE any other processing
2. **RPCServiceInterceptor** - Handles incoming RPC requests, creates or continues traces
3. **RPCClientInterceptor** - Propagates trace context to downstream services
4. **Baggage Headers** - Carries additional context like dynamic sampling configuration

### How It Works

1. **Trace Context Extraction** (TraceContextMiddleware)
   - Runs FIRST in the middleware chain
   - Extracts `sentry-trace` and `baggage` headers
   - Stores them in context for later use

2. **Intelligent Trace Handling** (RPCServiceInterceptor)
   - Checks if middleware already created a transaction
   - If yes: Uses existing transaction (prevents duplicates)
   - If no: Checks for trace headers to continue distributed trace
   - Otherwise: Creates new root transaction

3. **Automatic Propagation** (RPCClientInterceptor)
   - Adds trace headers to outgoing requests
   - Ensures downstream services continue the same trace
   - Handles both `sentry-trace` and `baggage` headers

4. **Span Creation** (Your Code)
   - Use `StartSpan()` to track operations
   - SDK automatically creates child spans under the current transaction

### Visual Example

```
User Request → Workflow Service
              │
              ├─ [Transaction] OrderService.CreateOrder  ← Created by RPCServiceInterceptor
              │   ├─ [Span] order.validate               ← You create this with StartSpan
              │   ├─ [Span] calling AssetService.GetAsset ← Created by RPCClientInterceptor
              │   │                                          │
              │   │                                          └─ [Transaction] AssetService.GetAsset
              │   │                                              └─ [Span] database.query
              │   └─ [Span] database.insert              ← You create this with StartSpan
              │
              └─ Response to User
```

**Key Point**: You never manage traces directly. Just add spans where you want visibility!

### Span Operation Types (span.op)

The `span.op` field categorizes operations in Sentry for better filtering and performance insights.

#### Automatically Set by Interceptors/Middleware

These operation types are set by the SDK's interceptors and middleware - you don't need to set them:

| Operation Type | Description | Set By |
|----------------|-------------|---------|
| `rpc.server` | Incoming RPC request handler | RPCServiceInterceptor or permissions middleware |
| `rpc.client` | Outgoing RPC call | RPCClientInterceptor |
| `http.server` | HTTP server request | HTTPMiddleware |

#### Automatically Inferred from Operation Names

These operation types are inferred by the SDK based on your operation naming patterns:

| Operation Type | Description | Pattern Match |
|----------------|-------------|---------------|
| `db` | Database operation | Operation name contains "database", "db", "sql", or "query" |
| `cache` | Cache operation | Operation name contains "cache", "redis", or "memcache" |
| `queue` | Message queue operation | Operation name contains "queue", "message", "publish", or "consume" |
| `usecase` | Business logic layer | Operation name contains "usecase" |
| `repository` | Data access layer | Operation name contains "repository" or "repo" |
| `rpc` | Generic RPC operation | Operation name contains "rpc" or "grpc" |
| `http` | Generic HTTP operation | Operation name contains "http", "api", or "rest" |
| `function` | Generic function call | Default when no pattern matches |

**Examples:**
- `SituationService.GetSituation` → `span.op = "rpc.server"` (incoming RPC)
- `calling PermissionService.CheckPermission` → `span.op = "rpc.client"` (outgoing RPC)
- `SituationUseCase.GetSituation` → `span.op = "usecase"` (business logic)
- `SituationRepository.GetByID` → `span.op = "repository"` (data access)
- `database.query` → `span.op = "db"` (database operation)

**Note**: The SDK automatically infers operation types based on naming patterns, but you can explicitly set them when needed using `StartTransaction(ctx, name, "custom.type")`.

## Configuration

### Disabling Sentry

To completely disable Sentry in your service (for testing, development, or debugging), set the `DISABLE_SENTRY` environment variable:

```bash
export DISABLE_SENTRY=true
```

When `DISABLE_SENTRY` is set to `true`, the SDK will:
- Skip all Sentry initialization
- Make all operations no-ops (no data sent to Sentry)
- Continue running without errors
- Print a warning: `WARNING: DISABLE_SENTRY is set. Herosentry will operate in no-op mode.`

This is useful for:
- **Local development** when you don't want telemetry data
- **Testing environments** where Sentry integration isn't needed
- **Debugging** to isolate issues from tracing overhead
- **CI/CD pipelines** where telemetry might interfere with tests

Note: This takes precedence over `SENTRY_DSN` - even if DSN is set, `DISABLE_SENTRY=true` will disable all functionality.

### Automatic Sampling Rates

The SDK automatically adjusts sampling rates based on the `ENVIRONMENT` variable:

| Environment | Sample Rate | Description |
|-------------|-------------|-------------|
| `development` | 100% | All transactions captured for debugging |
| Any other value | 10% | Reduced sampling for production environments |

### Custom Configuration

You can override defaults using the optional `Config` parameter:

```go
// Custom sampling rates
devRate := 0.5    // 50% in development
prodRate := 0.05  // 5% in production

if err := herosentry.Init("my-service", herosentry.Config{
    DataSourceName:        "https://<EMAIL>/123",
    Environment:           "staging",
    DevelopmentSampleRate: &devRate,
    ProductionSampleRate:  &prodRate,
    Release:               "v2.0.0",
}); err != nil {
    log.Printf("Failed to initialize Sentry: %v", err)
}
```

### Error Filtering Configuration

By default, ALL errors are captured and sent to Sentry (backward compatible). You can configure the SDK to filter out specific error types that are expected in normal operation:

```go
// Example: Filter out validation and not-found errors
falseVal := false
trueVal := true

err := herosentry.Init("my-service", herosentry.Config{
    // Filter out common expected errors
    CaptureValidationErrors: &falseVal,  // Don't capture validation errors
    CaptureNotFoundErrors:   &falseVal,  // Don't capture 404/not found errors
    
    // Still capture important errors (these are true by default, shown for clarity)
    CaptureConflictErrors:    &trueVal,  // Capture conflict/duplicate errors
    CaptureUnauthorizedErrors: &trueVal,  // Capture 401 unauthorized
    CaptureForbiddenErrors:   &trueVal,  // Capture 403 forbidden/permission errors
})
```

**Note**: We use pointers to bool (`*bool`) to distinguish between "not set" (nil) and "explicitly set to false". This allows the SDK to apply defaults only when a value is not provided.

**Available Error Filters:**

| Config Field | Default | Description | Example Errors |
|--------------|---------|-------------|----------------|
| `CaptureNotFoundErrors` | `true` | Capture 404/not found errors | `sql.ErrNoRows`, "user not found", "does not exist" |
| `CaptureValidationErrors` | `true` | Capture validation/bad request errors | "invalid email", "validation error", "required field" |
| `CaptureConflictErrors` | `true` | Capture conflict/duplicate errors | "already exists", "duplicate key", "conflict" |
| `CaptureUnauthorizedErrors` | `true` | Capture 401 unauthorized errors | "unauthorized", "not authorized", "token expired" |
| `CaptureForbiddenErrors` | `true` | Capture 403 forbidden errors | "forbidden", "permission denied", "access denied" |

**How Error Filtering Works:**

1. The SDK examines each error to determine its type
2. For `DomainError` types (from `common/connect`), it uses specific patterns from the error message
3. For other errors, it uses pattern matching on the error message
4. If an error type is configured as `false`, it won't be sent to Sentry
5. All other errors are captured by default

**Example Use Case:**

```go
// In a high-traffic service where validation errors are common
falseVal := false

err := herosentry.Init("api-gateway", herosentry.Config{
    // Filter out noise from expected errors
    CaptureValidationErrors: &falseVal,  // User input errors
    CaptureNotFoundErrors:   &falseVal,  // 404s from invalid URLs
    
    // Keep capturing system errors
    // (Unauthorized, Forbidden, Conflicts, and all other errors still captured)
})

// Later in your code
if err := validateEmail(email); err != nil {
    // This validation error will NOT be sent to Sentry (filtered)
    herosentry.CaptureException(ctx, err, "Email validation failed")
    return err
}

if err := db.Query(ctx, query); err != nil {
    // This database error WILL be sent to Sentry (not filtered)
    herosentry.CaptureException(ctx, err, "Database query failed")
    return err
}
```

**Force Capture Override:**

Sometimes you need to capture a specific error even if its type is filtered. You can override the filter by passing `true` as an additional parameter:

```go
// This critical validation error will be captured despite the filter
herosentry.CaptureException(ctx, validationErr, "Critical validation failure", true)
```

**Best Practices for Error Filtering:**

1. **Start with defaults**: Capture everything initially to understand your error patterns
2. **Filter gradually**: Once you identify noisy errors, filter them out
3. **Monitor impact**: Ensure you're not missing important errors after filtering
4. **Use force capture**: For critical errors that must be tracked despite filters
5. **Service-specific**: Different services may need different filtering strategies

**Complete Example:**

```go
func main() {
    // Configure error filtering for this service
    falseVal := false
    
    if err := herosentry.Init("workflow-service", herosentry.Config{
        // Only filter out truly expected errors
        CaptureValidationErrors: &falseVal,  // User input validation
        CaptureNotFoundErrors:   &falseVal,  // Resource not found (404s)
        // All other errors captured by default
    }); err != nil {
        log.Printf("Failed to initialize Sentry: %v", err)
    }
    defer herosentry.Flush()
    
    // Your service code...
}
```

### Custom Sampling Rules

For fine-grained control over sampling, you can set per-operation sampling rates. This is especially useful for high-volume operations that would otherwise flood Sentry with too many transactions.

```go
err := herosentry.Init("workflow-service", herosentry.Config{
    CustomSamplingRules: map[string]float64{
        // Exact match rules
        "SituationUseCase.ListSituations": 0.01, // 1% sampling for specific operation
        "OrderUseCase.ListOrders":         0.01, // 1% sampling
        "CheckPermission":                 0.05, // 5% for permission checks
        
        // Wildcard patterns
        "List*":                           0.01, // 1% for any operation starting with "List"
        "*Permission*":                    0.05, // 5% for any operation containing "Permission"
        "*.Create*":                       0.3,  // 30% for any Create operation
        "*Health":                         0.0,  // 0% for operations ending with "Health"
        
        // UseCase patterns
        "*UseCase.List*":                  0.01, // 1% for all UseCase List operations
        "PaymentUseCase.*":                0.5,  // 50% for all PaymentUseCase operations
        
        // Repository patterns
        "*Repository.*":                   0.1,  // 10% for all repository operations
        "*Repository.BatchGet*":           0.05, // 5% for batch get operations
    },
})
```

**Pattern Matching Rules:**
1. **Exact match** always takes precedence (e.g., `"OrderUseCase.ListOrders"`)
2. **Prefix wildcard** `"*suffix"` - matches operations ending with suffix
3. **Suffix wildcard** `"prefix*"` - matches operations starting with prefix
4. **Contains wildcard** `"*middle*"` - matches operations containing middle
5. Falls back to environment-based default rate if no pattern matches

**Rule Priority and Overwrites:**
- **Exact matches always win**: If you have both `"OrderUseCase.ListOrders": 0.01` and `"List*": 0.1`, the exact match (0.01) will be used
- **Pattern matches are non-deterministic**: When multiple patterns match, the order is undefined due to Go's map iteration
- **Avoid overlapping patterns**: If both `"*UseCase*": 0.1` and `"List*": 0.01` match, either could be selected
- **Be specific when possible**: Use exact matches for critical operations to ensure predictable behavior

**Example of Rule Conflicts:**
```go
CustomSamplingRules: map[string]float64{
    // For "OrderUseCase.ListOrders":
    "OrderUseCase.ListOrders": 0.01,  // ✓ This exact match will always be used
    "List*":                   0.05,  // ✗ Ignored due to exact match
    "*UseCase*":               0.1,   // ✗ Ignored due to exact match
    
    // For "SituationUseCase.GetSituation":
    "*UseCase*":               0.1,   // ? One of these will be used
    "SituationUseCase.*":      0.2,   // ? But which one is undefined!
    
    // Best practice: Be explicit
    "SituationUseCase.GetSituation": 0.15,  // ✓ Add exact match to avoid ambiguity
}
```

**How Sampling Works:**
1. **Distributed traces are always preserved**: If a parent span is sampled, all child spans are sampled (100%)
2. **Errors can be always sampled**: Set `AlwaysSampleErrors: true` to ensure error transactions are captured
3. **Pattern matching is case-sensitive**: `"List*"` won't match `"listOrders"`
4. **First match wins**: If multiple patterns match, the first one found is used

**Best Practices:**
- Use **0.01-0.05** (1-5%) for high-frequency read operations (List, Get)
- Use **0.1-0.3** (10-30%) for important write operations (Create, Update)
- Use **0.5-1.0** (50-100%) for critical business operations (Payment, Order)
- Use **0.0** (0%) to completely disable sampling for health checks or monitoring endpoints
- Monitor your Sentry quota and adjust rates based on actual usage

**Recommended Pattern Design:**
```go
CustomSamplingRules: map[string]float64{
    // 1. Start with broad patterns for categories
    "*Repository*":     0.05,  // 5% for all repository operations
    "*UseCase*":        0.1,   // 10% for all use case operations
    "List*":            0.01,  // 1% for all list operations
    
    // 2. Override with more specific patterns
    "PaymentUseCase*":  0.5,   // 50% for payment operations
    "*Critical*":       0.8,   // 80% for critical operations
    
    // 3. Use exact matches for fine control
    "OrderUseCase.CreateOrder":        0.3,   // 30% for order creation
    "PaymentUseCase.ProcessPayment":   1.0,   // 100% for payment processing
    "HealthCheck":                     0.0,   // 0% for health checks
}
```
This hierarchy ensures predictable behavior: exact matches override specific patterns, which override broad patterns.

**Example with Complete Config:**
```go
alwaysSampleErrors := true

err := herosentry.Init("my-service", herosentry.Config{
    // Global sampling rates
    DevelopmentSampleRate: &devRate,  // 100% in dev
    ProductionSampleRate:  &prodRate,  // 10% in prod
    
    // Always capture errors regardless of sampling
    AlwaysSampleErrors: &alwaysSampleErrors,
    
    // Custom rules for specific operations
    CustomSamplingRules: map[string]float64{
        // Reduce noise from high-volume operations
        "List*":            0.01,
        "*Health*":         0.0,
        "CheckPermission":  0.05,
        
        // Increase sampling for important operations
        "ProcessPayment":   0.8,
        "CreateOrder":      0.5,
        "*Critical*":       1.0,
    },
})
```

## Best Practices

1. **Always use the provided server setup**: `middleware.NewServerWithHealth()` ensures correct middleware ordering
2. **Use descriptive operation names**: `"OrderUseCase.CreateOrder"` instead of `"create"`
3. **Add business context**: Use SetTag for searchable strings, SetData for complex objects
4. **Let auto-capture work**: Don't manually set user/org/IP - the SDK does this
5. **One span per meaningful operation**: Don't create spans for every function call
6. **Handle errors consistently**: Always use CaptureException for errors
7. **Apply interceptors consistently**: Both service and client interceptors for full tracing
8. **Don't create duplicate transactions**: The SDK handles this intelligently

## Troubleshooting

### Events not appearing in Sentry?
1. Check `SENTRY_DSN` is set correctly
2. Ensure `herosentry.Init()` is called before any operations
3. Call `herosentry.Flush()` before service shutdown
4. Check service logs for initialization errors

### Panics not being captured?
1. Ensure the interceptor is properly applied to your routes
2. Check that panic handling is present in all execution paths (the interceptor handles this automatically)
3. Verify that the 2-second flush timeout is sufficient for your network conditions
4. Check logs for "panic" messages - the service should still crash after sending to Sentry

### Missing context data?
1. Ensure authentication middleware runs before your handlers
2. Check that context is properly propagated through your call chain
3. Use `CurrentSpan(ctx)` to verify span exists before adding data

### Traces not connecting across services?

1. **Check middleware order**: The most common cause is incorrect middleware ordering
   ```go
   // WRONG - Auth runs before trace context extraction
   handler = AuthMiddleware(handler)
   handler = herosentry.TraceContextMiddleware(handler)
   
   // CORRECT - Trace context MUST run first
   handler = herosentry.TraceContextMiddleware(handler)
   handler = AuthMiddleware(handler)
   ```

2. **Use the provided server setup**: 
   ```go
   // This ensures correct middleware ordering automatically
   srv := middleware.NewServerWithHealth(":8080", mux, healthMux, true)
   ```

3. **Verify interceptors are applied**:
   - Service interceptor on incoming routes
   - Client interceptor on outgoing clients

4. **Check for trace header stripping**:
   - Some proxies/load balancers strip custom headers
   - Ensure `sentry-trace` and `baggage` headers are allowed

5. **Debug with logs**: The SDK logs trace propagation details
   - Look for "continued transaction" messages
   - Check for "Trace ID mismatch" warnings