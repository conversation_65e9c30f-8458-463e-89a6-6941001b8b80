package connect

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"strings"

	"common/herosentry"

	"connectrpc.com/connect"
)

// ErrorType represents different categories of errors
type ErrorType int

const (
	ErrorTypeUnknown ErrorType = iota
	ErrorTypeNotFound
	ErrorTypeValidation
	ErrorTypeConflict
	ErrorTypeUnauthorized
	ErrorTypeForbidden
	ErrorTypeInternal
	ErrorTypeUnavailable
	ErrorTypeTimeout
	ErrorTypeUnprocessable
)

// DomainError represents a business logic error with a specific type
type DomainError struct {
	Type    ErrorType
	Message string
	Err     error
}

func (e *DomainError) Error() string {
	if e.Err != nil {
		return fmt.Sprintf("%s: %v", e.Message, e.Err)
	}
	return e.Message
}

func (e *DomainError) Unwrap() error {
	return e.Err
}

// NewDomainError creates a new domain error
func NewDomainError(errorType ErrorType, message string, err error) *DomainError {
	return &DomainError{
		Type:    errorType,
		Message: message,
		Err:     err,
	}
}

// AsConnectError converts any error to a Connect error with proper code and HeroSentry capture
func AsConnectError(ctx context.Context, err error, operation string) error {
	if err == nil {
		return nil
	}

	// Capture to HeroSentry with context
	captureToSentry(ctx, err, operation)

	// Determine the Connect error code based on error type
	code := determineErrorCode(err)

	// Create detailed error message
	message := createErrorMessage(err, operation)

	return connect.NewError(code, fmt.Errorf("%s", message))
}

// AsConnectErrorWithCode converts an error to a Connect error with a specific code
func AsConnectErrorWithCode(ctx context.Context, err error, code connect.Code, operation string) error {
	if err == nil {
		return nil
	}

	// Capture to HeroSentry with context
	captureToSentry(ctx, err, operation)

	// Create detailed error message
	message := createErrorMessage(err, operation)

	return connect.NewError(code, fmt.Errorf("%s", message))
}

// captureToSentry captures the error to HeroSentry with appropriate context
func captureToSentry(ctx context.Context, err error, operation string) {
	// Add operation context to the current span if available
	if span := herosentry.CurrentSpan(ctx); span != nil {
		span.SetTag("error", "true")
		span.SetTag("error.type", getErrorType(err))
		span.SetData("error.operation", operation)
		span.SetData("error.message", err.Error())
	}

	// Create a more descriptive title for Sentry
	// Format: "Operation: error summary"
	sentryTitle := createSentryTitle(operation, err)

	// Capture the exception with context
	// The CaptureException function now handles:
	// 1. Error type filtering based on configuration
	// 2. Deduplication within the same trace
	// - If this error type is configured to be filtered, it will be skipped
	// - If this error was already captured in a lower layer (e.g., usecase), it will be skipped
	// - If this is the first capture of this error and it passes filters, it will be sent to Sentry
	// - This prevents duplicate Sentry events and reduces noise from expected errors
	herosentry.CaptureException(ctx, err, sentryTitle)
}

// createSentryTitle creates a descriptive title for Sentry that includes operation and error context
func createSentryTitle(operation string, err error) string {
	errMsg := err.Error()

	// Truncate error message if too long for title
	const maxErrorLength = 100
	if len(errMsg) > maxErrorLength {
		errMsg = errMsg[:maxErrorLength] + "..."
	}

	// Determine error category for better context
	errorType := getErrorType(err)

	// Special handling for common error types
	switch errorType {
	case "not_found":
		return fmt.Sprintf("%s: Resource not found", operation)
	case "validation":
		return fmt.Sprintf("%s: Validation failed - %s", operation, errMsg)
	case "conflict":
		return fmt.Sprintf("%s: Conflict - %s", operation, errMsg)
	case "unauthorized":
		return fmt.Sprintf("%s: Unauthorized access", operation)
	case "forbidden":
		return fmt.Sprintf("%s: Permission denied", operation)
	case "timeout":
		return fmt.Sprintf("%s: Operation timed out", operation)
	case "canceled":
		return fmt.Sprintf("%s: Operation canceled", operation)
	default:
		// For internal errors, include the error message for context
		return fmt.Sprintf("%s: %s", operation, errMsg)
	}
}

// determineErrorCode maps errors to appropriate Connect error codes
func determineErrorCode(err error) connect.Code {
	// Check for DomainError type
	var domainErr *DomainError
	if errors.As(err, &domainErr) {
		switch domainErr.Type {
		case ErrorTypeNotFound:
			return connect.CodeNotFound
		case ErrorTypeValidation:
			return connect.CodeInvalidArgument
		case ErrorTypeConflict:
			return connect.CodeAlreadyExists
		case ErrorTypeUnauthorized:
			return connect.CodeUnauthenticated
		case ErrorTypeForbidden:
			return connect.CodePermissionDenied
		case ErrorTypeUnavailable:
			return connect.CodeUnavailable
		case ErrorTypeTimeout:
			return connect.CodeDeadlineExceeded
		case ErrorTypeUnprocessable:
			return connect.CodeFailedPrecondition
		default:
			return connect.CodeInternal
		}
	}

	// Check for standard database errors
	if errors.Is(err, sql.ErrNoRows) {
		return connect.CodeNotFound
	}

	// Check for context errors
	if errors.Is(err, context.Canceled) {
		return connect.CodeCanceled
	}
	if errors.Is(err, context.DeadlineExceeded) {
		return connect.CodeDeadlineExceeded
	}

	// Check error message patterns for common cases
	errMsg := strings.ToLower(err.Error())

	// Not found patterns
	if strings.Contains(errMsg, "not found") || strings.Contains(errMsg, "does not exist") {
		return connect.CodeNotFound
	}

	// Validation patterns
	if strings.Contains(errMsg, "invalid") || strings.Contains(errMsg, "validation") ||
		strings.Contains(errMsg, "required") || strings.Contains(errMsg, "must be") {
		return connect.CodeInvalidArgument
	}

	// Conflict patterns
	if strings.Contains(errMsg, "already exists") || strings.Contains(errMsg, "duplicate") ||
		strings.Contains(errMsg, "conflict") {
		return connect.CodeAlreadyExists
	}

	// Permission patterns
	if strings.Contains(errMsg, "unauthorized") || strings.Contains(errMsg, "not authorized") {
		return connect.CodeUnauthenticated
	}
	if strings.Contains(errMsg, "forbidden") || strings.Contains(errMsg, "permission") ||
		strings.Contains(errMsg, "access denied") {
		return connect.CodePermissionDenied
	}

	// Resource exhausted patterns
	if strings.Contains(errMsg, "quota") || strings.Contains(errMsg, "limit exceeded") ||
		strings.Contains(errMsg, "too many") {
		return connect.CodeResourceExhausted
	}

	// Default to internal error for unknown cases
	return connect.CodeInternal
}

// createErrorMessage creates a user-friendly error message
func createErrorMessage(err error, operation string) string {
	// For DomainError, use the custom message
	var domainErr *DomainError
	if errors.As(err, &domainErr) {
		if domainErr.Message != "" {
			return domainErr.Message
		}
	}

	// For known database errors
	if errors.Is(err, sql.ErrNoRows) {
		return "resource not found"
	}

	// For context errors
	if errors.Is(err, context.Canceled) {
		return "operation was canceled"
	}
	if errors.Is(err, context.DeadlineExceeded) {
		return "operation timed out"
	}

	// Default: return the original error
	return err.Error()
}

// getErrorType returns a string representation of the error type for logging
func getErrorType(err error) string {
	var domainErr *DomainError
	if errors.As(err, &domainErr) {
		switch domainErr.Type {
		case ErrorTypeNotFound:
			return "not_found"
		case ErrorTypeValidation:
			return "validation"
		case ErrorTypeConflict:
			return "conflict"
		case ErrorTypeUnauthorized:
			return "unauthorized"
		case ErrorTypeForbidden:
			return "forbidden"
		case ErrorTypeUnavailable:
			return "unavailable"
		case ErrorTypeTimeout:
			return "timeout"
		case ErrorTypeUnprocessable:
			return "unprocessable"
		case ErrorTypeInternal:
			return "internal"
		default:
			return "unknown"
		}
	}

	// Check for standard errors
	if errors.Is(err, sql.ErrNoRows) {
		return "not_found"
	}
	if errors.Is(err, context.Canceled) {
		return "canceled"
	}
	if errors.Is(err, context.DeadlineExceeded) {
		return "timeout"
	}

	return "internal"
}

// Helper functions for creating specific domain errors

// NotFoundError creates a not found domain error
func NotFoundError(resource string) *DomainError {
	return NewDomainError(ErrorTypeNotFound, fmt.Sprintf("%s not found", resource), nil)
}

// ValidationError creates a validation domain error
func ValidationError(field, message string) *DomainError {
	return NewDomainError(ErrorTypeValidation, fmt.Sprintf("validation error on %s: %s", field, message), nil)
}

// ConflictError creates a conflict domain error
func ConflictError(resource string) *DomainError {
	return NewDomainError(ErrorTypeConflict, fmt.Sprintf("%s already exists", resource), nil)
}

// InternalError creates an internal domain error
func InternalError(message string, err error) *DomainError {
	return NewDomainError(ErrorTypeInternal, message, err)
}
