# Task ID: 9
# Title: Enhanced Call Status Logging
# Status: pending
# Dependencies: 3, 8
# Priority: medium
# Description: Enhance existing /callstatus endpoint to handle forward event logging with eventType parameter
# Details:
Modify existing /callstatus handler to:
1. Accept eventType=forward query parameter
2. Log forward-specific events to call_forward_events table
3. Handle webhook parameters: DialCallSid, DialCallStatus, ParentCallSid, CallStatus, From, To
4. Maintain existing call status logging functionality
5. Add proper error handling for forward event logging
6. Ensure no TwiML response (200 OK only)

Reuse existing infrastructure while adding forward-specific logging capability.

# Test Strategy:
Test eventType parameter handling, verify forward event logging, ensure existing call status logging unaffected, test webhook parameter parsing, validate database insertions

# Subtasks:
## 1. Add eventType Parameter Handling to CallStatus Endpoint [pending]
### Dependencies: None
### Description: Modify the existing /callstatus endpoint handler to accept and parse the eventType query parameter, specifically handling eventType=forward while maintaining backward compatibility for existing calls without this parameter.
### Details:
Update the callstatus handler function to extract eventType from query parameters using request.URL.Query().Get("eventType"). Add conditional logic to branch between existing call status logging and new forward event logging based on eventType value. Ensure existing functionality remains unchanged when eventType is not provided.

## 2. Implement Forward Event Logging Logic [pending]
### Dependencies: 9.1
### Description: Create the forward-specific logging functionality that extracts webhook parameters (DialCallSid, DialCallStatus, ParentCallSid, CallStatus, From, To) and inserts records into the call_forward_events table using the existing repository layer.
### Details:
Extract forward-specific webhook parameters from the request body/form data. Map these parameters to CallForwardEvent struct fields and call the repository's Create or UpdateStatus method. Handle cases where ParentCallSid might be used to update existing records vs creating new ones. Include proper field validation for required parameters.

## 3. Add Error Handling and Response Management [pending]
### Dependencies: 9.2
### Description: Implement comprehensive error handling for forward event logging operations and ensure the endpoint returns proper HTTP 200 OK responses without TwiML content for both success and error scenarios.
### Details:
Add try-catch blocks around database operations with appropriate logging for forward event failures. Ensure database errors don't break existing call status logging functionality. Return HTTP 200 status with empty body for all forward event requests, logging errors internally without exposing them to Twilio webhook responses.

