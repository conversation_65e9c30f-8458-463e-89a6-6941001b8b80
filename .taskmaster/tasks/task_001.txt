# Task ID: 1
# Title: Database Migration - Column Rename and New Table
# Status: pending
# Dependencies: None
# Priority: high
# Description: Create database migration to rename primary_phone_number to forward_to_number and create call_forward_events table
# Details:
Create staged migration files:
1. Add forward_to_number column (nullable)
2. Backfill data from primary_phone_number
3. Create call_forward_events table with schema:
```sql
CREATE TABLE call_forward_events (
    id SERIAL PRIMARY KEY,
    call_sid TEXT NOT NULL,
    org_id INTEGER NOT NULL REFERENCES orgs(id),
    from_number TEXT NOT NULL,
    to_number TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'initiated',
    dial_call_status TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```
4. Add indexes on org_id and created_at for performance
5. Plan removal of primary_phone_number column for later phase

# Test Strategy:
Test migration rollback capability, verify data integrity after backfill, test index performance with sample data, validate foreign key constraints

# Subtasks:
## 1. Create Migration for forward_to_number Column Addition and Data Backfill [pending]
### Dependencies: None
### Description: Create the first migration file to add the forward_to_number column as nullable and backfill data from primary_phone_number column
### Details:
Create migration file with two operations: 1) ADD COLUMN forward_to_number TEXT NULL to the target table, 2) UPDATE statement to copy all data from primary_phone_number to forward_to_number WHERE primary_phone_number IS NOT NULL. Include proper transaction handling and add migration timestamp for ordering.

## 2. Create call_forward_events Table with Schema and Indexes [pending]
### Dependencies: None
### Description: Create the second migration file to establish the call_forward_events table with the specified schema and performance indexes
### Details:
Create migration file with the provided CREATE TABLE statement for call_forward_events including all columns (id, call_sid, org_id, from_number, to_number, status, dial_call_status, created_at, updated_at). Add CREATE INDEX statements for org_id and created_at columns. Include foreign key constraint validation for orgs(id) reference.

## 3. Plan and Document primary_phone_number Column Removal Strategy [pending]
### Dependencies: None
### Description: Document the strategy and create placeholder migration for future removal of primary_phone_number column after successful transition
### Details:
Create documentation outlining the removal strategy including: verification steps to ensure forward_to_number is fully adopted, application code updates required, and timeline for safe removal. Create a commented migration file template for the DROP COLUMN operation to be executed in a future phase. Include rollback considerations and data validation checkpoints.

