{"master": {"tasks": [{"id": 1, "title": "Database Migration - Column <PERSON>ame and New Table", "description": "Create database migration to rename primary_phone_number to forward_to_number and create call_forward_events table", "status": "done", "dependencies": [], "priority": "high", "details": "Create migration file with two operations:\n1. Rename primary_phone_number column to forward_to_number using ALTER TABLE RENAME COLUMN\n2. Create call_forward_events table with schema:\n```sql\nCREATE TABLE call_forward_events (\n    id SERIAL PRIMARY KEY,\n    call_sid TEXT NOT NULL,\n    org_id INTEGER NOT NULL REFERENCES orgs(id),\n    from_number TEXT NOT NULL,\n    to_number TEXT NOT NULL,\n    status TEXT NOT NULL DEFAULT 'initiated',\n    dial_call_status TEXT,\n    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()\n);\n```\n3. Add indexes on org_id and created_at for performance\n\nThis approach eliminates the need for data backfill since we're directly renaming the existing column.", "testStrategy": "Test migration rollback capability, verify column rename preserves all existing data, test index performance with sample data, validate foreign key constraints, ensure no data loss during column rename", "subtasks": [{"id": 1, "title": "Create Migration for Colum<PERSON> Rename from primary_phone_number to forward_to_number", "description": "Create migration file to rename the existing primary_phone_number column to forward_to_number using ALTER TABLE RENAME COLUMN", "status": "done", "dependencies": [], "details": "Create migration file with ALTER TABLE RENAME COLUMN statement to rename primary_phone_number to forward_to_number. This preserves all existing data without requiring backfill operations. Include proper transaction handling and add migration timestamp for ordering. Ensure the target table name is correctly identified.\n<info added on 2025-08-07T22:08:02.786Z>\nCOMPLETED: Successfully created migration file that adds forward_to_number column to orgs table (instead of renaming primary_phone_number) and includes comprehensive documentation explaining the schema context and purpose.\n</info added on 2025-08-07T22:08:02.786Z>", "testStrategy": "Test migration execution on development database, verify all existing data is preserved after rename, test rollback functionality, validate that all references to the old column name are updated, confirm no data loss during rename operation"}, {"id": 2, "title": "Create call_forward_events Table with Schema and Indexes", "description": "Create the migration operations to establish the call_forward_events table with the specified schema and performance indexes", "status": "done", "dependencies": [], "details": "Add to the same migration file the CREATE TABLE statement for call_forward_events including all columns (id, call_sid, org_id, from_number, to_number, status, dial_call_status, created_at, updated_at). Add CREATE INDEX statements for org_id and created_at columns. Include foreign key constraint validation for orgs(id) reference.\n<info added on 2025-08-07T22:08:21.497Z>\nCOMPLETED: Successfully created call_forward_events table with complete schema including all required columns, foreign key constraints to orgs(id), and performance indexes on org_id and created_at. Added comprehensive inline documentation for each column and index purpose.\n</info added on 2025-08-07T22:08:21.497Z>", "testStrategy": "Verify table creation with correct column types and constraints, test foreign key constraint with valid and invalid org_id values, validate index creation and query performance, test default values for status and timestamp columns"}, {"id": 3, "title": "Validate Migration Completeness and Rollback Strategy", "description": "Ensure the single migration file handles both column rename and table creation properly with appropriate rollback procedures", "status": "done", "dependencies": [], "details": "Review the combined migration file to ensure both operations (column rename and table creation) are properly sequenced and can be rolled back safely. Document any application code that may need updates to reference the new column name. Create validation queries to confirm successful migration execution.\n<info added on 2025-08-07T22:08:34.210Z>\nCOMPLETED: Successfully validated migration completeness with proper rollback functionality tested. Updated orgs service README documentation to include new forward_to_number field, clarified primary_phone_number purpose, and documented the complete call forwarding schema with all field descriptions. Migration includes comprehensive comments explaining the implementation approach.\n</info added on 2025-08-07T22:08:34.210Z>", "testStrategy": "Test complete migration execution and rollback on development environment, verify both operations succeed or fail together, validate that rollback properly restores original column name and removes new table, test with existing data scenarios"}]}, {"id": 2, "title": "Environment Configuration Setup", "description": "Add environment variable for call forwarding timeout configuration", "status": "done", "dependencies": [], "priority": "medium", "details": "Add the following environment variable:\n- CALL_FORWARD_TIMEOUT_SECONDS (default: 20)\n\nUpdate configuration loading in the communications service to read this value. Create validation for timeout values (must be positive integer between 5-60 seconds). Document configuration options in service README.\n\nNote: Call forwarding enablement is determined per-organization via the database field is_call_forwarding_enabled, fetched through the orgs repository (e.g., GetOrgForwardingSettings). The communications service must NOT read any global enablement environment variable.", "testStrategy": "Test default value loading for timeout, validate configuration parsing with various input types, test service startup with missing/invalid timeout config values, confirm no dependency on global enablement environment variable", "subtasks": [{"id": 1, "title": "Add CALL_FORWARD_TIMEOUT_SECONDS Environment Variable to Configuration", "description": "Add CALL_FORWARD_TIMEOUT_SECONDS environment variable to the communications service configuration system with default value of 20 seconds.", "status": "done", "dependencies": [], "details": "Add the CALL_FORWARD_TIMEOUT_SECONDS environment variable to the service's configuration struct or environment loading mechanism. Set default to 20 seconds. Ensure this value is accessible throughout the service for call forwarding functionality. Do NOT add any global enablement environment variable - enablement is determined per-organization via database.\n<info added on 2025-08-07T22:16:16.818Z>\nImplementation plan for subtask 2.1 (Add CALL_FORWARD_TIMEOUT_SECONDS env var to communications service):\n\nScope\n- Introduce a single env var: CALL_FORWARD_TIMEOUT_SECONDS (default 20)\n- Make it accessible inside the communications service for TwiML builder/usecase logic (used later by Task 4)\n- No global enablement env var; enablement remains per-org from DB via repo\n\nDesign\n- Create a small config module for forwarding-related configuration\n- Load once with sync.Once; parse and validate; clamp to [5,60]; default to 20 on invalid/missing\n- Provide a simple public getter used by handlers/builders: config.ForwardingTimeoutSeconds()\n\nFile changes\n1) services/communications/internal/config/forwarding.go\n   - Package: config\n   - Types: ForwardingConfig { ForwardTimeoutSeconds int }\n   - Functions:\n     - LoadForwardingConfig(): reads os.Getenv(\"CALL_FORWARD_TIMEOUT_SECONDS\"), parses int, validates 5..60, defaults to 20; caches result\n     - ForwardingTimeoutSeconds(): returns cached value; calls LoadForwardingConfig() via sync.Once\n   - Logging: use existing logging util (if available in comms service); otherwise basic log.Printf\n\n2) services/communications/cmd/server/main.go\n   - At startup, call config.LoadForwardingConfig() and log the effective timeout\n   - No behavior change beyond preloading and logging\n\n3) (Optional for this subtask) services/communications/internal/config/forwarding_test.go\n   - Table tests for: unset, non-integer, negative, 3, 61, valid 5, valid 60, typical 20\n   - Ensure clamp/default behavior works\n\nValidation rules\n- If unset → 20\n- If non-integer or out-of-range → log warning; use 20 (or clamp to nearest bound)\n- Enforce final value within [5,60]\n\nIntegration notes\n- Task 4 will use config.ForwardingTimeoutSeconds() when building TwiML\n- Task 7 will rely on repo to read is_call_forwarding_enabled and forward_to_number; unrelated to env\n\nRisks\n- None significant; ensure parsing errors are not fatal at startup\n\nRollout\n- Add variable to service docs in Task 2.3\n- No change to deployment manifests required unless we want to override default\n</info added on 2025-08-07T22:16:16.818Z>", "testStrategy": "Test that default value (20) is loaded correctly when environment variable is not set, and that custom values are properly read when environment variable is provided."}, {"id": 2, "title": "Implement Timeout Configuration Validation Logic", "description": "Create validation functions to ensure CALL_FORWARD_TIMEOUT_SECONDS is a positive integer between 5-60 seconds and handle configuration loading errors gracefully.", "status": "done", "dependencies": [1], "details": "Implement validation function that checks timeout value is an integer, positive, and within the 5-60 second range. Add error handling for invalid configuration values during service startup. Include logging for configuration validation failures and fallback to default values when appropriate. Only validate timeout - no global enablement flag validation needed.", "testStrategy": "Test validation with various input types (strings, negative numbers, out-of-range values), verify service startup behavior with invalid timeout configurations, and test fallback to default timeout value."}, {"id": 3, "title": "Update Service README Documentation", "description": "Document the call forwarding timeout configuration option in the communications service README with usage examples and validation rules.", "status": "done", "dependencies": [1, 2], "details": "Add a configuration section to the README explaining CALL_FORWARD_TIMEOUT_SECONDS variable. Include default value (20), valid range (5-60), data type (integer), and example usage. Document the validation rules and error handling behavior for invalid timeout configurations. Add a note that call forwarding enablement is per-organization via database field is_call_forwarding_enabled (checked via orgs repository function) and toggled via UI/API that writes to the database - no global environment variable controls enablement.", "testStrategy": "Review documentation for completeness and accuracy, verify timeout examples work as described, and ensure the timeout configuration option is properly documented with correct per-org enablement explanation."}]}, {"id": 3, "title": "Repository Layer for Call Forward Events", "description": "Implement minimal repository layer for call_forward_events table with basic CRUD operations", "status": "done", "dependencies": [1], "priority": "medium", "details": "Create repository interface and implementation in /services/communications/internal/cellularcall/data/:\n```go\ntype CallForwardEventRepository interface {\n    Create(ctx context.Context, event *CallForwardEvent) error\n    UpdateStatus(ctx context.Context, callSid string, status string, dialCallStatus string) error\n    GetByCallSid(ctx context.Context, callSid string) (*CallForwardEvent, error) // optional\n}\n```\nImplement with basic error handling, context cancellation, and simple structured logging. Focus on MVP functionality without advanced features like pagination, retry logic, or performance monitoring.", "testStrategy": "Unit tests for Create and UpdateStatus operations, basic integration tests with test database, test error handling for database connection issues, validate proper context cancellation", "subtasks": [{"id": 1, "title": "Create Repository Interface and Model Definitions", "description": "Define the minimal CallForwardEventRepository interface and CallForwardEvent model struct in the data package", "status": "done", "dependencies": [], "details": "Create repository.go file in /services/communications/internal/cellularcall/data/ with the CallForwardEventRepository interface containing Create, UpdateStatus, and optionally GetByCallSid methods. Define CallForwardEvent struct with fields matching call_forward_events table schema including ID, CallSid, OrgId, Status, DialCallStatus, CreatedAt, UpdatedAt. Include proper struct tags for database mapping.", "testStrategy": "Unit tests for struct validation and interface method signatures"}, {"id": 2, "title": "Implement Core Repository Operations", "description": "Implement the CallForwardEventRepository interface with Create and UpdateStatus methods using basic SQL queries", "status": "done", "dependencies": [1], "details": "Create repository_impl.go with struct implementing CallForwardEventRepository. Implement Create method with INSERT query and UpdateStatus with UPDATE query filtering by call_sid. Optionally implement GetByCallSid with SELECT query. Use database/sql package with prepared statements, basic context handling for cancellation, and simple error wrapping. Focus on straightforward implementation without connection pooling complexity or transaction handling.", "testStrategy": "Unit tests with mock database, integration tests with test database for Create and UpdateStatus operations, test context cancellation scenarios"}, {"id": 3, "title": "Add Basic Error Handling and Logging", "description": "Implement basic error handling and simple structured logging for repository operations", "status": "done", "dependencies": [2], "details": "Add simple structured logging using the project's logging framework to log database operations with call_sid and operation type. Implement basic error handling with standard error wrapping for database connection errors and not found scenarios. Keep logging minimal and focused on essential audit information without performance metrics or advanced retry logic.", "testStrategy": "Test basic error scenarios including database connection failures and context timeouts. Verify simple audit logs are generated for operations"}]}, {"id": 4, "title": "TwiML Builder Enhancement", "description": "Add ForwardDialResponse function to twiml/builder.go for generating call forwarding TwiML", "status": "done", "dependencies": [2], "priority": "high", "details": "Implement ForwardDialResponse function in /services/communications/internal/cellularcall/twiml/builder.go:\n```go\nfunc ForwardDialResponse(toNumber string, forwardedFromNumber string, forwardActionURL string) (string, error) {\n    dial := &twiml.VoiceDial{\n        Timeout:        20, // Read from env: CALL_FORWARD_TIMEOUT_SECONDS\n        Action:         forwardActionURL,\n        AnswerOnBridge: true,\n    }\n    \n    // Inline caller ID selection:\n    // 1. Use forwardedFromNumber if present\n    // 2. Otherwise omit CallerId for pass-through\n    // 3. Fallback to org TwilioNumber only if pass-through not permitted\n    if forwardedFromNumber != \"\" {\n        dial.CallerId = forwardedFromNumber\n    }\n    \n    numberElement := twiml.VoiceNumber{\n        PhoneNumber: toNumber,\n    }\n    dial.InnerElements = []twiml.Element{&numberElement}\n    return twiml.Voice([]twiml.Element{dial})\n}\n```\nInclude phone number validation using existing Hero Core utility utils.StandardizeUSPhoneNumber(). Implement inline caller ID selection logic without Twilio verification API.", "testStrategy": "Unit tests for TwiML generation with various input combinations, test phone number validation, verify generated XML structure matches Twilio requirements, test inline caller ID selection scenarios", "subtasks": [{"id": 1, "title": "Implement Phone Number Validation and Timeout Configuration", "description": "Add phone number validation using Hero Core utilities and environment variable configuration for call timeout. This establishes the foundation for secure and configurable call forwarding.", "status": "done", "dependencies": [], "details": "Import utils.StandardizeUSPhoneNumber() from Hero Core and add validation for toNumber parameter. Create environment variable CALL_FORWARD_TIMEOUT_SECONDS with default value 20. Add error handling for invalid phone numbers and return appropriate error messages.", "testStrategy": "Unit tests for phone number validation with valid/invalid formats, test environment variable reading with default fallback, verify error messages for malformed numbers"}, {"id": 2, "title": "Implement Inline Caller ID Selection Logic", "description": "<PERSON>reate simplified inline caller ID selection logic without Twilio verification API. Use forwarded_from_number if present, otherwise omit CallerId for pass-through behavior.", "status": "done", "dependencies": [1], "details": "Implement inline caller ID selection: use forwardedFromNumber parameter if present and valid; otherwise omit CallerId field to allow pass-through of original caller. Add fallback to organization TwilioNumber only if pass-through is not permitted. No external API calls required - all logic is inline.", "testStrategy": "Test caller ID selection with present/empty forwardedFromNumber, verify pass-through behavior when Caller<PERSON>d is omitted, test fallback scenarios"}, {"id": 3, "title": "Complete ForwardDialResponse Function Implementation", "description": "Finalize the ForwardDialResponse function by integrating validation logic and inline caller ID selection with TwiML generation. This creates the complete MVP call forwarding TwiML response.", "status": "done", "dependencies": [1, 2], "details": "Combine phone number validation, inline caller ID logic, and environment configuration into the ForwardDialResponse function. Ensure proper TwiML structure with VoiceDial and VoiceNumber elements. Add comprehensive error handling for validation failures and return properly formatted TwiML string or detailed error messages.", "testStrategy": "Integration tests with various input combinations, verify generated TwiML XML structure matches Twilio requirements, test error scenarios with invalid inputs, validate timeout configuration from environment"}]}, {"id": 6, "title": "Forward Action Handler Implementation", "description": "Implement HandleForwardActionRequest handler for processing Dial action callbacks", "details": "Create HandleForwardActionRequest in cellularcall_usecase.go:\n1. Parse DialCallStatus from Twilio webhook\n2. Log forward attempt result to call_forward_events\n3. On failure states (no-answer, busy, failed, canceled), trigger existing QueueCall flow\n4. On success (completed), return empty TwiML response\n5. Include infinite loop prevention with forwardAttempted flag\n6. Implement proper error handling and status logging\n\nWebhook parameters to handle: DialCallStatus, CallSid, From, To, forwardAttempted", "testStrategy": "Unit tests for all DialCallStatus scenarios, test fallback to queue logic, verify infinite loop prevention, test webhook parameter parsing, integration tests with mock Twilio webhooks", "priority": "high", "dependencies": [3, 4], "status": "done", "subtasks": [{"id": 1, "title": "Webhook Parameter Parsing and Validation", "description": "Implement request parsing logic to extract and validate Twilio webhook parameters (DialCallStatus, CallSid, From, To, forwardAttempted) in HandleForwardActionRequest function.", "dependencies": [], "details": "Create parameter extraction logic in HandleForwardActionRequest function. Parse form values from HTTP request, validate required parameters (DialCallStatus, CallSid), and handle optional forwardAttempted flag. Implement proper error responses for missing or invalid parameters following existing Twilio webhook patterns in the codebase.", "status": "done", "testStrategy": "Unit tests for parameter parsing with valid/invalid inputs, test missing required parameters, verify proper error response format"}, {"id": 2, "title": "Call Forward Event Logging Implementation", "description": "Implement logging of forward attempt results to call_forward_events table using the repository layer, capturing dial status and call metadata.", "dependencies": ["6.1"], "details": "Use CallForwardEventRepository to log forward attempt results. Create or update call forward event records with DialCallStatus, timestamp, and call metadata. Handle database errors gracefully and ensure logging doesn't block the webhook response. Include proper context handling for database operations.", "status": "done", "testStrategy": "Unit tests for event logging with different dial statuses, test database error handling, verify proper context usage"}, {"id": 3, "title": "Status-Based Flow Control and Response Generation", "description": "Implement conditional logic to handle different DialCallStatus values - trigger QueueCall flow for failure states and return empty TwiML for success, with infinite loop prevention.", "dependencies": ["6.1", "6.2"], "details": "Implement switch/case logic for DialCallStatus handling. For failure states (no-answer, busy, failed, canceled), check forwardAttempted flag to prevent loops, then trigger existing QueueCall flow. For success state (completed), return empty TwiML response. Include proper error handling and ensure TwiML responses follow Twilio specifications.", "status": "done", "testStrategy": "Unit tests for all DialCallStatus scenarios, test infinite loop prevention with forwardAttempted flag, verify correct TwiML response format"}]}, {"id": 7, "title": "Enhanced HandleVoiceRequest Logic", "description": "Modify existing HandleVoiceRequest to check forwarding flag and return forward TwiML before queue logic", "details": "Enhance HandleVoiceRequest in cellularcall_usecase.go:\n1. Check is_call_forwarding_enabled flag for organization\n2. If enabled and forward_to_number exists, return ForwardDialResponse TwiML\n3. Include forwardAttempted=true in action URL to prevent loops\n4. Log forward initiation to call_forward_events table\n5. Maintain existing queue logic as fallback\n6. Add proper error handling for missing/invalid forward numbers\n\nEnsure backward compatibility with existing call flow when forwarding is disabled.", "testStrategy": "Test forwarding enabled/disabled scenarios, verify existing queue logic remains intact, test with missing forward_to_number, validate TwiML response format, integration tests with full call flow", "priority": "high", "dependencies": [6], "status": "done", "subtasks": [{"id": 1, "title": "Add Organization Forwarding Configuration Check", "description": "Modify HandleVoiceRequest to check organization's call forwarding settings before processing queue logic", "dependencies": [], "details": "Add logic at the beginning of HandleVoiceRequest to query organization settings for is_call_forwarding_enabled flag and forward_to_number. Implement early return pattern if forwarding is enabled and valid forward number exists. Add validation for forward_to_number format and handle missing/empty values gracefully.", "status": "done", "testStrategy": "Unit tests for organization lookup, test with enabled/disabled forwarding flags, validate forward number format checking, test error handling for database connection issues"}, {"id": 2, "title": "Implement Forward TwiML Response Generation", "description": "Generate and return ForwardDialResponse TwiML when call forwarding conditions are met", "dependencies": ["7.1"], "details": "Call ForwardDialResponse function from twiml builder with organization's forward_to_number, caller ID, and action URL containing forwardAttempted=true parameter. Ensure proper error handling if TwiML generation fails and fallback to existing queue logic. Set appropriate HTTP headers and response format for Twilio webhook consumption.", "status": "done", "testStrategy": "Test TwiML generation with valid forward numbers, verify forwardAttempted parameter inclusion, test fallback to queue logic on TwiML errors, validate HTTP response format"}, {"id": 3, "title": "Add Forward Event Logging and Loop Prevention", "description": "Log call forwarding initiation to call_forward_events table and implement loop prevention mechanism", "dependencies": ["7.2"], "details": "Create call_forward_events record when forwarding is initiated, capturing original call details and forward attempt. Check for forwardAttempted parameter in incoming requests to prevent infinite forwarding loops. Maintain existing queue logic as fallback when forwarding fails or is already attempted. Add comprehensive error logging for debugging.", "status": "done", "testStrategy": "Test event logging with various call parameters, verify loop prevention with forwardAttempted parameter, test fallback to queue logic, validate database insertion success and error handling"}]}, {"id": 8, "title": "Route Registration for Forward Action Endpoint", "description": "Register new /twiml/forward-action endpoint in main.go server setup", "status": "done", "dependencies": [6], "priority": "medium", "details": "Add route registration in /services/communications/cmd/server/main.go:\n1. Register POST /hero.communications.v1.TwilioWebhookService/twiml/forward-action endpoint pointing to HandleForwardActionRequest\n\nFollow existing patterns for Twilio webhook endpoints in the service. Middleware, timeouts, and security configurations are handled separately in the Security task.", "testStrategy": "Test route registration and accessibility, verify endpoint responds to POST requests", "subtasks": [{"id": 1, "title": "Add Route Registration in main.go", "description": "Register the POST /hero.communications.v1.TwilioWebhookService/twiml/forward-action endpoint in the main.go server setup, pointing to the HandleForwardActionRequest handler function", "status": "done", "dependencies": [], "details": "In /services/communications/cmd/server/main.go, add route registration following existing Twilio webhook patterns. Register POST route '/hero.communications.v1.TwilioWebhookService/twiml/forward-action' that calls the HandleForwardActionRequest handler from the cellular call use case. Follow the same pattern as other Twilio webhook endpoints in the service.", "testStrategy": "Test route registration and verify endpoint accessibility with basic HTTP POST requests"}]}, {"id": 9, "title": "Enhanced Call Status Logging", "description": "Enhance existing /callstatus endpoint to handle forward event logging with eventType parameter", "details": "Modify existing /callstatus handler to:\n1. Accept eventType=forward query parameter\n2. Log forward-specific events to call_forward_events table\n3. Handle webhook parameters: DialCallSid, DialCallStatus, ParentCallSid, CallStatus, From, To\n4. Maintain existing call status logging functionality\n5. Add proper error handling for forward event logging\n6. Ensure no TwiML response (200 OK only)\n\nReuse existing infrastructure while adding forward-specific logging capability.", "testStrategy": "Test eventType parameter handling, verify forward event logging, ensure existing call status logging unaffected, test webhook parameter parsing, validate database insertions", "priority": "medium", "dependencies": [3, 8], "status": "done", "subtasks": [{"id": 1, "title": "Add eventType Parameter Handling to CallStatus Endpoint", "description": "Modify the existing /callstatus endpoint handler to accept and parse the eventType query parameter, specifically handling eventType=forward while maintaining backward compatibility for existing calls without this parameter.", "dependencies": [], "details": "Update the callstatus handler function to extract eventType from query parameters using request.URL.Query().Get(\"eventType\"). Add conditional logic to branch between existing call status logging and new forward event logging based on eventType value. Ensure existing functionality remains unchanged when eventType is not provided.", "status": "done", "testStrategy": "Test endpoint with and without eventType parameter, verify existing call status logging unaffected, test invalid eventType values"}, {"id": 2, "title": "Implement Forward Event Logging Logic", "description": "Create the forward-specific logging functionality that extracts webhook parameters (DialCallSid, DialCallStatus, ParentCallSid, CallStatus, From, To) and inserts records into the call_forward_events table using the existing repository layer.", "dependencies": ["9.1"], "details": "Extract forward-specific webhook parameters from the request body/form data. Map these parameters to CallForwardEvent struct fields and call the repository's Create or UpdateStatus method. Handle cases where ParentCallSid might be used to update existing records vs creating new ones. Include proper field validation for required parameters.", "status": "done", "testStrategy": "Test webhook parameter extraction, verify database insertions with various parameter combinations, test missing required parameters handling"}, {"id": 3, "title": "Add Error Handling and Response Management", "description": "Implement comprehensive error handling for forward event logging operations and ensure the endpoint returns proper HTTP 200 OK responses without TwiML content for both success and error scenarios.", "dependencies": ["9.2"], "details": "Add try-catch blocks around database operations with appropriate logging for forward event failures. Ensure database errors don't break existing call status logging functionality. Return HTTP 200 status with empty body for all forward event requests, logging errors internally without exposing them to Twilio webhook responses.", "status": "done", "testStrategy": "Test database connection failures, verify 200 OK responses in all scenarios, test concurrent requests, validate error logging without TwiML interference"}]}, {"id": 10, "title": "Orgs Service Integration", "description": "Introduce dedicated UpdateForwardingConfig RPC in the orgs service without changing existing PSTN/Twilio-number-claim behavior or Create/UpdateOrg operations", "status": "pending", "dependencies": [1], "priority": "medium", "details": "Update services/orgs to add dedicated UpdateForwardingConfig RPC for managing call forwarding configuration while keeping existing functionality intact:\n1. Add UpdateForwardingConfig RPC to proto/API with request fields: org_id (required), is_call_forwarding_enabled (google.protobuf.BoolValue, optional), forward_to_number (google.protobuf.StringValue, optional) - COMPLETED\n2. Update data layer with focused repository method for updating only forwarding fields\n3. Implement UpdateForwardingConfig usecase handler with validation\n4. Update bootstrap recipes to optionally set forward_to_number\n5. Keep Create/UpdateOrg operations and all PSTN/Twilio claim logic unchanged\n\nThis introduces a dedicated RPC for forwarding config management - no changes to existing Create/UpdateOrg behavior.", "testStrategy": "Test UpdateForwardingConfig RPC with various field combinations, verify repository updates only forwarding fields, validate phone number format validation, ensure existing Create/UpdateOrg and PSTN/Twilio functionality remains unaffected", "subtasks": [{"id": 1, "title": "Proto/API Updates for UpdateForwardingConfig RPC", "description": "Add UpdateForwardingConfig RPC to orgs proto with dedicated request/response messages and regenerate stubs", "status": "done", "dependencies": [], "details": "Add UpdateForwardingConfig RPC to orgs service proto with UpdateForwardingConfigRequest containing: org_id (required), is_call_forwarding_enabled (google.protobuf.BoolValue, optional), forward_to_number (google.protobuf.StringValue, optional). Response returns updated Org message. Keep existing Create/UpdateOrg RPCs unchanged. Regenerate all language stubs (Go, TypeScript, connect) using make proto.", "testStrategy": "Verify proto compilation succeeds, test stub generation for all target languages, validate new RPC definition follows protobuf best practices, ensure no breaking changes to existing RPCs"}, {"id": 2, "title": "Data Layer Method for Forwarding Config Updates", "description": "Add UpdateForwardingConfig method to postgres_orgs_repo.go that updates only is_call_forwarding_enabled and forward_to_number fields", "status": "done", "dependencies": [1], "details": "Add UpdateForwardingConfig(ctx, tx, orgID, enabledOpt, forwardToOpt) method to postgres_orgs_repo.go that updates only is_call_forwarding_enabled and forward_to_number fields for a given org_id. Use restricted SET clause to ensure no other fields are modified. Handle NULL values properly for optional wrapper types. Method signature should accept optional google.protobuf.BoolValue and StringValue parameters.", "testStrategy": "Test repository method updates only forwarding fields, verify other org fields remain unchanged, validate proper NULL handling for optional wrapper fields, test with various field combinations including partial updates"}, {"id": 3, "title": "UpdateForwardingConfig Usecase Handler Implementation", "description": "Implement UpdateForwardingConfig usecase handler with validation and field-specific updates", "status": "done", "dependencies": [2], "details": "Implement UpdateForwardingConfig handler in orgs usecase: validate and standardize forward_to_number if provided, apply optional fields via repository UpdateForwardingConfig method, persist changes, return updated Org. Include proper error handling for invalid org_id and phone number format validation. No changes to PSTN/Twilio claim logic.", "testStrategy": "Test handler with various field combinations, verify phone number format validation and standardization, test error handling for invalid org_id, ensure only specified fields are updated, validate response contains updated values"}, {"id": 4, "title": "Bootstrap Recipe Updates for forward_to_number", "description": "Update bootstrap recipes to optionally set forward_to_number during organization creation", "status": "in-progress", "dependencies": [3], "details": "Modify bootstrap recipes and configuration files to support optional forward_to_number field when creating organizations through existing Create/UpdateOrg operations. Update configuration templates to handle the new field. Ensure bootstrap process works with both existing and new field configurations. This uses existing RPCs, not the new UpdateForwardingConfig RPC.", "testStrategy": "Test bootstrap recipe execution with forward_to_number field, verify configuration file updates work correctly, validate deployment process handles new field properly, test both with and without forward_to_number specified"}, {"id": 5, "title": "Documentation and Testing for UpdateForwardingConfig RPC", "description": "Add brief README documentation for UpdateForwardingConfig RPC and create smoke tests", "status": "pending", "dependencies": [4], "details": "Update orgs service README with brief note documenting the new UpdateForwardingConfig RPC, its purpose, and basic usage. Create smoke tests covering: update forwarding config with various field combinations, verify field-specific updates work correctly, and basic integration validation. Keep documentation concise and focused on the new RPC functionality.", "testStrategy": "Verify documentation accuracy, test example usage works correctly, validate smoke tests cover key scenarios including partial field updates, ensure integration with existing orgs service functionality"}]}, {"id": 11, "title": "Security and Validation Implementation", "description": "Implement Twilio signature validation for call forwarding webhook endpoints", "status": "pending", "dependencies": [8, 9], "priority": "high", "details": "Implement security measures:\n1. <PERSON>ida<PERSON> <PERSON>wi<PERSON> signatures on /twiml/forward-action and /callstatus endpoints\n\nEnsure webhook requests are authenticated and prevent unauthorized access to call forwarding endpoints.", "testStrategy": "Test Twilio signature validation with valid/invalid signatures, verify proper error responses for unauthorized requests", "subtasks": [{"id": 1, "title": "Implement Twilio Webhook Signature Validation Middleware", "description": "Create middleware function to validate Twilio signatures using the Twilio SDK for authenticating webhook requests.", "status": "pending", "dependencies": [], "details": "Create middleware function to validate Twilio signatures using the Twilio SDK. Extract request URL, body, and X-Twilio-Signature header. Use Twilio's RequestValidator with auth token from environment variables. Return 403 Forbidden for invalid signatures with appropriate logging.", "testStrategy": "Test with valid Twilio signatures, invalid signatures, missing signatures, and malformed headers. Verify proper error responses and logging."}, {"id": 2, "title": "Apply Signature Validation to Webhook Endpoints", "description": "Apply the signature validation middleware to both /twiml/forward-action and /callstatus endpoints to secure webhook handling.", "status": "pending", "dependencies": [1], "details": "Apply the signature validation middleware to both /twiml/forward-action and /callstatus webhook endpoints. Ensure middleware is properly integrated into the request handling pipeline and that invalid signatures are rejected before processing webhook data.", "testStrategy": "Test both endpoints with valid and invalid signatures, verify middleware integration, confirm proper request rejection for unauthorized access attempts."}]}, {"id": 12, "title": "Comprehensive Testing and Documentation", "description": "Implement MVP-focused test suite and documentation for call forwarding feature", "status": "pending", "dependencies": [7, 9, 10, 11], "priority": "medium", "details": "Create focused testing and documentation for <PERSON>:\n1. Unit tests for new builder/handlers/repository functions\n2. Basic integration tests with mock Twilio webhooks\n3. Brief README updates with forwarding feature details\n4. Short troubleshooting list for common issues\n\nTarget reasonable code coverage for all new functionality without extensive performance testing or comprehensive API documentation.", "testStrategy": "Automated test execution in CI/CD pipeline, basic code coverage reporting, manual testing with real Twilio webhooks for validation", "subtasks": [{"id": 1, "title": "Unit and Basic Integration Test Suite Implementation", "description": "Create unit tests for new builder/handlers/repository functions and basic integration tests with mock Twilio webhooks", "status": "pending", "dependencies": [], "details": "Implement unit tests for HandleForwardActionRequest, caller ID selection logic, TwiML builder functions, and database repository operations. Create basic integration tests using mock Twilio webhooks to test core forwarding flow. Set up test fixtures for key DialCallStatus scenarios and webhook parameter combinations. Use testify framework for assertions and focus on critical path coverage.", "testStrategy": "Achieve reasonable code coverage for new functionality, automated test execution in CI/CD pipeline"}, {"id": 2, "title": "README Updates and Basic Documentation", "description": "Update service README with call forwarding feature overview and basic configuration details", "status": "pending", "dependencies": [1], "details": "Add brief section to service README covering call forwarding feature overview, environment variable configuration (CALL_FORWARD_TIMEOUT_SECONDS, CALL_FORWARD_ENABLED), and webhook endpoint details. Include basic setup instructions and configuration examples. Keep documentation concise and focused on essential information for MVP deployment.", "testStrategy": "Documentation review and validation against actual implementation"}, {"id": 3, "title": "Short Troubleshooting Guide", "description": "Create concise troubleshooting list for common call forwarding issues", "status": "pending", "dependencies": [1, 2], "details": "Create short troubleshooting guide covering most common issues: caller ID validation failures, forwarding timeout problems, webhook signature validation errors, and basic debugging steps. Focus on actionable solutions and common configuration mistakes. Keep guide brief and practical for MVP support needs.", "testStrategy": "Manual testing with real Twilio webhooks to verify troubleshooting accuracy and completeness"}]}], "metadata": {"created": "2025-08-07T20:42:34.939Z", "updated": "2025-08-11T22:15:45.686Z", "description": "Tasks for master context"}}}