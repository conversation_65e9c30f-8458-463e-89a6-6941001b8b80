# Task ID: 10
# Title: Orgs Service Integration
# Status: pending
# Dependencies: 1
# Priority: medium
# Description: Update orgs service to handle column rename from primary_phone_number to forward_to_number
# Details:
Update services/orgs to:
1. Switch reads/writes from primary_phone_number to forward_to_number
2. Update database models and perform backfill migration
3. Adjust existing API handlers to use forward_to_number
4. Update bootstrap recipes and configurations
5. Maintain backward compatibility during transition

Focus on seamless column rename without adding new public endpoints or feature flags.

# Test Strategy:
Test existing API endpoints with new field names, verify backward compatibility, validate bootstrap recipe updates, test database migration and backfill process

# Subtasks:
## 1. Database Schema Migration and Model Updates with Backfill [pending]
### Dependencies: None
### Description: Update the orgs service database models and schema to support the forward_to_number field, perform data backfill, and maintain backward compatibility
### Details:
Add forward_to_number column to orgs table with proper indexing. Update Go struct models to include forward_to_number field. Create database migration scripts that copy existing primary_phone_number values to forward_to_number (backfill). Ensure proper NULL handling and default values. Maintain primary_phone_number column for backward compatibility during transition period.

## 2. Adjust Existing API Handlers to Use forward_to_number [pending]
### Dependencies: 10.1
### Description: Modify existing orgs service API handlers to read from and write to forward_to_number instead of primary_phone_number
### Details:
Update existing REST API handlers to use forward_to_number in internal logic while maintaining same request/response structure. Modify service layer methods to read from forward_to_number first, falling back to primary_phone_number for backward compatibility. Update all write operations to use forward_to_number. No new public endpoints or validation changes required.

## 3. Update Bootstrap Recipes and Configuration [pending]
### Dependencies: 10.1
### Description: Update bootstrap recipes and configuration files to use forward_to_number field instead of primary_phone_number
### Details:
Modify bootstrap recipes and configuration files to reference forward_to_number field. Update any hardcoded references to primary_phone_number in configuration templates. Ensure bootstrap process works seamlessly with new column name. Update deployment scripts and documentation as needed.

