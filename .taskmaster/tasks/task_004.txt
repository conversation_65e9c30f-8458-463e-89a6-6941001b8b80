# Task ID: 4
# Title: TwiML Builder Enhancement
# Status: pending
# Dependencies: 2
# Priority: high
# Description: Add ForwardDialResponse function to twiml/builder.go for generating call forwarding TwiML
# Details:
Implement ForwardDialResponse function in /services/communications/internal/cellularcall/twiml/builder.go:
```go
func ForwardDialResponse(toNumber string, forwardedFromNumber string, forwardActionURL string) (string, error) {
    dial := &twiml.VoiceDial{
        Timeout:        20, // Read from env: CALL_FORWARD_TIMEOUT_SECONDS
        Action:         forwardActionURL,
        AnswerOnBridge: true,
    }
    
    // Inline caller ID selection:
    // 1. Use forwardedFromNumber if present
    // 2. Otherwise omit CallerId for pass-through
    // 3. Fallback to org TwilioNumber only if pass-through not permitted
    if forwardedFromNumber != "" {
        dial.CallerId = forwardedFromNumber
    }
    
    numberElement := twiml.VoiceNumber{
        PhoneNumber: toNumber,
    }
    dial.InnerElements = []twiml.Element{&numberElement}
    return twiml.Voice([]twiml.Element{dial})
}
```
Include phone number validation using existing Hero Core utility utils.StandardizeUSPhoneNumber(). Implement inline caller ID selection logic without Twilio verification API.

# Test Strategy:
Unit tests for TwiML generation with various input combinations, test phone number validation, verify generated XML structure matches Twilio requirements, test inline caller ID selection scenarios

# Subtasks:
## 1. Implement Phone Number Validation and Timeout Configuration [pending]
### Dependencies: None
### Description: Add phone number validation using Hero Core utilities and environment variable configuration for call timeout. This establishes the foundation for secure and configurable call forwarding.
### Details:
Import utils.StandardizeUSPhoneNumber() from Hero Core and add validation for toNumber parameter. Create environment variable CALL_FORWARD_TIMEOUT_SECONDS with default value 20. Add error handling for invalid phone numbers and return appropriate error messages.

## 2. Implement Inline Caller ID Selection Logic [pending]
### Dependencies: 4.1
### Description: Create simplified inline caller ID selection logic without Twilio verification API. Use forwarded_from_number if present, otherwise omit CallerId for pass-through behavior.
### Details:
Implement inline caller ID selection: use forwardedFromNumber parameter if present and valid; otherwise omit CallerId field to allow pass-through of original caller. Add fallback to organization TwilioNumber only if pass-through is not permitted. No external API calls required - all logic is inline.

## 3. Complete ForwardDialResponse Function Implementation [pending]
### Dependencies: 4.1, 4.2
### Description: Finalize the ForwardDialResponse function by integrating validation logic and inline caller ID selection with TwiML generation. This creates the complete MVP call forwarding TwiML response.
### Details:
Combine phone number validation, inline caller ID logic, and environment configuration into the ForwardDialResponse function. Ensure proper TwiML structure with VoiceDial and VoiceNumber elements. Add comprehensive error handling for validation failures and return properly formatted TwiML string or detailed error messages.

