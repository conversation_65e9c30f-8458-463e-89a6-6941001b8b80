# Task ID: 7
# Title: Enhanced HandleVoiceRequest Logic
# Status: pending
# Dependencies: 6
# Priority: high
# Description: Modify existing HandleVoiceRequest to check forwarding flag and return forward TwiML before queue logic
# Details:
Enhance HandleVoiceRequest in cellularcall_usecase.go:
1. Check is_call_forwarding_enabled flag for organization
2. If enabled and forward_to_number exists, return ForwardDialResponse TwiML
3. Include forwardAttempted=true in action URL to prevent loops
4. Log forward initiation to call_forward_events table
5. Maintain existing queue logic as fallback
6. Add proper error handling for missing/invalid forward numbers

Ensure backward compatibility with existing call flow when forwarding is disabled.

# Test Strategy:
Test forwarding enabled/disabled scenarios, verify existing queue logic remains intact, test with missing forward_to_number, validate TwiML response format, integration tests with full call flow

# Subtasks:
## 1. Add Organization Forwarding Configuration Check [pending]
### Dependencies: None
### Description: Modify HandleVoiceRequest to check organization's call forwarding settings before processing queue logic
### Details:
Add logic at the beginning of HandleVoiceRequest to query organization settings for is_call_forwarding_enabled flag and forward_to_number. Implement early return pattern if forwarding is enabled and valid forward number exists. Add validation for forward_to_number format and handle missing/empty values gracefully.

## 2. Implement Forward TwiML Response Generation [pending]
### Dependencies: 7.1
### Description: Generate and return ForwardDialResponse TwiML when call forwarding conditions are met
### Details:
Call ForwardDialResponse function from twiml builder with organization's forward_to_number, caller ID, and action URL containing forwardAttempted=true parameter. Ensure proper error handling if TwiML generation fails and fallback to existing queue logic. Set appropriate HTTP headers and response format for Twilio webhook consumption.

## 3. Add Forward Event Logging and Loop Prevention [pending]
### Dependencies: 7.2
### Description: Log call forwarding initiation to call_forward_events table and implement loop prevention mechanism
### Details:
Create call_forward_events record when forwarding is initiated, capturing original call details and forward attempt. Check for forwardAttempted parameter in incoming requests to prevent infinite forwarding loops. Maintain existing queue logic as fallback when forwarding fails or is already attempted. Add comprehensive error logging for debugging.

