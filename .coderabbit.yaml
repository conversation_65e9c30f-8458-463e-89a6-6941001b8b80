reviews:
  profile: chill
  poem: true
  sequence_diagrams: true
  collapse_walkthrough: true
  changed_files_summary: true
  estimate_code_review_effort: false
  assess_linked_issues: true
  related_issues: true
  related_prs: true
  suggested_labels: true
  suggested_reviewers: true
  auto_review:
    enabled: true
    auto_incremental_review: false # Disables per-commit reviews to avoid comment spam
    drafts: false # Skips drafts to focus on ready PRs
  tools:
    # Disable noisy linters/tools that generate minor nits; keep essential ones for high-signal issues
    markdownlint:
      enabled: false # Often noisy for non-code
    eslint:
      enabled: true # Keep for JS/TS, but assume repo config filters nits
    semgrep:
      enabled: true # Good for security
    gitleaks:
      enabled: true # Security focus
    checkov:
      enabled: true # Infra security
    # Disable others if they add too much noise, e.g.:
    ruff:
      enabled: false
    flake8:
      enabled: false
    pylint:
      enabled: false
  path_instructions:
    - path: "**/*" # Global instructions for all files
      instructions: |
        Analyze for critical flaws only: potential crashes, edge cases, inefficient algorithms, or security risks. Suggest improvements in 3-5 sentences max per comment, grouping related issues to stay under 5-7 comments per PR unless changes are extensive. Comment only on specific lines, avoiding large code chunks unless critical. Include brief code examples for clarity if needed. Learn from past interactions to refine focus on team priorities.
tone_instructions: |
  Focus on high-impact issues: bugs, security, performance, logic errors. Group suggestions, max 5-7 comments per PR. Comment on specific lines only. Direct tone, no pleasantries. Ignore minor style issues unless they conflict with team conventions.
chat:
  auto_reply: true # Keeps interactive learning for better adaptation
knowledge_base:
  web_search:
    enabled: true # Helps with contextual, high-signal advice
  code_guidelines:
    enabled: true
