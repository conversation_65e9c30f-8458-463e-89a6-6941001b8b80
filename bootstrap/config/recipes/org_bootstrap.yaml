# Recipe for bootstrapping a new org with an admin role and asset
# This must be ran using the credentials of the meta org admin user (<EMAIL>)

# before running, set the params in <>
steps:
  - name: create_org
    description: Create the initial organization
    rpc: hero.orgs.v1.OrgsService/CreateOrg
    payload:
      org:
        id: "1"
        name: "Hero Demo 1"
        service_type: SERVICE_TYPE_DEMO
        domains: ["demo-1.gethero.com"]
        primary_phone_number: "+14157012311"
        template_id: "no-ptt"

  - name: create_admin_cognito_user
    description: Create the cognito user
    rpc: hero.orgs.v1.OrgsService/CreateCognitoUser
    payload:
      org_id: "${create_org.org.id}"
      username: "<EMAIL>"
      email: "<EMAIL>"
      password: "Password123!"

  - name: create_admin_role
    description: Create the admin role
    rpc: hero.permissions.v1.PermissionService/CreateRole
    payload:
      role:
        name: "Admin"
        org_id: "${create_org.org.id}"
        categories:
          - name: "Permission"
            can_do_all: true
          - name: "Orgs"
            can_do_all: true
          - name: "AssetRegistry"
            can_do_all: true
          - name: "Situation"
            can_do_all: true
          - name: "Report"
            can_do_all: true
          - name: "CameraFeed"
            can_do_all: true
          - name: "Order"
            can_do_all: true
          - name: "Entity"
            can_do_all: true
          - name: "VideoCall"
            can_do_all: true
          - name: "Chat"
            can_do_all: true
          - name: "CellularCall"
            can_do_all: true
          - name: "PTT"
            can_do_all: true
          - name: "Case"
            can_do_all: true
          - name: "FileRepository"
            can_do_all: true
          - name: "Property"
            can_do_all: true
          - name: "ETL"
            can_do_all: true
          - name: "FeatureFlags"
            can_do_all: true
    depends_on:
      - create_org

  - name: create_responder_role
    description: Create the responder role
    rpc: hero.permissions.v1.PermissionService/CreateRole
    payload:
      role:
        name: "Responder"
        org_id: "${create_org.org.id}"
        categories:
          - name: "Permission"
            can_do_all: true
          - name: "Orgs"
            can_do_all: true
          - name: "AssetRegistry"
            can_do_all: true
          - name: "Situation"
            can_do_all: true
          - name: "Report"
            can_do_all: true
          - name: "Camera"
            can_do_all: true
          - name: "CameraOrchestration"
            can_do_all: true
          - name: "Order"
            can_do_all: true
          - name: "Entity"
            can_do_all: true
          - name: "VideoCall"
            can_do_all: true
          - name: "Chat"
            can_do_all: true
          - name: "CellularCall"
            can_do_all: true
          - name: "PTT"
            can_do_all: true
          - name: "Case"
            can_do_all: true
          - name: "FileRepository"
            can_do_all: true
          - name: "Property"
            can_do_all: true
          - name: "ETL"
            can_do_all: true
          - name: "FeatureFlags"
            can_do_all: true
    depends_on:
      - create_org

  - name: assign_admin_role
    description: Assign the admin role to the default user
    rpc: hero.permissions.v1.PermissionService/AddCognitoUserToRole
    payload:
      role_id: "${create_admin_role.role.id}"
      cognito_sub_id: "${create_admin_cognito_user.cognitoSubId}"
      org_id_override: "${create_org.org.id}"
    depends_on:
      - create_admin_cognito_user
      - create_admin_role
  
  # for twilio webhook
  - name: create_webhook_role
    description: Create the webhook role
    rpc: hero.permissions.v1.PermissionService/CreateRole
    payload:
      role:
        name: "webhook"
        org_id: "${create_org.org.id}"
        categories:
          - name: "AssetRegistry"
            can_do_all: true
          - name: "Situation"
            can_do_all: true
          - name: "CellularCall"
            can_do_all: true
          - name: "TwilioWebhook"
            can_do_all: true
          - name: "Orgs"
            actions:
              - name: "GetOrgAPIUserPrivateById"
                can_do_action: true
              - name: "ValidateOrgCreds"
                can_do_action: true

  # for twilio webhook
  - name: create_webhook_role_assignment
    description: Create the webhook role assignment
    rpc: hero.permissions.v1.PermissionService/AddUserToRole
    payload:
      role_id: "${create_webhook_role.role.id}"
      user_id: "${create_org.twilioApiUserId}"
      org_id_override: "${create_org.org.id}"
    depends_on:
      - create_webhook_role