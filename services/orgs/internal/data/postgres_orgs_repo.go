package repository

import (
	"context"
	"database/sql"
	"fmt"
	"strconv"
	"time"

	"github.com/google/uuid"
	"github.com/lib/pq"

	assets "proto/hero/assets/v2"
	pb "proto/hero/orgs/v1"

	"google.golang.org/protobuf/types/known/timestamppb"

	database "common/database"
	"common/herosentry"
	"common/utils"
)

// postgresOrgRepository implements OrgRepository using PostgreSQL
type postgresOrgRepository struct {
	db *sql.DB
}

// Helper function to convert a *timestamppb.Timestamp to time.Time.
func TimestamppbToTime(ts *timestamppb.Timestamp) time.Time {
	if ts == nil {
		return time.Time{}
	}
	return ts.AsTime()
}

func (r *postgresOrgRepository) CreateOrg(ctx context.Context, transaction *sql.Tx, org *pb.Org) (*pb.Org, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "postgresOrgRepository.CreateOrg")
	defer finish()
	span.SetTag("org.name", org.Name)
	span.SetTag("org.id", fmt.Sprintf("%d", org.Id))

	return database.WithSession(r.db, spanContext, transaction, func(tx *sql.Tx) (*pb.Org, error) {
		// Standardize phone numbers if present
		if org.PrimaryPhoneNumber != "" {
			standardizedNumber, err := utils.StandardizeUSPhoneNumber(org.PrimaryPhoneNumber)
			if err != nil {
				return nil, fmt.Errorf("invalid primary_phone_number format: %v", err)
			}
			org.PrimaryPhoneNumber = standardizedNumber
		}

		var query string
		var row *sql.Row
		// Allow client to specify org ID, but if not, the DB will auto-assign the next available ID
		if org.Id == 0 { // org ID is not specified
			query = `
				INSERT INTO orgs (name, domains, twiml_app_sid, twilio_number, twilio_number_sid, service_type, template_id, 
					is_call_forwarding_enabled, call_forwarding_type, primary_phone_number, sip_uri, created_at, updated_at)
				VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
				RETURNING id, name, domains, twiml_app_sid, twilio_number, twilio_number_sid, service_type, template_id,
					is_call_forwarding_enabled, call_forwarding_type, primary_phone_number, sip_uri, created_at, updated_at
			`
			row = tx.QueryRowContext(ctx, query,
				org.Name,
				pq.Array(org.Domains),
				org.TwimlAppSid,
				org.TwilioNumber,
				org.TwilioNumberSid,
				org.ServiceType,
				org.TemplateId,
				org.IsCallForwardingEnabled,
				org.CallForwardingType,
				org.PrimaryPhoneNumber,
				org.SipUri,
				TimestamppbToTime(org.CreatedAt),
				TimestamppbToTime(org.UpdatedAt),
			)
		} else { // org ID is specified
			query = `
				INSERT INTO orgs (id, name, domains, twiml_app_sid, twilio_number, twilio_number_sid, service_type, template_id,
					is_call_forwarding_enabled, call_forwarding_type, primary_phone_number, sip_uri, created_at, updated_at)
				VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
				RETURNING id, name, domains, twiml_app_sid, twilio_number, twilio_number_sid, service_type, template_id,
					is_call_forwarding_enabled, call_forwarding_type, primary_phone_number, sip_uri, created_at, updated_at
			`
			row = tx.QueryRowContext(ctx, query,
				org.Id,
				org.Name,
				pq.Array(org.Domains),
				org.TwimlAppSid,
				org.TwilioNumber,
				org.TwilioNumberSid,
				org.ServiceType,
				org.TemplateId,
				org.IsCallForwardingEnabled,
				org.CallForwardingType,
				org.PrimaryPhoneNumber,
				org.SipUri,
				TimestamppbToTime(org.CreatedAt),
				TimestamppbToTime(org.UpdatedAt),
			)
		}

		currentTime := time.Now()
		org.CreatedAt = timestamppb.New(currentTime)
		org.UpdatedAt = timestamppb.New(currentTime)

		var domains []string
		var createdAt, updatedAt time.Time
		var primaryPhoneNumber, sipURI sql.NullString

		err := row.Scan(
			&org.Id,
			&org.Name,
			pq.Array(&domains),
			&org.TwimlAppSid,
			&org.TwilioNumber,
			&org.TwilioNumberSid,
			&org.ServiceType,
			&org.TemplateId,
			&org.IsCallForwardingEnabled,
			&org.CallForwardingType,
			&primaryPhoneNumber,
			&sipURI,
			&createdAt,
			&updatedAt,
		)
		if err != nil {
			herosentry.CaptureException(spanContext, err, "Failed to scan org creation result")
			return nil, err
		}
		org.Domains = domains
		if primaryPhoneNumber.Valid {
			// Ensure the phone number is standardized when reading from DB
			standardizedNumber, err := utils.StandardizeUSPhoneNumber(primaryPhoneNumber.String)
			if err != nil {
				return nil, fmt.Errorf("invalid primary_phone_number format in database: %v", err)
			}
			org.PrimaryPhoneNumber = standardizedNumber
		}
		if sipURI.Valid {
			org.SipUri = sipURI.String
		}
		org.CreatedAt = timestamppb.New(createdAt)
		org.UpdatedAt = timestamppb.New(updatedAt)
		return org, nil
	})
}

func (r *postgresOrgRepository) GetOrg(ctx context.Context, transaction *sql.Tx, orgID int32) (*pb.Org, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "postgresOrgRepository.GetOrg")
	defer finish()
	span.SetTag("org.id", fmt.Sprintf("%d", orgID))

	return database.WithSession(r.db, spanContext, transaction, func(tx *sql.Tx) (*pb.Org, error) {
		query := `
			SELECT id, name, domains, twiml_app_sid, twilio_number, twilio_number_sid, service_type, template_id,
				is_call_forwarding_enabled, call_forwarding_type, primary_phone_number, sip_uri, twilio_api_user_id, created_at, updated_at
			FROM orgs
			WHERE id = $1
		`
		org := &pb.Org{}
		var domains []string
		var createdAt, updatedAt time.Time
		var callForwardingType, primaryPhoneNumber, sipURI, twilioApiUserId sql.NullString

		err := tx.QueryRowContext(ctx, query, orgID).Scan(
			&org.Id,
			&org.Name,
			pq.Array(&domains),
			&org.TwimlAppSid,
			&org.TwilioNumber,
			&org.TwilioNumberSid,
			&org.ServiceType,
			&org.TemplateId,
			&org.IsCallForwardingEnabled,
			&callForwardingType,
			&primaryPhoneNumber,
			&sipURI,
			&twilioApiUserId,
			&createdAt,
			&updatedAt,
		)
		if err == sql.ErrNoRows {
			return nil, ErrOrgNotFound
		}
		if err != nil {
			herosentry.CaptureException(spanContext, err, "Failed to get org")
			return nil, err
		}
		org.Domains = domains
		if callForwardingType.Valid {
			org.CallForwardingType = callForwardingType.String
		}
		if primaryPhoneNumber.Valid {
			org.PrimaryPhoneNumber = primaryPhoneNumber.String
		}
		if sipURI.Valid {
			org.SipUri = sipURI.String
		}
		if twilioApiUserId.Valid {
			org.TwilioApiUserId = twilioApiUserId.String
		}
		org.CreatedAt = timestamppb.New(createdAt)
		org.UpdatedAt = timestamppb.New(updatedAt)
		return org, nil
	})
}

func (r *postgresOrgRepository) ListOrgs(ctx context.Context, transaction *sql.Tx) ([]*pb.Org, error) {
	spanContext, _, finish := herosentry.StartSpan(ctx, "postgresOrgRepository.ListOrgs")
	defer finish()

	return database.WithSession(r.db, spanContext, transaction, func(tx *sql.Tx) ([]*pb.Org, error) {
		query := `
			SELECT id, name, domains, twiml_app_sid, twilio_number, twilio_number_sid, service_type, template_id,
				is_call_forwarding_enabled, call_forwarding_type, primary_phone_number, sip_uri, twilio_api_user_id, created_at, updated_at
			FROM orgs
		`
		rows, err := tx.QueryContext(spanContext, query)
		if err != nil {
			herosentry.CaptureException(spanContext, err, "Failed to list orgs")
			return nil, err
		}
		defer rows.Close()

		var orgs []*pb.Org
		for rows.Next() {
			org := &pb.Org{}
			var domains []string
			var createdAt, updatedAt time.Time
			var twilioNumberSid, primaryPhoneNumber, sipURI, callForwardingType, twilioApiUserId sql.NullString

			err := rows.Scan(
				&org.Id,
				&org.Name,
				pq.Array(&domains),
				&org.TwimlAppSid,
				&org.TwilioNumber,
				&twilioNumberSid,
				&org.ServiceType,
				&org.TemplateId,
				&org.IsCallForwardingEnabled,
				&callForwardingType,
				&primaryPhoneNumber,
				&sipURI,
				&twilioApiUserId,
				&createdAt,
				&updatedAt,
			)
			if err != nil {
				return nil, err
			}
			org.Domains = domains
			if callForwardingType.Valid {
				org.CallForwardingType = callForwardingType.String
			}
			if twilioNumberSid.Valid {
				org.TwilioNumberSid = twilioNumberSid.String
			}
			if primaryPhoneNumber.Valid {
				org.PrimaryPhoneNumber = primaryPhoneNumber.String
			}
			if sipURI.Valid {
				org.SipUri = sipURI.String
			}
			if twilioApiUserId.Valid {
				org.TwilioApiUserId = twilioApiUserId.String
			}
			org.CreatedAt = timestamppb.New(createdAt)
			org.UpdatedAt = timestamppb.New(updatedAt)
			orgs = append(orgs, org)
		}
		if err := rows.Err(); err != nil {
			return nil, err
		}
		return orgs, nil
	})
}

func (r *postgresOrgRepository) DeleteOrg(ctx context.Context, transaction *sql.Tx, orgID int32) error {
	spanContext, span, finish := herosentry.StartSpan(ctx, "postgresOrgRepository.DeleteOrg")
	defer finish()
	span.SetTag("org.id", fmt.Sprintf("%d", orgID))

	return database.WithSessionErr(r.db, spanContext, transaction, func(tx *sql.Tx) error {
		query := `DELETE FROM orgs WHERE id = $1`
		result, err := tx.ExecContext(spanContext, query, orgID)
		if err != nil {
			herosentry.CaptureException(spanContext, err, "Failed to delete org")
			return err
		}
		rowsAffected, err := result.RowsAffected()
		if err != nil {
			return err
		}
		if rowsAffected == 0 {
			return ErrOrgNotFound
		}
		return nil
	})
}

func (r *postgresOrgRepository) UpdateOrg(ctx context.Context, transaction *sql.Tx, org *pb.Org) error {
	spanContext, span, finish := herosentry.StartSpan(ctx, "postgresOrgRepository.UpdateOrg")
	defer finish()
	span.SetTag("org.id", fmt.Sprintf("%d", org.Id))
	span.SetTag("org.name", org.Name)

	return database.WithSessionErr(r.db, spanContext, transaction, func(tx *sql.Tx) error {
		// Standardize phone numbers if present
		if org.PrimaryPhoneNumber != "" {
			standardizedNumber, err := utils.StandardizeUSPhoneNumber(org.PrimaryPhoneNumber)
			if err != nil {
				return fmt.Errorf("invalid primary_phone_number format: %v", err)
			}
			org.PrimaryPhoneNumber = standardizedNumber
		}

		query := `
			UPDATE orgs 
			SET name = $1, domains = $2, twiml_app_sid = $3, twilio_number = $4, twilio_number_sid = $5, 
				service_type = $6, template_id = $7, is_call_forwarding_enabled = $8, call_forwarding_type = $9, 
				primary_phone_number = $10, sip_uri = $11, twilio_api_user_id = $12, updated_at = $13
			WHERE id = $14
		`
		currentTime := time.Now()
		org.UpdatedAt = timestamppb.New(currentTime)
		result, err := tx.ExecContext(spanContext, query,
			org.Name,
			pq.Array(org.Domains),
			org.TwimlAppSid,
			org.TwilioNumber,
			org.TwilioNumberSid,
			org.ServiceType,
			org.TemplateId,
			org.IsCallForwardingEnabled,
			org.CallForwardingType,
			org.PrimaryPhoneNumber,
			org.SipUri,
			sql.NullString{String: org.TwilioApiUserId, Valid: org.TwilioApiUserId != ""},
			TimestamppbToTime(org.UpdatedAt),
			org.Id,
		)
		if err != nil {
			herosentry.CaptureException(spanContext, err, "Failed to update org")
			return err
		}
		rowsAffected, err := result.RowsAffected()
		if err != nil {
			return err
		}
		if rowsAffected == 0 {
			return ErrOrgNotFound
		}
		return nil
	})
}

func (r *postgresOrgRepository) CreateOrgAPIUser(ctx context.Context, transaction *sql.Tx, orgID int32, encryptedPassword string, hashedPassword string) (string, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "postgresOrgRepository.CreateOrgAPIUser")
	defer finish()
	span.SetTag("org.id", fmt.Sprintf("%d", orgID))

	return database.WithSession(r.db, spanContext, transaction, func(tx *sql.Tx) (string, error) {
		// First verify the org exists
		_, err := r.GetOrg(spanContext, tx, orgID)
		if err != nil {
			return "", err
		}

		createdAt := time.Now()
		updatedAt := time.Now()
		userID := uuid.New().String()

		// Insert the new API user
		query := `
			INSERT INTO org_api_users (id, org_id, encrypted_password, hashed_password, created_at, updated_at)
			VALUES ($1, $2, $3, $4, $5, $6)
		`
		_, err = tx.ExecContext(spanContext, query,
			userID,
			orgID,
			encryptedPassword,
			hashedPassword,
			createdAt,
			updatedAt,
		)
		if err != nil {
			herosentry.CaptureException(spanContext, err, "Failed to create API user")
			return "", fmt.Errorf("failed to create API user: %v", err)
		}

		return userID, nil
	})
}

func (r *postgresOrgRepository) GetOrgAPIUserById(ctx context.Context, transaction *sql.Tx, userId string) (*pb.OrgApiUser, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "postgresOrgRepository.GetOrgAPIUserById")
	defer finish()
	span.SetTag("user.id", userId)

	return database.WithSession(r.db, spanContext, transaction, func(tx *sql.Tx) (*pb.OrgApiUser, error) {
		query := `
			SELECT id, org_id, encrypted_password, hashed_password, created_at, updated_at
			FROM org_api_users
			WHERE id = $1
		`
		var orgID int32
		var id, encryptedPassword, hashedPassword string
		var createdAt, updatedAt time.Time
		err := tx.QueryRowContext(spanContext, query, userId).Scan(&id, &orgID, &encryptedPassword, &hashedPassword, &createdAt, &updatedAt)
		if err == sql.ErrNoRows {
			return nil, ErrOrgNotFound
		}
		if err != nil {
			herosentry.CaptureException(spanContext, err, "Failed to get org API user by ID")
			return nil, fmt.Errorf("failed to query credentials: %v", err)
		}
		return &pb.OrgApiUser{
			Id:                id,
			OrgId:             orgID,
			EncryptedPassword: encryptedPassword,
			HashedPassword:    hashedPassword,
			CreatedAt:         timestamppb.New(createdAt),
			UpdatedAt:         timestamppb.New(updatedAt),
		}, nil
	})
}

func (r *postgresOrgRepository) GetOrgAPIUser(ctx context.Context, transaction *sql.Tx, userId string) (*pb.OrgApiUser, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "postgresOrgRepository.GetOrgAPIUser")
	defer finish()
	span.SetTag("user.id", userId)

	return database.WithSession(r.db, spanContext, transaction, func(tx *sql.Tx) (*pb.OrgApiUser, error) {
		query := `
			SELECT id, org_id, encrypted_password, hashed_password, created_at, updated_at
			FROM org_api_users
			WHERE id = $1
		`
		var orgID int32
		var id, encryptedPassword, hashedPassword string
		var createdAt, updatedAt time.Time
		err := tx.QueryRowContext(spanContext, query, userId).Scan(&id, &orgID, &encryptedPassword, &hashedPassword, &createdAt, &updatedAt)
		if err == sql.ErrNoRows {
			return nil, ErrOrgNotFound
		}
		if err != nil {
			herosentry.CaptureException(spanContext, err, "Failed to get org API user")
			return nil, fmt.Errorf("failed to query credentials: %v", err)
		}
		return &pb.OrgApiUser{
			Id:                id,
			OrgId:             orgID,
			EncryptedPassword: encryptedPassword,
			HashedPassword:    hashedPassword,
			CreatedAt:         timestamppb.New(createdAt),
			UpdatedAt:         timestamppb.New(updatedAt),
		}, nil
	})
}

func (r *postgresOrgRepository) GetZelloChannels(ctx context.Context, transaction *sql.Tx) ([]*pb.ZelloChannel, error) {
	spanContext, _, finish := herosentry.StartSpan(ctx, "postgresOrgRepository.GetZelloChannels")
	defer finish()

	return database.WithSession(r.db, spanContext, transaction, func(tx *sql.Tx) ([]*pb.ZelloChannel, error) {
		selectQuery := `
			SELECT id, org_id, zello_channel_id, display_name
			FROM zello_channels
		`
		queryRows, queryError := tx.QueryContext(spanContext, selectQuery)
		if queryError != nil {
			return nil, queryError
		}
		defer queryRows.Close()

		var zelloChannels []*pb.ZelloChannel
		for queryRows.Next() {
			var zelloChannel pb.ZelloChannel
			if scanError := queryRows.Scan(&zelloChannel.Id, &zelloChannel.OrgId, &zelloChannel.ZelloChannelId, &zelloChannel.DisplayName); scanError != nil {
				return nil, scanError
			}
			zelloChannels = append(zelloChannels, &zelloChannel)
		}
		if rowsError := queryRows.Err(); rowsError != nil {
			return nil, rowsError
		}
		return zelloChannels, nil
	})
}

func (r *postgresOrgRepository) CreateZelloChannel(ctx context.Context, transaction *sql.Tx, orgID int32, zelloChannel *pb.ZelloChannel) error {
	spanContext, span, finish := herosentry.StartSpan(ctx, "postgresOrgRepository.CreateZelloChannel")
	defer finish()
	span.SetTag("org.id", fmt.Sprintf("%d", orgID))
	span.SetTag("zello.channel_id", zelloChannel.ZelloChannelId)

	return database.WithSessionErr(r.db, spanContext, transaction, func(tx *sql.Tx) error {
		zelloChannel.Id = uuid.New().String()
		query := `
			INSERT INTO zello_channels (id, org_id, zello_channel_id, display_name)
			VALUES ($1, $2, $3, $4)
		`
		_, err := tx.ExecContext(spanContext, query,
			zelloChannel.Id,
			orgID,
			zelloChannel.ZelloChannelId,
			zelloChannel.DisplayName,
		)
		if err != nil {
			herosentry.CaptureException(spanContext, err, "Failed to create Zello channel")
			return fmt.Errorf("failed to create Zello channel: %v", err)
		}
		return nil
	})
}

func (r *postgresOrgRepository) DeleteZelloChannel(ctx context.Context, transaction *sql.Tx, orgID int32, zelloChannelIds []string) error {
	spanContext, span, finish := herosentry.StartSpan(ctx, "postgresOrgRepository.DeleteZelloChannel")
	defer finish()
	span.SetTag("org.id", fmt.Sprintf("%d", orgID))
	span.SetTag("channel_count", fmt.Sprintf("%d", len(zelloChannelIds)))

	return database.WithSessionErr(r.db, spanContext, transaction, func(tx *sql.Tx) error {
		query := `DELETE FROM zello_channels WHERE org_id = $1 AND zello_channel_id = ANY($2)`
		_, err := tx.ExecContext(spanContext, query, orgID, pq.Array(zelloChannelIds))
		return err
	})
}

// GetOrgAssetsWithCognitoJwtSub returns a list of assets with Cognito JWT sub for an organization.
func (r *postgresOrgRepository) GetOrgAssetsWithCognitoJwtSub(ctx context.Context, transaction *sql.Tx, orgID int32) ([]*AssetInfo, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "postgresOrgRepository.GetOrgAssetsWithCognitoJwtSub")
	defer finish()
	span.SetTag("org.id", fmt.Sprintf("%d", orgID))

	return database.WithSession(r.db, spanContext, transaction, func(tx *sql.Tx) ([]*AssetInfo, error) {
		query := `
			SELECT id, cognito_jwt_sub
			FROM assets
			WHERE org_id = $1
		`

		rows, err := tx.QueryContext(spanContext, query, orgID)
		if err != nil {
			return nil, fmt.Errorf("failed to query assets: %v", err)
		}
		defer rows.Close()

		var assets []*AssetInfo
		for rows.Next() {
			asset := &AssetInfo{}
			if err := rows.Scan(&asset.ID, &asset.CognitoJwtSub); err != nil {
				return nil, fmt.Errorf("failed to scan asset row: %v", err)
			}
			assets = append(assets, asset)
		}

		if err := rows.Err(); err != nil {
			return nil, fmt.Errorf("error iterating asset rows: %v", err)
		}

		return assets, nil
	})
}

// GetZelloCreds returns the Zello credentials for an asset.
func (r *postgresOrgRepository) GetZelloCreds(ctx context.Context, transaction *sql.Tx, assetID string) (*assets.ZelloCreds, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "postgresOrgRepository.GetZelloCreds")
	defer finish()
	span.SetTag("asset.id", assetID)

	return database.WithSession(r.db, spanContext, transaction, func(tx *sql.Tx) (*assets.ZelloCreds, error) {
		query := `
			SELECT org_id, asset_id, username, encrypted_password
			FROM zello_creds
			WHERE asset_id = $1
		`
		var orgID int32
		var assetID string
		var username, encryptedPassword string
		err := tx.QueryRowContext(spanContext, query, assetID).Scan(&orgID, &assetID, &username, &encryptedPassword)
		if err == sql.ErrNoRows {
			return nil, nil
		}
		if err != nil {
			return nil, fmt.Errorf("failed to query Zello credentials: %v", err)
		}
		return &assets.ZelloCreds{
			OrgId:             orgID,
			AssetId:           assetID,
			Username:          username,
			EncryptedPassword: encryptedPassword,
		}, nil
	})
}

// DeleteZelloCreds deletes the Zello credentials for an asset.
func (r *postgresOrgRepository) DeleteZelloCreds(ctx context.Context, transaction *sql.Tx, assetID string) error {
	spanContext, span, finish := herosentry.StartSpan(ctx, "postgresOrgRepository.DeleteZelloCreds")
	defer finish()
	span.SetTag("asset.id", assetID)

	return database.WithSessionErr(r.db, spanContext, transaction, func(tx *sql.Tx) error {
		query := `DELETE FROM zello_creds WHERE asset_id = $1`
		_, err := tx.ExecContext(spanContext, query, assetID)
		if err != nil {
			herosentry.CaptureException(spanContext, err, "Failed to delete Zello credentials")
			return fmt.Errorf("failed to delete Zello credentials: %v", err)
		}
		return nil
	})
}

func (r *postgresOrgRepository) GetTwilioQueueSid(ctx context.Context, transaction *sql.Tx, orgID int32) (string, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "postgresOrgRepository.GetTwilioQueueSid")
	defer finish()
	span.SetTag("org.id", fmt.Sprintf("%d", orgID))

	return database.WithSession(r.db, spanContext, transaction, func(tx *sql.Tx) (string, error) {
		query := `SELECT twilio_queue_sid FROM twilio_queue_configurations WHERE org_id = $1`
		var twilioQueueSid sql.NullString
		err := tx.QueryRowContext(spanContext, query, orgID).Scan(&twilioQueueSid)
		if err == sql.ErrNoRows {
			return "", nil
		}
		if err != nil {
			return "", fmt.Errorf("failed to query Twilio queue configuration: %v", err)
		}
		if !twilioQueueSid.Valid {
			return "", nil
		}
		return twilioQueueSid.String, nil
	})
}

func (r *postgresOrgRepository) DeleteTwilioQueueConfiguration(ctx context.Context, transaction *sql.Tx, orgID int32) error {
	spanContext, span, finish := herosentry.StartSpan(ctx, "postgresOrgRepository.DeleteTwilioQueueConfiguration")
	defer finish()
	span.SetTag("org.id", fmt.Sprintf("%d", orgID))

	return database.WithSessionErr(r.db, spanContext, transaction, func(tx *sql.Tx) error {
		query := `DELETE FROM twilio_queue_configurations WHERE org_id = $1`
		_, err := tx.ExecContext(spanContext, query, orgID)
		return err
	})
}

// StoreTwilioQueueConfiguration stores a Twilio queue configuration for an organization.
func (r *postgresOrgRepository) StoreTwilioQueueConfiguration(ctx context.Context, transaction *sql.Tx, orgID int32, friendlyName string, queueSID string, description string) error {
	spanContext, span, finish := herosentry.StartSpan(ctx, "postgresOrgRepository.StoreTwilioQueueConfiguration")
	defer finish()
	span.SetTag("org.id", fmt.Sprintf("%d", orgID))
	span.SetTag("queue.sid", queueSID)

	return database.WithSessionErr(r.db, spanContext, transaction, func(tx *sql.Tx) error {
		query := `INSERT INTO twilio_queue_configurations (friendly_name, twilio_queue_sid, org_id, description) 
		 VALUES ($1, $2, $3, $4)`
		_, err := tx.ExecContext(
			spanContext,
			query,
			friendlyName,
			queueSID,
			orgID,
			description,
		)
		if err != nil {
			herosentry.CaptureException(spanContext, err, "Failed to store Twilio queue configuration")
			return fmt.Errorf("failed to store Twilio queue SID: %w", err)
		}
		return nil
	})
}

func (r *postgresOrgRepository) InsertOrgQueue(ctx context.Context, transaction *sql.Tx, req *pb.InsertOrgQueueRequest) (*pb.OrgQueue, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "postgresOrgRepository.InsertOrgQueue")
	defer finish()
	span.SetTag("org.id", fmt.Sprintf("%d", req.OrgId))
	span.SetTag("queue.friendly_name", req.FriendlyName)

	return database.WithSession(r.db, spanContext, transaction, func(tx *sql.Tx) (*pb.OrgQueue, error) {
		query := `
			INSERT INTO twilio_queue_configurations (friendly_name, twilio_queue_sid, org_id, description, created_at, updated_at)
			VALUES ($1, $2, $3, $4, NOW(), NOW())
			RETURNING id, friendly_name, twilio_queue_sid, org_id, description, created_at, updated_at
		`
		row := tx.QueryRowContext(spanContext, query, req.FriendlyName, req.TwilioQueueSid, req.OrgId, req.Description)

		orgQueue := &pb.OrgQueue{}
		var createdAt, updatedAt time.Time

		err := row.Scan(
			&orgQueue.Id, // Assuming pb.OrgQueue has an int32 Id field matching the table
			&orgQueue.FriendlyName,
			&orgQueue.TwilioQueueSid,
			&orgQueue.OrgId,
			&orgQueue.Description,
			&createdAt,
			&updatedAt,
		)

		if err != nil {
			if err == sql.ErrNoRows {
				// This shouldn't happen with INSERT ... RETURNING unless something is very wrong
				return nil, fmt.Errorf("failed to insert org queue, no rows returned: %w", err)
			}
			herosentry.CaptureException(spanContext, err, "Failed to insert org queue")
			return nil, fmt.Errorf("failed to insert org queue and scan result: %w", err)
		}

		orgQueue.CreatedAt = timestamppb.New(createdAt)
		orgQueue.UpdatedAt = timestamppb.New(updatedAt)

		return orgQueue, nil
	})
}

// AddToContactBook creates a new contact record in the organization's contact book
func (r *postgresOrgRepository) AddToContactBook(ctx context.Context, transaction *sql.Tx, contact *pb.ContactRecord) error {
	spanContext, span, finish := herosentry.StartSpan(ctx, "postgresOrgRepository.AddToContactBook")
	defer finish()
	span.SetTag("contact.id", contact.Id)
	span.SetTag("org.id", fmt.Sprintf("%d", contact.OrgId))

	return database.WithSessionErr(r.db, spanContext, transaction, func(tx *sql.Tx) error {
		query := `
			INSERT INTO org_contacts_book (id, org_id, name, phone, created_at, updated_at)
			VALUES ($1, $2, $3, $4, $5, $6)
		`
		_, err := tx.ExecContext(spanContext, query,
			contact.Id,
			contact.OrgId,
			contact.Name,
			contact.Phone,
			TimestamppbToTime(contact.CreatedAt),
			TimestamppbToTime(contact.UpdatedAt),
		)
		return err
	})
}

// UpdateContactInContactBook updates an existing contact record in the organization's contact book
func (r *postgresOrgRepository) UpdateContactInContactBook(ctx context.Context, transaction *sql.Tx, contact *pb.ContactRecord) error {
	spanContext, span, finish := herosentry.StartSpan(ctx, "postgresOrgRepository.UpdateContactInContactBook")
	defer finish()
	span.SetTag("contact.id", contact.Id)

	return database.WithSessionErr(r.db, spanContext, transaction, func(tx *sql.Tx) error {
		query := `
			UPDATE org_contacts_book 
			SET name = $2, phone = $3, updated_at = $4
			WHERE id = $1
		`
		result, err := tx.ExecContext(spanContext, query,
			contact.Id,
			contact.Name,
			contact.Phone,
			TimestamppbToTime(contact.UpdatedAt),
		)
		if err != nil {
			return err
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil {
			return err
		}
		if rowsAffected == 0 {
			return fmt.Errorf("contact with ID %s not found", contact.Id)
		}

		return nil
	})
}

// DeleteFromContactBook deletes a contact record from the organization's contact book
func (r *postgresOrgRepository) DeleteFromContactBook(ctx context.Context, transaction *sql.Tx, contactID string) error {
	spanContext, span, finish := herosentry.StartSpan(ctx, "postgresOrgRepository.DeleteFromContactBook")
	defer finish()
	span.SetTag("contact.id", contactID)

	return database.WithSessionErr(r.db, spanContext, transaction, func(tx *sql.Tx) error {
		query := `DELETE FROM org_contacts_book WHERE id = $1`
		result, err := tx.ExecContext(spanContext, query, contactID)
		if err != nil {
			return err
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil {
			return err
		}
		if rowsAffected == 0 {
			return fmt.Errorf("contact with ID %s not found", contactID)
		}

		return nil
	})
}

// GetContactFromContactBook retrieves a contact record by its ID from the organization's contact book
func (r *postgresOrgRepository) GetContactFromContactBook(ctx context.Context, transaction *sql.Tx, contactID string) (*pb.ContactRecord, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "postgresOrgRepository.GetContactFromContactBook")
	defer finish()
	span.SetTag("contact.id", contactID)

	return database.WithSession(r.db, spanContext, transaction, func(tx *sql.Tx) (*pb.ContactRecord, error) {
		query := `
			SELECT id, org_id, name, phone, created_at, updated_at
			FROM org_contacts_book 
			WHERE id = $1
		`
		row := tx.QueryRowContext(spanContext, query, contactID)

		var contact pb.ContactRecord
		var createdAt, updatedAt time.Time

		err := row.Scan(
			&contact.Id,
			&contact.OrgId,
			&contact.Name,
			&contact.Phone,
			&createdAt,
			&updatedAt,
		)
		if err != nil {
			if err == sql.ErrNoRows {
				return nil, fmt.Errorf("contact with ID %s not found", contactID)
			}
			return nil, err
		}

		contact.CreatedAt = timestamppb.New(createdAt)
		contact.UpdatedAt = timestamppb.New(updatedAt)

		return &contact, nil
	})
}

// ContactBookPageResult holds the result of a paginated contact book query
type ContactBookPageResult struct {
	Contacts      []*pb.ContactRecord
	NextPageToken string
	TotalCount    int32
}

// ListContactsInContactBook returns paginated contact records for an organization's contact book
func (r *postgresOrgRepository) ListContactsInContactBook(ctx context.Context, transaction *sql.Tx, orgID int32, pageToken string, pageSize int32) ([]*pb.ContactRecord, string, int32, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "postgresOrgRepository.ListContactsInContactBook")
	defer finish()
	span.SetTag("org.id", fmt.Sprintf("%d", orgID))
	span.SetTag("page_size", fmt.Sprintf("%d", pageSize))

	result, err := database.WithSession(r.db, spanContext, transaction, func(tx *sql.Tx) (*ContactBookPageResult, error) {
		// Enforce max page size of 100
		if pageSize <= 0 || pageSize > 100 {
			pageSize = 100
		}

		// Handle pagination with OFFSET/LIMIT approach
		// For production, consider using cursor-based pagination for better performance
		var offset int32 = 0
		if pageToken != "" {
			// Simple implementation: pageToken is the offset as string
			// In production, consider encoding more information or using cursor-based pagination
			if parsedOffset, err := strconv.ParseInt(pageToken, 10, 32); err == nil {
				offset = int32(parsedOffset)
			}
		}

		// Get total count
		countQuery := `SELECT COUNT(*) FROM org_contacts_book WHERE org_id = $1`
		var totalCount int32
		err := tx.QueryRowContext(spanContext, countQuery, orgID).Scan(&totalCount)
		if err != nil {
			return nil, err
		}

		// Get paginated results
		query := `
			SELECT id, org_id, name, phone, created_at, updated_at
			FROM org_contacts_book 
			WHERE org_id = $1
			ORDER BY name ASC
			LIMIT $2 OFFSET $3
		`
		rows, err := tx.QueryContext(spanContext, query, orgID, pageSize, offset)
		if err != nil {
			return nil, err
		}
		defer rows.Close()

		var contacts []*pb.ContactRecord
		for rows.Next() {
			var contact pb.ContactRecord
			var createdAt, updatedAt time.Time

			err := rows.Scan(
				&contact.Id,
				&contact.OrgId,
				&contact.Name,
				&contact.Phone,
				&createdAt,
				&updatedAt,
			)
			if err != nil {
				return nil, err
			}

			contact.CreatedAt = timestamppb.New(createdAt)
			contact.UpdatedAt = timestamppb.New(updatedAt)

			contacts = append(contacts, &contact)
		}

		if err := rows.Err(); err != nil {
			return nil, err
		}

		// Calculate next page token
		var nextPageToken string
		if len(contacts) == int(pageSize) && offset+pageSize < totalCount {
			nextPageToken = strconv.FormatInt(int64(offset+pageSize), 10)
		}

		return &ContactBookPageResult{
			Contacts:      contacts,
			NextPageToken: nextPageToken,
			TotalCount:    totalCount,
		}, nil
	})

	if err != nil {
		return nil, "", 0, err
	}

	return result.Contacts, result.NextPageToken, result.TotalCount, nil
}

// GetContactByPhoneNumber retrieves a contact record by phone number from the organization's contact book
func (r *postgresOrgRepository) GetContactByPhoneNumber(ctx context.Context, transaction *sql.Tx, orgID int32, phone string) (*pb.ContactRecord, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "postgresOrgRepository.GetContactByPhoneNumber")
	defer finish()
	span.SetTag("org.id", fmt.Sprintf("%d", orgID))

	return database.WithSession(r.db, spanContext, transaction, func(tx *sql.Tx) (*pb.ContactRecord, error) {
		query := `
			SELECT id, org_id, name, phone, created_at, updated_at
			FROM org_contacts_book	 
			WHERE org_id = $1 AND phone = $2
			LIMIT 1
		`
		row := tx.QueryRowContext(spanContext, query, orgID, phone)

		var contact pb.ContactRecord
		var createdAt, updatedAt time.Time

		err := row.Scan(
			&contact.Id,
			&contact.OrgId,
			&contact.Name,
			&contact.Phone,
			&createdAt,
			&updatedAt,
		)
		if err != nil {
			if err == sql.ErrNoRows {
				return nil, fmt.Errorf("contact with phone %s not found in org %d", phone, orgID)
			}
			return nil, err
		}

		contact.CreatedAt = timestamppb.New(createdAt)
		contact.UpdatedAt = timestamppb.New(updatedAt)

		return &contact, nil
	})
}

// CreatePreRegistrationMapping creates a new pre-registration user mapping
func (r *postgresOrgRepository) CreatePreRegistrationMapping(ctx context.Context, transaction *sql.Tx, mapping *pb.PreRegistrationUserMapping) (*pb.PreRegistrationUserMapping, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "postgresOrgRepository.CreatePreRegistrationMapping")
	defer finish()
	span.SetTag("org.id", fmt.Sprintf("%d", mapping.OrgId))

	return database.WithSession(r.db, spanContext, transaction, func(tx *sql.Tx) (*pb.PreRegistrationUserMapping, error) {
		query := `
			INSERT INTO pre_registration_user_mappings (email, org_id, asset_type, created_by)
			VALUES ($1, $2, $3, $4)
			RETURNING id, email, org_id, asset_type, created_at, used_at, created_by
		`

		var createdAt time.Time
		var usedAt sql.NullTime

		err := tx.QueryRowContext(spanContext, query,
			mapping.Email,
			mapping.OrgId,
			mapping.AssetType,
			mapping.CreatedBy,
		).Scan(
			&mapping.Id,
			&mapping.Email,
			&mapping.OrgId,
			&mapping.AssetType,
			&createdAt,
			&usedAt,
			&mapping.CreatedBy,
		)
		if err != nil {
			return nil, err
		}

		mapping.CreatedAt = timestamppb.New(createdAt)
		if usedAt.Valid {
			mapping.UsedAt = timestamppb.New(usedAt.Time)
		}

		return mapping, nil
	})
}

// GetPreRegistrationMapping retrieves a pre-registration mapping by email and org ID
func (r *postgresOrgRepository) GetPreRegistrationMapping(ctx context.Context, transaction *sql.Tx, email string, orgID int32) (*pb.PreRegistrationUserMapping, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "postgresOrgRepository.GetPreRegistrationMapping")
	defer finish()
	span.SetTag("org.id", fmt.Sprintf("%d", orgID))

	return database.WithSession(r.db, spanContext, transaction, func(tx *sql.Tx) (*pb.PreRegistrationUserMapping, error) {
		query := `
			SELECT id, email, org_id, asset_type, created_at, used_at, created_by
			FROM pre_registration_user_mappings
			WHERE email = $1 AND org_id = $2
			LIMIT 1
		`

		row := tx.QueryRowContext(spanContext, query, email, orgID)

		var mapping pb.PreRegistrationUserMapping
		var createdAt time.Time
		var usedAt sql.NullTime
		var assetType int32

		err := row.Scan(
			&mapping.Id,
			&mapping.Email,
			&mapping.OrgId,
			&assetType,
			&createdAt,
			&usedAt,
			&mapping.CreatedBy,
		)
		if err != nil {
			if err == sql.ErrNoRows {
				return nil, fmt.Errorf("pre-registration mapping not found for email %s in org %d", email, orgID)
			}
			return nil, err
		}

		mapping.AssetType = assets.AssetType(assetType)
		mapping.CreatedAt = timestamppb.New(createdAt)
		if usedAt.Valid {
			mapping.UsedAt = timestamppb.New(usedAt.Time)
		}

		return &mapping, nil
	})
}

// ListPreRegistrationMappings retrieves paginated pre-registration mappings for an organization
func (r *postgresOrgRepository) ListPreRegistrationMappings(ctx context.Context, transaction *sql.Tx, orgID int32, pageToken string, pageSize int32, includeUsed bool) ([]*pb.PreRegistrationUserMapping, string, int32, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "postgresOrgRepository.ListPreRegistrationMappings")
	defer finish()
	span.SetTag("org.id", fmt.Sprintf("%d", orgID))
	span.SetTag("include_used", fmt.Sprintf("%v", includeUsed))

	type MappingPageResult struct {
		Mappings      []*pb.PreRegistrationUserMapping
		NextPageToken string
		TotalCount    int32
	}

	result, err := database.WithSession(r.db, spanContext, transaction, func(tx *sql.Tx) (*MappingPageResult, error) {
		// Parse page token for offset
		offset := int32(0)
		if pageToken != "" {
			if parsedOffset, err := strconv.ParseInt(pageToken, 10, 32); err == nil {
				offset = int32(parsedOffset)
			}
		}

		// Set default page size
		if pageSize <= 0 || pageSize > 100 {
			pageSize = 50
		}

		// Build query with optional used_at filter
		var countQuery, query string
		args := []interface{}{orgID}

		if !includeUsed {
			// Get total count with used_at filter
			countQuery = "SELECT COUNT(*) FROM pre_registration_user_mappings WHERE org_id = $1 AND used_at IS NULL"

			// Get paginated results with used_at filter
			query = `
				SELECT id, email, org_id, asset_type, created_at, used_at, created_by
				FROM pre_registration_user_mappings
				WHERE org_id = $1 AND used_at IS NULL
				ORDER BY created_at DESC
				LIMIT $2 OFFSET $3
			`
		} else {
			// Get total count without used_at filter
			countQuery = "SELECT COUNT(*) FROM pre_registration_user_mappings WHERE org_id = $1"

			// Get paginated results without used_at filter
			query = `
				SELECT id, email, org_id, asset_type, created_at, used_at, created_by
				FROM pre_registration_user_mappings
				WHERE org_id = $1
				ORDER BY created_at DESC
				LIMIT $2 OFFSET $3
			`
		}

		var totalCount int32
		err := tx.QueryRowContext(spanContext, countQuery, args...).Scan(&totalCount)
		if err != nil {
			return nil, err
		}

		args = append(args, pageSize, offset)
		rows, err := tx.QueryContext(spanContext, query, args...)
		if err != nil {
			return nil, err
		}
		defer rows.Close()

		var mappings []*pb.PreRegistrationUserMapping
		for rows.Next() {
			var mapping pb.PreRegistrationUserMapping
			var createdAt time.Time
			var usedAt sql.NullTime
			var assetType int32

			err := rows.Scan(
				&mapping.Id,
				&mapping.Email,
				&mapping.OrgId,
				&assetType,
				&createdAt,
				&usedAt,
				&mapping.CreatedBy,
			)
			if err != nil {
				return nil, err
			}

			mapping.AssetType = assets.AssetType(assetType)
			mapping.CreatedAt = timestamppb.New(createdAt)
			if usedAt.Valid {
				mapping.UsedAt = timestamppb.New(usedAt.Time)
			}

			mappings = append(mappings, &mapping)
		}

		// Calculate next page token
		nextPageToken := ""
		if len(mappings) == int(pageSize) && offset+pageSize < totalCount {
			nextPageToken = strconv.FormatInt(int64(offset+pageSize), 10)
		}

		return &MappingPageResult{
			Mappings:      mappings,
			NextPageToken: nextPageToken,
			TotalCount:    totalCount,
		}, nil
	})

	if err != nil {
		return nil, "", 0, err
	}

	return result.Mappings, result.NextPageToken, result.TotalCount, nil
}

// UpdatePreRegistrationMapping updates an existing pre-registration mapping
func (r *postgresOrgRepository) UpdatePreRegistrationMapping(ctx context.Context, transaction *sql.Tx, mapping *pb.PreRegistrationUserMapping) (*pb.PreRegistrationUserMapping, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "postgresOrgRepository.UpdatePreRegistrationMapping")
	defer finish()
	span.SetTag("mapping.id", mapping.Id)

	return database.WithSession(r.db, spanContext, transaction, func(tx *sql.Tx) (*pb.PreRegistrationUserMapping, error) {
		query := `
			UPDATE pre_registration_user_mappings
			SET asset_type = $2
			WHERE id = $1 AND used_at IS NULL
			RETURNING id, email, org_id, asset_type, created_at, used_at, created_by
		`

		row := tx.QueryRowContext(spanContext, query,
			mapping.Id,
			int32(mapping.AssetType),
		)

		var createdAt time.Time
		var usedAt sql.NullTime
		var assetType int32

		err := row.Scan(
			&mapping.Id,
			&mapping.Email,
			&mapping.OrgId,
			&assetType,
			&createdAt,
			&usedAt,
			&mapping.CreatedBy,
		)
		if err != nil {
			if err == sql.ErrNoRows {
				return nil, fmt.Errorf("pre-registration mapping not found or already used: %s", mapping.Id)
			}
			return nil, err
		}

		mapping.AssetType = assets.AssetType(assetType)
		mapping.CreatedAt = timestamppb.New(createdAt)
		if usedAt.Valid {
			mapping.UsedAt = timestamppb.New(usedAt.Time)
		}

		return mapping, nil
	})
}

// DeletePreRegistrationMapping deletes a pre-registration mapping
func (r *postgresOrgRepository) DeletePreRegistrationMapping(ctx context.Context, transaction *sql.Tx, mappingID string) error {
	spanContext, span, finish := herosentry.StartSpan(ctx, "postgresOrgRepository.DeletePreRegistrationMapping")
	defer finish()
	span.SetTag("mapping.id", mappingID)

	return database.WithSessionErr(r.db, spanContext, transaction, func(tx *sql.Tx) error {
		query := `DELETE FROM pre_registration_user_mappings WHERE id = $1`
		result, err := tx.ExecContext(spanContext, query, mappingID)
		if err != nil {
			return err
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil {
			return err
		}

		if rowsAffected == 0 {
			return fmt.Errorf("pre-registration mapping not found: %s", mappingID)
		}

		return nil
	})
}

// MarkMappingAsUsed marks a pre-registration mapping as used
func (r *postgresOrgRepository) MarkMappingAsUsed(ctx context.Context, transaction *sql.Tx, mappingID string) error {
	spanContext, span, finish := herosentry.StartSpan(ctx, "postgresOrgRepository.MarkMappingAsUsed")
	defer finish()
	span.SetTag("mapping.id", mappingID)

	return database.WithSessionErr(r.db, spanContext, transaction, func(tx *sql.Tx) error {
		query := `
			UPDATE pre_registration_user_mappings
			SET used_at = CURRENT_TIMESTAMP
			WHERE id = $1 AND used_at IS NULL
		`
		result, err := tx.ExecContext(spanContext, query, mappingID)
		if err != nil {
			return err
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil {
			return err
		}

		if rowsAffected == 0 {
			return fmt.Errorf("pre-registration mapping not found: %s", mappingID)
		}

		return nil
	})
}
