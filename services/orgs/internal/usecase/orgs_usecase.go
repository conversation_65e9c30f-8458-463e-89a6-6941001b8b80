package usecase

import (
	"context"
	"database/sql"
	"encoding/base64"
	"encoding/hex"
	"errors"
	"fmt"
	"log"
	"os"
	"regexp"
	"strconv"
	"strings"
	"time"

	cmncontext "common/context"

	"github.com/google/uuid"
	_ "github.com/lib/pq"
	"github.com/twilio/twilio-go"
	api "github.com/twilio/twilio-go/rest/api/v2010"
	"google.golang.org/protobuf/types/known/timestamppb"

	services "common/clients/services"
	zello "common/clients/zello"
	"common/herosentry"
	utils "common/utils"
	"orgs/internal/config"
	orgRepository "orgs/internal/data"
	util "orgs/internal/util"
	assets "proto/hero/assets/v2"
	orgs "proto/hero/orgs/v1"
	permspb "proto/hero/permissions/v1"

	"github.com/aws/aws-sdk-go-v2/aws"
	awsconfig "github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/cognitoidentityprovider"
	"github.com/aws/aws-sdk-go-v2/service/cognitoidentityprovider/types"
	"github.com/aws/aws-sdk-go-v2/service/kms"

	"crypto/rand"

	connect "connectrpc.com/connect"
	"golang.org/x/crypto/bcrypt"
)

const UsaCountryCode = "US"

// Regex validator for a friendly name for a Twilio queue
var friendlyNamePattern = regexp.MustCompile(`^[\w\s\-]+$`)

// Max length for a friendly name for a Twilio queue
const maxFriendlyNameLen = 64

func ValidateFriendlyName(name string) error {
	if len(name) == 0 {
		return errors.New("friendly name cannot be empty")
	}
	if len(name) > maxFriendlyNameLen {
		return errors.New("friendly name exceeds max length (64 chars)")
	}
	if !friendlyNamePattern.MatchString(name) {
		return errors.New("friendly name contains invalid characters (only letters, numbers, spaces, dashes, underscores allowed)")
	}
	return nil
}

// OrgUseCase defines the use-case layer for organization operations.
type OrgUseCase struct {
	database                *sql.DB // Only needed for transactional operations.
	orgRepo                 orgRepository.OrgRepository
	kmsClient               *kms.Client
	kmsKeyArn               string
	cognitoClient           *cognitoidentityprovider.Client
	userPoolId              string
	templates               *config.TemplatesConfig
	permsClient             services.PermissionClient
	commsServerPublicDomain string
}

// NewOrgUseCase creates a new OrgUseCase.
func NewOrgUseCase(
	database *sql.DB,
	orgRepo orgRepository.OrgRepository) *OrgUseCase {
	// For in-memory orgRepository, we need a dummy DB to support transactions.
	if database == nil {
		var openError error
		database, openError = sql.Open("sqlite", ":memory:")
		if openError != nil {
			log.Fatalf("failed to open in-memory sqlite db: %v", openError)
		}
	}

	ctx := context.Background()
	awsRegion := os.Getenv("AWS_REGION")
	if awsRegion == "" {
		log.Fatal("FATAL: AWS_REGION environment variable not set")
	}
	cfg, err := awsconfig.LoadDefaultConfig(ctx, awsconfig.WithRegion(awsRegion))
	if err != nil {
		log.Fatalf("failed to load AWS config: %v", err)
	}

	kmsKeyArn := os.Getenv("KMS_KEY_ARN")
	if kmsKeyArn == "" {
		kmsKeyArn = "test-key-arn"
	}

	kmsClient := kms.NewFromConfig(cfg)
	cognitoClient := cognitoidentityprovider.NewFromConfig(cfg)

	userPoolId := os.Getenv("COGNITO_USER_POOL_ID")
	if userPoolId == "" {
		log.Fatalf("COGNITO_USER_POOL_ID is not set")
	}

	// Load organization templates
	var templates *config.TemplatesConfig
	templates, err = config.LoadTemplates("/app/config/org_templates.yaml")
	if err != nil {
		log.Fatalf("failed to load organization templates: %v", err)
	}

	permsClient := services.NewPermissionClient(os.Getenv("PERMS_SERVICE_URL"), herosentry.RPCClientInterceptor())

	commsServerPublicDomain := os.Getenv("COMMS_SERVER_PUBLIC_DOMAIN")
	if commsServerPublicDomain == "" {
		log.Fatalf("COMMS_SERVER_PUBLIC_DOMAIN is not set")
	}

	return &OrgUseCase{
		database:                database,
		orgRepo:                 orgRepo,
		kmsClient:               kmsClient,
		kmsKeyArn:               kmsKeyArn,
		cognitoClient:           cognitoClient,
		userPoolId:              userPoolId,
		templates:               templates,
		permsClient:             permsClient,
		commsServerPublicDomain: commsServerPublicDomain,
	}
}

// CreateOrg creates a new organization.
func (orgUseCase *OrgUseCase) CreateOrg(ctx context.Context, org *orgs.Org) error {
	spanContext, span, finish := herosentry.StartSpan(ctx, "OrgUseCase.CreateOrg")
	defer finish()
	span.SetTag("org.name", org.Name)
	span.SetTag("org.service_type", org.ServiceType.String())

	if org.Name == "" {
		err := fmt.Errorf("organization name is required")
		herosentry.CaptureException(spanContext, err, "Organization name validation failed")
		return err
	}
	if org.ServiceType == orgs.ServiceType_SERVICE_TYPE_UNSPECIFIED {
		org.ServiceType = orgs.ServiceType_SERVICE_TYPE_DEMO
	}

	if org.PrimaryPhoneNumber == "" {
		err := fmt.Errorf("primary_phone_number is required for twilio phone number claiming")
		herosentry.CaptureException(spanContext, err, "Primary phone number validation failed")
		return err
	}

	// Validate call forwarding settings
	if org.IsCallForwardingEnabled {
		if org.CallForwardingType == "" {
			err := fmt.Errorf("call_forwarding_type is required when call forwarding is enabled")
			herosentry.CaptureException(spanContext, err, "Call forwarding type validation failed")
			return err
		}
		switch org.CallForwardingType {
		case "PSTN":
			if org.PrimaryPhoneNumber == "" {
				err := fmt.Errorf("primary_phone_number is required for PSTN call forwarding")
				herosentry.CaptureException(spanContext, err, "PSTN call forwarding validation failed")
				return err
			}
			// Standardize and validate the phone number format
			standardizedNumber, err := utils.StandardizeUSPhoneNumber(org.PrimaryPhoneNumber)
			if err != nil {
				herosentry.CaptureException(spanContext, err, "Phone number standardization failed")
				return fmt.Errorf("invalid primary_phone_number format: %v", err)
			}
			org.PrimaryPhoneNumber = standardizedNumber
		case "SIP":
			if org.SipUri == "" {
				err := fmt.Errorf("sip_uri is required for SIP call forwarding")
				herosentry.CaptureException(spanContext, err, "SIP URI validation failed")
				return err
			}
			// Basic SIP URI format validation
			if !strings.HasPrefix(org.SipUri, "sip:") {
				err := fmt.Errorf("invalid sip_uri format: must start with 'sip:'")
				herosentry.CaptureException(spanContext, err, "SIP URI format validation failed")
				return err
			}
		default:
			err := fmt.Errorf("invalid call_forwarding_type: %s", org.CallForwardingType)
			herosentry.CaptureException(spanContext, err, "Invalid call forwarding type")
			return err
		}
	}

	// Get the template for this organization
	if org.TemplateId == "" {
		org.TemplateId = "local" // Default to full template if none specified
	}
	template, err := orgUseCase.templates.GetTemplate(org.TemplateId)
	if err != nil {
		herosentry.CaptureException(spanContext, err, fmt.Sprintf("Failed to get template %s", org.TemplateId))
		return fmt.Errorf("failed to get template: %v", err)
	}

	// Create the org in the repository
	createdOrg, err := orgUseCase.orgRepo.CreateOrg(spanContext, nil, org)
	if err != nil {
		herosentry.CaptureException(spanContext, err, "Failed to create organization in repository")
		return fmt.Errorf("failed to create org: %v", err)
	}
	span.SetTag("org.id", fmt.Sprintf("%d", createdOrg.Id))

	// Track resources for potential rollback
	var cognitoGroupCreated bool
	var twilioNumberClaimed bool
	var twilioQueueCreated bool
	var twilioAppCreated bool
	var zelloChannelCreated bool
	var TwilioQueueSid string

	// Function to handle rollback if needed
	rollback := func() {
		// Rollback in reverse order of creation
		if zelloChannelCreated {
			if err := orgUseCase.DeleteZelloOrgChannels(spanContext, createdOrg.Id); err != nil {
				log.Printf("Rollback error: failed to delete Zello channels: %v", err)
			}
		}

		if twilioAppCreated && org.TwimlAppSid != "" {
			client := twilio.NewRestClient()
			if err := client.Api.DeleteApplication(org.TwimlAppSid, &api.DeleteApplicationParams{}); err != nil {
				log.Printf("Rollback error: failed to delete Twilio application: %v", err)
			}
		}

		if twilioQueueCreated && TwilioQueueSid != "" {
			client := twilio.NewRestClient()
			params := &api.DeleteQueueParams{}
			if err := client.Api.DeleteQueue(TwilioQueueSid, params); err != nil {
				log.Printf("Rollback error: failed to delete Twilio queue: %v", err)
			}
		}

		if twilioNumberClaimed && org.TwilioNumberSid != "" {
			client := twilio.NewRestClient()
			params := &api.DeleteIncomingPhoneNumberParams{}
			if err := client.Api.DeleteIncomingPhoneNumber(org.TwilioNumberSid, params); err != nil {
				log.Printf("Rollback error: failed to release Twilio number: %v", err)
			}
		}

		if cognitoGroupCreated {
			_, err := orgUseCase.cognitoClient.DeleteGroup(spanContext, &cognitoidentityprovider.DeleteGroupInput{
				UserPoolId: aws.String(orgUseCase.userPoolId),
				GroupName:  aws.String("org:" + strconv.Itoa(int(createdOrg.Id))),
			})
			if err != nil {
				log.Printf("Rollback error: failed to delete Cognito group: %v", err)
			}
		}

		// Finally, delete the org from the database (this will cascade delete API users)
		if err := orgUseCase.orgRepo.DeleteOrg(spanContext, nil, createdOrg.Id); err != nil {
			log.Printf("Rollback error: failed to delete org: %v", err)
		}
	}

	// Create Cognito group if enabled in template
	if template.Features.Cognito {
		_, err = orgUseCase.cognitoClient.CreateGroup(spanContext, &cognitoidentityprovider.CreateGroupInput{
			UserPoolId:  aws.String(orgUseCase.userPoolId),
			GroupName:   aws.String("org:" + strconv.Itoa(int(createdOrg.Id))),
			Description: aws.String(fmt.Sprintf("Group for organization %s", org.Name)),
		})
		if err != nil {
			rollback()
			herosentry.CaptureException(spanContext, err, "Failed to create Cognito group")
			return fmt.Errorf("failed to create Cognito group: %v", err)
		}
		cognitoGroupCreated = true
	}

	// Set up Twilio if enabled in template
	if template.Features.Twilio {
		phoneNumber, err := orgUseCase.FetchAvailablePhoneNumberCountry(spanContext, org.PrimaryPhoneNumber)
		if err != nil {
			rollback()
			herosentry.CaptureException(spanContext, err, "Failed to fetch available phone number")
			return fmt.Errorf("failed to fetch available phone number: %v", err)
		}
		org.TwilioNumber = phoneNumber

		twilioNumberSid, err := orgUseCase.ClaimPhoneNumber(spanContext, phoneNumber)
		if err != nil {
			rollback()
			herosentry.CaptureException(spanContext, err, "Failed to claim phone number")
			return fmt.Errorf("failed to claim phone number: %v", err)
		}
		twilioNumberClaimed = true
		org.TwilioNumberSid = twilioNumberSid

		apiUser, apiPassword, err := orgUseCase.CreateOrgAPIUser(spanContext, org.Id)
		if err != nil {
			rollback()
			herosentry.CaptureException(spanContext, err, "Failed to create API user")
			return fmt.Errorf("failed to create API user: %v", err)
		}
		org.TwilioApiUserId = apiUser.Id

		appSid, err := orgUseCase.CreateTwilioApplication(spanContext, org.Name, apiUser.Id, apiPassword)
		if err != nil {
			rollback()
			herosentry.CaptureException(spanContext, err, "Failed to create Twilio application")
			return fmt.Errorf("failed to create Twilio application: %v", err)
		}
		twilioAppCreated = true
		org.TwimlAppSid = appSid

		// Connect the phone number to the Twilio application
		client := twilio.NewRestClient()
		params := &api.UpdateIncomingPhoneNumberParams{}
		params.SetVoiceApplicationSid(appSid)
		_, err = client.Api.UpdateIncomingPhoneNumber(org.TwilioNumberSid, params)
		if err != nil {
			rollback()
			herosentry.CaptureException(spanContext, err, "Failed to connect phone number to Twilio application")
			return fmt.Errorf("failed to connect phone number to Twilio application: %v", err)
		}

		// create a twilio queue
		queueSid, err := orgUseCase.CreateTwilioQueue(spanContext, org.Id, org.Name)
		if err != nil {
			rollback()
			herosentry.CaptureException(spanContext, err, "Failed to create Twilio queue")
			return fmt.Errorf("failed to create Twilio queue: %v", err)
		}
		twilioQueueCreated = true
		TwilioQueueSid = queueSid
	}

	// Set up Zello if enabled in template
	if template.Features.Zello {
		err = orgUseCase.CreateZelloChannel(spanContext, org.Id, "Everyone")
		if err != nil {
			rollback()
			herosentry.CaptureException(spanContext, err, "Failed to create Zello channel")
			return fmt.Errorf("failed to create Zello channel: %v", err)
		}
		zelloChannelCreated = true
	}

	// Update the org with any new credentials
	if err := orgUseCase.orgRepo.UpdateOrg(spanContext, nil, org); err != nil {
		herosentry.CaptureException(spanContext, err, "Failed to update org with credentials")
		return err
	}
	return nil
}

func generateRandomPassword() (string, error) {
	bytes := make([]byte, 16) // 16 bytes = 128 bits
	_, err := rand.Read(bytes)
	if err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

// CreateOrgAPIUser creates a new organization API user.
func (orgUseCase *OrgUseCase) CreateOrgAPIUser(ctx context.Context, orgID int32) (*orgs.CreateOrgAPIUserResponse, string, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "OrgUseCase.CreateOrgAPIUser")
	defer finish()
	span.SetTag("org.id", fmt.Sprintf("%d", orgID))

	tokenOrgId := cmncontext.GetOrgId(spanContext)
	// allow the meta org to specify the orgId
	var orgIdToUse int32
	if tokenOrgId == -1 {
		orgIdToUse = orgID
	} else {
		orgIdToUse = tokenOrgId
	}

	password, err := generateRandomPassword()
	if err != nil {
		herosentry.CaptureException(spanContext, err, "Failed to generate password")
		return nil, "", fmt.Errorf("failed to generate password: %v", err)
	}

	input := &kms.EncryptInput{
		KeyId:     aws.String(orgUseCase.kmsKeyArn),
		Plaintext: []byte(password),
	}
	result, err := orgUseCase.kmsClient.Encrypt(spanContext, input)
	if err != nil {
		herosentry.CaptureException(spanContext, err, "Failed to encrypt password with KMS")
		return nil, "", fmt.Errorf("failed to encrypt data: %v", err)
	}

	encryptedPassword := base64.StdEncoding.EncodeToString(result.CiphertextBlob)
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		herosentry.CaptureException(spanContext, err, "Failed to hash password")
		return nil, "", fmt.Errorf("failed to hash password: %v", err)
	}
	hashedPasswordString := string(hashedPassword)

	userID, err := orgUseCase.orgRepo.CreateOrgAPIUser(spanContext, nil, orgIdToUse, encryptedPassword, hashedPasswordString)
	if err != nil {
		herosentry.CaptureException(spanContext, err, "Failed to create org API user in repository")
		return nil, "", err
	}

	return &orgs.CreateOrgAPIUserResponse{
		Id:                userID,
		EncryptedPassword: encryptedPassword,
		HashedPassword:    hashedPasswordString,
	}, password, nil
}

// GetOrg retrieves an organization by its ID.
func (orgUseCase *OrgUseCase) GetOrg(ctx context.Context, orgID int32) (*orgs.Org, error) {
	spanContext, _, finish := herosentry.StartSpan(ctx, "OrgUseCase.GetOrg")
	defer finish()
	return orgUseCase.orgRepo.GetOrg(spanContext, nil, orgID)
}

// DeleteOrg deletes an organization and its associated Twilio resources.
func (orgUseCase *OrgUseCase) DeleteOrg(ctx context.Context, orgID int32) error {
	spanContext, span, finish := herosentry.StartSpan(ctx, "OrgUseCase.DeleteOrg")
	defer finish()
	span.SetTag("org.id", fmt.Sprintf("%d", orgID))

	// First get the org to access Twilio credentials
	org, err := orgUseCase.orgRepo.GetOrg(spanContext, nil, orgID)
	if err != nil {
		herosentry.CaptureException(spanContext, err, "Failed to get org for deletion")
		return fmt.Errorf("failed to get org: %v", err)
	}

	accountSid := os.Getenv("TWILIO_ACCOUNT_SID")
	if accountSid == "" {
		err := fmt.Errorf("TWILIO_ACCOUNT_SID is not set")
		herosentry.CaptureException(spanContext, err, "Missing Twilio account SID")
		return err
	}

	twilioQueueSid, err := orgUseCase.orgRepo.GetTwilioQueueSid(spanContext, nil, orgID)
	if err != nil {
		herosentry.CaptureException(spanContext, err, "Failed to get Twilio queue SID")
		return fmt.Errorf("failed to get Twilio queue sid: %v", err)
	}

	// Delete the org's twilio queue if it exists
	if twilioQueueSid != "" {
		client := twilio.NewRestClient()
		err := client.Api.DeleteQueue(twilioQueueSid, &api.DeleteQueueParams{})
		if err != nil {
			herosentry.CaptureException(spanContext, err, fmt.Sprintf("Failed to delete Twilio queue %s", twilioQueueSid))
			return fmt.Errorf("failed to delete Twilio queue %s: %v", twilioQueueSid, err)
		}
		err = orgUseCase.orgRepo.DeleteTwilioQueueConfiguration(spanContext, nil, orgID)
		if err != nil {
			herosentry.CaptureException(spanContext, err, "Failed to delete Twilio queue configuration")
			return fmt.Errorf("failed to delete Twilio queue configuration: %v", err)
		}
	}

	// Delete Twilio application if it exists
	if org.TwimlAppSid != "" {
		client := twilio.NewRestClient()
		err := client.Api.DeleteApplication(org.TwimlAppSid, &api.DeleteApplicationParams{})
		if err != nil {
			herosentry.CaptureException(spanContext, err, fmt.Sprintf("Failed to delete Twilio application %s", org.TwimlAppSid))
			return fmt.Errorf("failed to delete Twilio application %s: %v", org.TwimlAppSid, err)
		}
	}

	// Release Twilio phone number if it exists
	if org.TwilioNumber != "" {
		client := twilio.NewRestClient()
		params := &api.DeleteIncomingPhoneNumberParams{}
		err := client.Api.DeleteIncomingPhoneNumber(org.TwilioNumberSid, params)
		if err != nil {
			herosentry.CaptureException(spanContext, err, fmt.Sprintf("Failed to release Twilio number %s", org.TwilioNumber))
			return fmt.Errorf("failed to release Twilio number %s: %v", org.TwilioNumber, err)
		}
	}

	// delete the Zello channels
	err = orgUseCase.DeleteZelloOrgChannels(spanContext, orgID)
	if err != nil {
		herosentry.CaptureException(spanContext, err, "Failed to delete Zello channels")
		return fmt.Errorf("failed to delete Zello channels: %v", err)
	}

	// delete all the users in the org
	err = orgUseCase.DeleteOrgAssetsAndCognitoUsers(spanContext, orgID)
	if err != nil {
		herosentry.CaptureException(spanContext, err, "Failed to delete org users")
		return fmt.Errorf("failed to delete org users: %v", err)
	}

	// Delete Cognito group
	_, err = orgUseCase.cognitoClient.DeleteGroup(spanContext, &cognitoidentityprovider.DeleteGroupInput{
		UserPoolId: aws.String(orgUseCase.userPoolId),
		GroupName:  aws.String("org:" + strconv.Itoa(int(orgID))),
	})
	if err != nil {
		herosentry.CaptureException(spanContext, err, "Failed to delete Cognito group")
		return fmt.Errorf("failed to delete Cognito group: %v", err)
	}

	// Finally delete the org from our database
	if err := orgUseCase.orgRepo.DeleteOrg(spanContext, nil, orgID); err != nil {
		herosentry.CaptureException(spanContext, err, "Failed to delete org from database")
		return err
	}
	return nil
}

// ListOrgs returns a list of all organizations.
func (orgUseCase *OrgUseCase) ListOrgs(ctx context.Context) ([]*orgs.Org, error) {
	spanContext, _, finish := herosentry.StartSpan(ctx, "OrgUseCase.ListOrgs")
	defer finish()
	return orgUseCase.orgRepo.ListOrgs(spanContext, nil)
}

// ValidateOrgCreds validates organization credentials.
func (orgUseCase *OrgUseCase) ValidateOrgCreds(ctx context.Context, username, password string) (bool, *orgs.OrgApiUser, error) {
	spanContext, _, finish := herosentry.StartSpan(ctx, "OrgUseCase.ValidateOrgCreds")
	defer finish()

	if username == "" || password == "" {
		err := fmt.Errorf("username and password are required")
		herosentry.CaptureException(spanContext, err, "Missing credentials")
		return false, nil, err
	}

	orgAPIUser, err := orgUseCase.orgRepo.GetOrgAPIUser(spanContext, nil, username)
	if err != nil {
		// Log the specific error but return a generic message
		herosentry.CaptureException(spanContext, err, "Failed to fetch credentials")
		return false, nil, fmt.Errorf("authentication failed")
	}

	err = bcrypt.CompareHashAndPassword([]byte(orgAPIUser.HashedPassword), []byte(password))
	if err != nil {
		if err == bcrypt.ErrMismatchedHashAndPassword {
			// Wrong password - don't expose this detail in the error
			return false, nil, fmt.Errorf("authentication failed")
		}
		// Unexpected error during comparison
		return false, nil, fmt.Errorf("authentication failed")
	}

	return true, orgAPIUser, nil
}

func (orgUseCase *OrgUseCase) GetOrgAPIUserPrivateById(ctx context.Context, userId string) (*orgs.OrgApiUserPrivate, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "OrgUseCase.GetOrgAPIUserPrivateById")
	defer finish()
	span.SetTag("user.id", userId)

	if userId == "" {
		err := fmt.Errorf("user ID is required")
		herosentry.CaptureException(spanContext, err, "Missing user ID")
		return nil, err
	}

	orgAPIUser, err := orgUseCase.orgRepo.GetOrgAPIUserById(spanContext, nil, userId)
	if err != nil {
		herosentry.CaptureException(spanContext, err, "Failed to get org API user")
		return nil, fmt.Errorf("error getting org API user: %v", err)
	}

	// decrypt the password
	ciphertextBlob, err := base64.StdEncoding.DecodeString(orgAPIUser.EncryptedPassword)
	if err != nil {
		herosentry.CaptureException(spanContext, err, "Failed to decode encrypted password")
		return nil, fmt.Errorf("failed to decode base64 encrypted password: %v", err)
	}
	input := &kms.DecryptInput{
		CiphertextBlob: ciphertextBlob,
		KeyId:          aws.String(orgUseCase.kmsKeyArn),
	}
	decryptedPassword, err := orgUseCase.kmsClient.Decrypt(spanContext, input)
	if err != nil {
		herosentry.CaptureException(spanContext, err, "Failed to decrypt password with KMS")
		return nil, fmt.Errorf("error decrypting password: %v", err)
	}
	privateOrgAPIUser := orgs.OrgApiUserPrivate{
		Id:          orgAPIUser.Id,
		RawPassword: string(decryptedPassword.Plaintext),
	}
	return &privateOrgAPIUser, nil
}

// UpdateOrg updates an organization's fields.
func (orgUseCase *OrgUseCase) UpdateOrg(ctx context.Context, updatedOrg *orgs.Org) (*orgs.Org, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "OrgUseCase.UpdateOrg")
	defer finish()
	span.SetTag("org.id", fmt.Sprintf("%d", updatedOrg.Id))

	existingOrg, err := orgUseCase.orgRepo.GetOrg(spanContext, nil, updatedOrg.Id)
	if err != nil {
		herosentry.CaptureException(spanContext, err, "Failed to get existing org for update")
		return nil, err
	}

	// Validate call forwarding settings
	if updatedOrg.IsCallForwardingEnabled {
		if updatedOrg.CallForwardingType == "" {
			err := fmt.Errorf("call_forwarding_type is required when call forwarding is enabled")
			herosentry.CaptureException(spanContext, err, "Call forwarding type validation failed")
			return nil, err
		}
		switch updatedOrg.CallForwardingType {
		case "PSTN":
			if updatedOrg.PrimaryPhoneNumber == "" {
				err := fmt.Errorf("primary_phone_number is required for PSTN call forwarding")
				herosentry.CaptureException(spanContext, err, "PSTN phone number validation failed")
				return nil, err
			}
			// Standardize and validate the phone number format
			standardizedNumber, err := utils.StandardizeUSPhoneNumber(updatedOrg.PrimaryPhoneNumber)
			if err != nil {
				herosentry.CaptureException(spanContext, err, "Phone number standardization failed")
				return nil, fmt.Errorf("invalid primary_phone_number format: %v", err)
			}
			updatedOrg.PrimaryPhoneNumber = standardizedNumber
		case "SIP":
			if updatedOrg.SipUri == "" {
				err := fmt.Errorf("sip_uri is required for SIP call forwarding")
				herosentry.CaptureException(spanContext, err, "SIP URI validation failed")
				return nil, err
			}
			// Basic SIP URI format validation
			if !strings.HasPrefix(updatedOrg.SipUri, "sip:") {
				err := fmt.Errorf("invalid sip_uri format: must start with 'sip:'")
				herosentry.CaptureException(spanContext, err, "SIP URI format validation failed")
				return nil, err
			}
		default:
			err := fmt.Errorf("invalid call_forwarding_type: %s", updatedOrg.CallForwardingType)
			herosentry.CaptureException(spanContext, err, "Invalid call forwarding type")
			return nil, err
		}
	}

	// Update only fields that are provided
	if updatedOrg.Name != "" {
		existingOrg.Name = updatedOrg.Name
	}
	if len(updatedOrg.Domains) > 0 {
		existingOrg.Domains = updatedOrg.Domains
	}
	if updatedOrg.TwimlAppSid != "" {
		existingOrg.TwimlAppSid = updatedOrg.TwimlAppSid
	}
	if updatedOrg.TwilioNumber != "" {
		existingOrg.TwilioNumber = updatedOrg.TwilioNumber
	}
	if updatedOrg.ServiceType != orgs.ServiceType_SERVICE_TYPE_UNSPECIFIED {
		existingOrg.ServiceType = updatedOrg.ServiceType
	}
	if updatedOrg.IsCallForwardingEnabled != existingOrg.IsCallForwardingEnabled {
		existingOrg.IsCallForwardingEnabled = updatedOrg.IsCallForwardingEnabled
	}
	if updatedOrg.CallForwardingType != "" {
		existingOrg.CallForwardingType = updatedOrg.CallForwardingType
	}
	if updatedOrg.PrimaryPhoneNumber != "" {
		existingOrg.PrimaryPhoneNumber = updatedOrg.PrimaryPhoneNumber
	}
	if updatedOrg.SipUri != "" {
		existingOrg.SipUri = updatedOrg.SipUri
	}
	if updatedOrg.TwilioApiUserId != "" {
		existingOrg.TwilioApiUserId = updatedOrg.TwilioApiUserId
	}

	// Always update the update time
	existingOrg.UpdatedAt = timestamppb.New(time.Now())

	// Persist the updated org
	if err := orgUseCase.orgRepo.UpdateOrg(spanContext, nil, existingOrg); err != nil {
		herosentry.CaptureException(spanContext, err, "Failed to update org in repository")
		return nil, err
	}

	return existingOrg, nil
}

// GetZelloChannels returns a list of all Zello channels for an organization.
func (orgUseCase *OrgUseCase) GetZelloChannels(ctx context.Context) ([]*orgs.ZelloChannel, error) {
	spanContext, _, finish := herosentry.StartSpan(ctx, "OrgUseCase.GetZelloChannels")
	defer finish()
	return orgUseCase.orgRepo.GetZelloChannels(spanContext, nil)
}

// CreateZelloChannel creates a new Zello channel for an organization.
func (orgUseCase *OrgUseCase) CreateZelloChannel(ctx context.Context, orgID int32, channelName string) error {
	spanContext, span, finish := herosentry.StartSpan(ctx, "OrgUseCase.CreateZelloChannel")
	defer finish()
	span.SetTag("org.id", fmt.Sprintf("%d", orgID))
	span.SetTag("channel.name", channelName)

	zelloClient, err := zello.NewZelloClient()
	if err != nil {
		herosentry.CaptureException(spanContext, err, "Failed to create Zello client")
		return fmt.Errorf("failed to create Zello client: %v", err)
	}

	namespacedChannelName := strconv.Itoa(int(orgID)) + "_" + channelName
	_, err = zelloClient.AddChannel(namespacedChannelName, true, false, []string{})
	if err != nil {
		herosentry.CaptureException(spanContext, err, fmt.Sprintf("Failed to create Zello channel %s", namespacedChannelName))
		return fmt.Errorf("failed to create Zello channel: %v", err)
	}

	zelloChannel := &orgs.ZelloChannel{
		OrgId:          orgID,
		ZelloChannelId: namespacedChannelName,
		DisplayName:    channelName,
	}

	if err := orgUseCase.orgRepo.CreateZelloChannel(spanContext, nil, orgID, zelloChannel); err != nil {
		herosentry.CaptureException(spanContext, err, "Failed to store Zello channel in repository")
		return err
	}
	return nil
}

// DeleteZelloOrgChannels deletes all Zello channels for an organization.
func (orgUseCase *OrgUseCase) DeleteZelloOrgChannels(ctx context.Context, orgID int32) error {
	spanContext, span, finish := herosentry.StartSpan(ctx, "OrgUseCase.DeleteZelloOrgChannels")
	defer finish()
	span.SetTag("org.id", fmt.Sprintf("%d", orgID))

	zelloClient, err := zello.NewZelloClient()
	if err != nil {
		herosentry.CaptureException(spanContext, err, "Failed to create Zello client")
		return fmt.Errorf("failed to create Zello client: %v", err)
	}

	orgZelloChannels, err := orgUseCase.orgRepo.GetZelloChannels(spanContext, nil)
	if err != nil {
		herosentry.CaptureException(spanContext, err, "Failed to get Zello channels")
		return fmt.Errorf("failed to get Zello channels: %v", err)
	}
	orgZelloChannelIds := []string{}
	for _, zelloChannel := range orgZelloChannels {
		orgZelloChannelIds = append(orgZelloChannelIds, zelloChannel.ZelloChannelId)
	}
	_, err = zelloClient.DeleteChannel(orgZelloChannelIds)
	if err != nil {
		herosentry.CaptureException(spanContext, err, fmt.Sprintf("Failed to delete %d Zello channels", len(orgZelloChannelIds)))
		return fmt.Errorf("failed to delete Zello channels: %v", err)
	}

	if err := orgUseCase.orgRepo.DeleteZelloChannel(spanContext, nil, orgID, orgZelloChannelIds); err != nil {
		herosentry.CaptureException(spanContext, err, "Failed to delete Zello channels from repository")
		return err
	}
	return nil
}

// FetchAvailablePhoneNumberCountry fetches available phone number information for a specific country.
func (orgUseCase *OrgUseCase) FetchAvailablePhoneNumberCountry(ctx context.Context, phoneNumber string) (string, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "OrgUseCase.FetchAvailablePhoneNumberCountry")
	defer finish()

	client := twilio.NewRestClient()

	localParams := &api.ListAvailablePhoneNumberLocalParams{
		NearNumber: aws.String(phoneNumber),
	}
	phoneNumbers := []string{}
	localResp, err := client.Api.ListAvailablePhoneNumberLocal(UsaCountryCode, localParams)
	if err != nil {
		herosentry.CaptureException(spanContext, err, "Failed to fetch available phone numbers from Twilio")
		log.Printf("Warning: failed to fetch local numbers: %v", err)
		return "", fmt.Errorf("failed to fetch local numbers: %v", err)
	} else {
		log.Printf("Found %d local numbers", len(localResp))
		span.SetTag("phone.available_count", fmt.Sprintf("%d", len(localResp)))
		for _, number := range localResp {
			if number.PhoneNumber != nil {
				log.Printf("Local number: %s", *number.PhoneNumber)
				phoneNumbers = append(phoneNumbers, *number.PhoneNumber)
			}
		}
	}

	bestNumber := util.MostRecognizableNumber(phoneNumbers)
	if bestNumber == "" {
		err := fmt.Errorf("no suitable phone numbers found")
		herosentry.CaptureException(spanContext, err, "No suitable phone numbers available")
		return "", err
	}

	log.Printf("Best number: %s with score %d", bestNumber, util.ScoreNumber(bestNumber))
	log.Printf("All numbers: %v", phoneNumbers)

	return bestNumber, nil
}

// ClaimPhoneNumber claims a specific phone number using Twilio.
func (orgUseCase *OrgUseCase) ClaimPhoneNumber(ctx context.Context, phoneNumber string) (string, error) {
	spanContext, _, finish := herosentry.StartSpan(ctx, "OrgUseCase.ClaimPhoneNumber")
	defer finish()

	client := twilio.NewRestClient()

	params := &api.CreateIncomingPhoneNumberParams{}
	params.SetPhoneNumber(phoneNumber)

	resp, err := client.Api.CreateIncomingPhoneNumber(params)
	if err != nil {
		herosentry.CaptureException(spanContext, err, fmt.Sprintf("Failed to claim phone number %s", phoneNumber))
		return "", fmt.Errorf("failed to claim phone number: %v", err)
	}

	if resp.AccountSid == nil {
		err := fmt.Errorf("failed to claim phone number: no account SID returned")
		herosentry.CaptureException(spanContext, err, "Twilio returned no account SID")
		return "", err
	}

	log.Printf("Successfully claimed phone number %s with account SID %s", phoneNumber, *resp.AccountSid)
	return *resp.Sid, nil
}

// CreateTwilioQueue creates a new Twilio queue for an organization
func (orgUseCase *OrgUseCase) CreateTwilioQueue(ctx context.Context, orgID int32, friendlyName string) (string, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "OrgUseCase.CreateTwilioQueue")
	defer finish()
	span.SetTag("org.id", fmt.Sprintf("%d", orgID))
	span.SetTag("queue.friendly_name", friendlyName)

	if err := ValidateFriendlyName(friendlyName); err != nil {
		herosentry.CaptureException(spanContext, err, "Invalid queue friendly name")
		return "", fmt.Errorf("invalid queue friendly name: %w", err)
	}

	client := twilio.NewRestClient()

	// Create a queue with the organization's name
	params := &api.CreateQueueParams{
		FriendlyName: &friendlyName,
	}

	queue, err := client.Api.CreateQueue(params)
	if err != nil {
		herosentry.CaptureException(spanContext, err, "Failed to create Twilio queue")
		return "", fmt.Errorf("failed to create Twilio queue: %w", err)
	}

	// Store the queue SID in the twilio_queue_configurations table
	description := fmt.Sprintf("Queue for organization %d", orgID)
	err = orgUseCase.orgRepo.StoreTwilioQueueConfiguration(spanContext, nil, orgID, friendlyName, *queue.Sid, description)
	if err != nil {
		herosentry.CaptureException(spanContext, err, "Failed to store Twilio queue configuration")
		// Try to clean up the created queue if we can't store its SID
		deleteParams := &api.DeleteQueueParams{}
		if deleteErr := client.Api.DeleteQueue(*queue.Sid, deleteParams); deleteErr != nil {
			// Log the delete error but return the original error
			log.Printf("Failed to delete Twilio queue %s after storage error: %v", *queue.Sid, deleteErr)
		}
		return "", err
	}

	return *queue.Sid, nil
}

// CreateTwilioApplication creates a new Twilio application for voice calls.
func (orgUseCase *OrgUseCase) CreateTwilioApplication(ctx context.Context, friendlyName string, username string, password string) (string, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "OrgUseCase.CreateTwilioApplication")
	defer finish()
	span.SetTag("app.friendly_name", friendlyName)

	client := twilio.NewRestClient()

	params := &api.CreateApplicationParams{}
	params.SetVoiceMethod("POST")
	params.SetVoiceUrl("https://" + username + ":" + password + "@" + orgUseCase.commsServerPublicDomain + "/hero.communications.v1.TwilioWebhookService/voice")
	params.SetStatusCallback("https://" + username + ":" + password + "@" + orgUseCase.commsServerPublicDomain + "/hero.communications.v1.TwilioWebhookService/callstatus")
	params.SetFriendlyName("Voice Channel - " + friendlyName)

	resp, err := client.Api.CreateApplication(params)
	if err != nil {
		herosentry.CaptureException(spanContext, err, fmt.Sprintf("Failed to create Twilio application %s", friendlyName))
		return "", fmt.Errorf("failed to create Twilio application: %v", err)
	}

	if resp.AccountSid == nil {
		err := fmt.Errorf("failed to create Twilio application: no account SID returned")
		herosentry.CaptureException(spanContext, err, "Twilio returned no account SID for application")
		return "", err
	}

	log.Printf("Successfully created Twilio application %s with account SID %s", friendlyName, *resp.AccountSid)
	return *resp.Sid, nil
}

// DeleteOrgAssetsAndCognitoUsers lists all assets for an organization and deletes any Cognito users associated with those assets.
func (orgUseCase *OrgUseCase) DeleteOrgAssetsAndCognitoUsers(ctx context.Context, orgID int32) error {
	spanContext, span, finish := herosentry.StartSpan(ctx, "OrgUseCase.DeleteOrgAssetsAndCognitoUsers")
	defer finish()
	span.SetTag("org.id", fmt.Sprintf("%d", orgID))

	// First get the org to verify it exists
	_, err := orgUseCase.orgRepo.GetOrg(spanContext, nil, orgID)
	if err != nil {
		herosentry.CaptureException(spanContext, err, "Failed to get org for asset deletion")
		return fmt.Errorf("failed to get org: %v", err)
	}

	// Get assets with Cognito JWT sub from the repository
	assets, err := orgUseCase.orgRepo.GetOrgAssetsWithCognitoJwtSub(spanContext, nil, orgID)
	if err != nil {
		herosentry.CaptureException(spanContext, err, "Failed to get assets with Cognito JWT sub")
		return fmt.Errorf("failed to get assets: %v", err)
	}

	// Create a Zello client for deleting Zello users
	zelloClient, err := zello.NewZelloClient()
	if err != nil {
		return fmt.Errorf("failed to create Zello client: %v", err)
	}
	defer func() {
		if err := zelloClient.Logout(); err != nil {
			log.Printf("could not logout Zello client: %v", err)
		}
	}()

	// Delete Cognito users and Zello users for assets
	for _, asset := range assets {
		// Delete the Cognito user if it exists
		if asset.CognitoJwtSub != "" {
			_, err = orgUseCase.cognitoClient.AdminDeleteUser(spanContext, &cognitoidentityprovider.AdminDeleteUserInput{
				UserPoolId: aws.String(orgUseCase.userPoolId),
				Username:   aws.String(asset.CognitoJwtSub),
			})
			if err != nil {
				// Log the error but continue with other assets
				log.Printf("failed to delete Cognito user %s for asset %s: %v", asset.CognitoJwtSub, asset.ID, err)
			} else {
				log.Printf("successfully deleted Cognito user %s for asset %s", asset.CognitoJwtSub, asset.ID)
			}
		}

		// Get Zello credentials for the asset
		zelloCreds, err := orgUseCase.orgRepo.GetZelloCreds(spanContext, nil, asset.ID)
		if err != nil {
			// Log the error but continue with other assets
			log.Printf("failed to get Zello credentials for asset %s: %v", asset.ID, err)
			continue
		}

		// Delete the Zello user
		if zelloCreds != nil && zelloCreds.Username != "" {
			_, err = zelloClient.DeleteUser([]string{zelloCreds.Username})
			if err != nil {
				// Log the error but continue with other assets
				log.Printf("failed to delete Zello user %s for asset %s: %v", zelloCreds.Username, asset.ID, err)
			} else {
				log.Printf("successfully deleted Zello user %s for asset %s", zelloCreds.Username, asset.ID)
			}

			// Delete the Zello credentials from our database
			err = orgUseCase.orgRepo.DeleteZelloCreds(spanContext, nil, asset.ID)
			if err != nil {
				// Log the error but continue with other assets
				log.Printf("failed to delete Zello credentials for asset %s: %v", asset.ID, err)
			}
		}
	}

	return nil
}

// InsertOrgQueue inserts a new organization queue.
func (orgUseCase *OrgUseCase) InsertOrgQueue(ctx context.Context, req *orgs.InsertOrgQueueRequest) (*orgs.OrgQueue, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "OrgUseCase.InsertOrgQueue")
	defer finish()

	// Add any business logic/validation here before calling the repository.
	// For example, validating req.FriendlyName, req.TwilioQueueSid, etc.
	// For now, it's a direct pass-through.
	var orgIDToUse int32
	tokenOrgId := cmncontext.GetOrgId(spanContext)
	// only allow the meta org to insert queues for other orgs
	if tokenOrgId == -1 {
		orgIDToUse = int32(req.OrgId)
	} else {
		orgIDToUse = tokenOrgId
	}
	req.OrgId = orgIDToUse
	span.SetTag("org.id", fmt.Sprintf("%d", orgIDToUse))
	span.SetTag("queue.friendly_name", req.FriendlyName)

	result, err := orgUseCase.orgRepo.InsertOrgQueue(spanContext, nil, req)
	if err != nil {
		herosentry.CaptureException(spanContext, err, "Failed to insert org queue")
		return nil, err
	}
	return result, nil
}

// AddUserToCognitoGroup adds a user to a Cognito group.
// AddUserToCognitoGroup is a special case, potentially very powerful.
// Its intent is to add a user as a guest to a different org, and attach this information
// directly to the cognito token.

// Normally, a user's role primarily determines their permissions.

// But a role in org X cannot, in general, access org Y and should therefore not be able to access this endpoint.

// So as an additional security measure, we run an additional check to confirm the user already has a role in the org
// that they are trying to access.

// This role assignment would necesssarily have to be done by a member of the host org, thus keeping the host org
// in control of the guest's entry.

// This role will also serve to control access, when they make requests as a guest.
func (orgUseCase *OrgUseCase) TurnOnGuestMode(ctx context.Context, orgId int32) error {
	spanContext, span, finish := herosentry.StartSpan(ctx, "OrgUseCase.TurnOnGuestMode")
	defer finish()
	span.SetTag("org.id", fmt.Sprintf("%d", orgId))

	if orgId == 0 {
		err := fmt.Errorf("org id is required")
		herosentry.CaptureException(spanContext, err, "Missing org ID")
		return err
	}

	// get username from cmncontext
	username := cmncontext.GetUsername(spanContext)
	// orgId := cmncontext.GetOrgId(ctx)

	groupName := "org:" + strconv.Itoa(int(orgId)) + ":guest"

	// check if user has role in org
	request := connect.NewRequest(&permspb.GetUserRolesRequest{
		OrgId:  orgId,
		UserId: username,
	})
	resp, err := orgUseCase.permsClient.GetUserRoles(spanContext, request)
	if err != nil {
		herosentry.CaptureException(spanContext, err, "Failed to get user roles")
		return fmt.Errorf("failed to get role: %v", err)
	}
	if len(resp.Msg.Roles) == 0 {
		err := fmt.Errorf("user does not have a role in org")
		herosentry.CaptureException(spanContext, err, "User has no role in target org")
		return err
	}

	// drop cognito prefix
	username = strings.TrimPrefix(username, "cognito:")
	// Add user to the group
	_, err = orgUseCase.cognitoClient.AdminAddUserToGroup(spanContext, &cognitoidentityprovider.AdminAddUserToGroupInput{
		UserPoolId: aws.String(orgUseCase.userPoolId),
		Username:   aws.String(username),
		GroupName:  aws.String(groupName),
	})
	if err != nil {
		herosentry.CaptureException(spanContext, err, "Failed to add user to Cognito guest group")
		return fmt.Errorf("failed to add user to Cognito group: %v", err)
	}

	return nil
}

// this one is simpler. We are less concerned when privileges are being dropped.
func (orgUseCase *OrgUseCase) TurnOffGuestMode(ctx context.Context, orgId int32) error {
	spanContext, span, finish := herosentry.StartSpan(ctx, "OrgUseCase.TurnOffGuestMode")
	defer finish()
	span.SetTag("org.id", fmt.Sprintf("%d", orgId))

	username := cmncontext.GetUsername(spanContext)
	// drop cognito prefix
	username = strings.TrimPrefix(username, "cognito:")

	groupName := "org:" + strconv.Itoa(int(orgId)) + ":guest"

	// Remove user from the group
	_, err := orgUseCase.cognitoClient.AdminRemoveUserFromGroup(spanContext, &cognitoidentityprovider.AdminRemoveUserFromGroupInput{
		UserPoolId: aws.String(orgUseCase.userPoolId),
		Username:   aws.String(username),
		GroupName:  aws.String(groupName),
	})
	if err != nil {
		herosentry.CaptureException(spanContext, err, "Failed to remove user from Cognito guest group")
		return fmt.Errorf("failed to remove user from Cognito group: %v", err)
	}

	return nil
}

// CreateCognitoUser creates a new user in Cognito for an organization.
func (orgUseCase *OrgUseCase) CreateCognitoUser(ctx context.Context, orgID int32, username, email, password string) (string, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "OrgUseCase.CreateCognitoUser")
	defer finish()
	span.SetTag("org.id", fmt.Sprintf("%d", orgID))

	if username == "" || email == "" || password == "" {
		err := fmt.Errorf("username, email, and password are required")
		herosentry.CaptureException(spanContext, err, "Missing required user fields")
		return "", err
	}

	// Validate email format
	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	if !emailRegex.MatchString(email) {
		err := fmt.Errorf("invalid email format")
		herosentry.CaptureException(spanContext, err, "Email validation failed")
		return "", err
	}

	// Validate password strength
	if len(password) < 8 {
		err := fmt.Errorf("password must be at least 8 characters long")
		herosentry.CaptureException(spanContext, err, "Password validation failed")
		return "", err
	}

	// Create the user in Cognito
	createUserOutput, err := orgUseCase.cognitoClient.AdminCreateUser(spanContext, &cognitoidentityprovider.AdminCreateUserInput{
		UserPoolId: aws.String(orgUseCase.userPoolId),
		Username:   aws.String(username),
		UserAttributes: []types.AttributeType{
			{
				Name:  aws.String("email"),
				Value: aws.String(email),
			},
			{
				Name:  aws.String("email_verified"),
				Value: aws.String("true"),
			},
		},
		MessageAction: "SUPPRESS",
	})
	if err != nil {
		herosentry.CaptureException(spanContext, err, "Failed to create Cognito user")
		return "", fmt.Errorf("failed to create Cognito user: %v", err)
	}

	// Get the Cognito sub (user ID)
	var cognitoSub string
	for _, attr := range createUserOutput.User.Attributes {
		if *attr.Name == "sub" {
			cognitoSub = *attr.Value
			break
		}
	}
	if cognitoSub == "" {
		// If setting password fails, try to clean up by deleting the user
		_, deleteErr := orgUseCase.cognitoClient.AdminDeleteUser(spanContext, &cognitoidentityprovider.AdminDeleteUserInput{
			UserPoolId: aws.String(orgUseCase.userPoolId),
			Username:   aws.String(username),
		})
		if deleteErr != nil {
			log.Printf("Failed to delete Cognito user after sub not found: %v", deleteErr)
		}
		err := fmt.Errorf("failed to get Cognito sub for user")
		herosentry.CaptureException(spanContext, err, "Failed to get Cognito sub")
		return "", err
	}

	// Set the user's password
	_, err = orgUseCase.cognitoClient.AdminSetUserPassword(spanContext, &cognitoidentityprovider.AdminSetUserPasswordInput{
		UserPoolId: aws.String(orgUseCase.userPoolId),
		Username:   aws.String(username),
		Password:   aws.String(password),
		Permanent:  true,
	})
	if err != nil {
		// If setting password fails, try to clean up by deleting the user
		_, deleteErr := orgUseCase.cognitoClient.AdminDeleteUser(spanContext, &cognitoidentityprovider.AdminDeleteUserInput{
			UserPoolId: aws.String(orgUseCase.userPoolId),
			Username:   aws.String(username),
		})
		if deleteErr != nil {
			log.Printf("Failed to delete Cognito user after password set failure: %v", deleteErr)
		}
		herosentry.CaptureException(spanContext, err, "Failed to set user password")
		return "", fmt.Errorf("failed to set user password: %v", err)
	}

	// Add user to the organization's group
	groupName := "org:" + strconv.Itoa(int(orgID))
	_, err = orgUseCase.cognitoClient.AdminAddUserToGroup(spanContext, &cognitoidentityprovider.AdminAddUserToGroupInput{
		UserPoolId: aws.String(orgUseCase.userPoolId),
		Username:   aws.String(username),
		GroupName:  aws.String(groupName),
	})
	if err != nil {
		// If adding to group fails, try to clean up by deleting the user
		_, deleteErr := orgUseCase.cognitoClient.AdminDeleteUser(spanContext, &cognitoidentityprovider.AdminDeleteUserInput{
			UserPoolId: aws.String(orgUseCase.userPoolId),
			Username:   aws.String(username),
		})
		if deleteErr != nil {
			log.Printf("Failed to delete Cognito user after group assignment failure: %v", deleteErr)
		}
		herosentry.CaptureException(spanContext, err, "Failed to add user to organization group")
		return "", fmt.Errorf("failed to add user to organization group: %v", err)
	}

	return cognitoSub, nil
}

// AddToContactBook creates a new contact record in the organization's contact book
func (orgUseCase *OrgUseCase) AddToContactBook(ctx context.Context, orgID int32, name, phone string) (*orgs.ContactRecord, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "OrgUseCase.AddToContactBook")
	defer finish()
	span.SetTag("org.id", fmt.Sprintf("%d", orgID))

	if name == "" {
		err := fmt.Errorf("contact name is required")
		herosentry.CaptureException(spanContext, err, "Missing contact name")
		return nil, err
	}
	if phone == "" {
		err := fmt.Errorf("contact phone is required")
		herosentry.CaptureException(spanContext, err, "Missing contact phone")
		return nil, err
	}

	// Standardize and validate the phone number format
	standardizedPhone, err := utils.StandardizeUSPhoneNumber(phone)
	if err != nil {
		herosentry.CaptureException(spanContext, err, "Invalid phone number format")
		return nil, fmt.Errorf("invalid phone number format: %v", err)
	}

	// Validate the organization exists
	_, err = orgUseCase.orgRepo.GetOrg(spanContext, nil, orgID)
	if err != nil {
		herosentry.CaptureException(spanContext, err, "Organization not found")
		return nil, fmt.Errorf("organization not found: %v", err)
	}

	// Generate unique ID for the contact
	contactID := uuid.New().String()

	// Create the contact
	contact := &orgs.ContactRecord{
		Id:        contactID,
		OrgId:     orgID,
		Name:      name,
		Phone:     standardizedPhone,
		CreatedAt: timestamppb.New(time.Now()),
		UpdatedAt: timestamppb.New(time.Now()),
	}

	err = orgUseCase.orgRepo.AddToContactBook(spanContext, nil, contact)
	if err != nil {
		herosentry.CaptureException(spanContext, err, "Failed to add contact to repository")
		return nil, fmt.Errorf("failed to create contact: %v", err)
	}

	return contact, nil
}

// UpdateContactInContactBook updates an existing contact record in the organization's contact book
func (orgUseCase *OrgUseCase) UpdateContactInContactBook(ctx context.Context, contactID, name, phone string) (*orgs.ContactRecord, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "OrgUseCase.UpdateContactInContactBook")
	defer finish()
	span.SetTag("contact.id", contactID)

	if contactID == "" {
		err := fmt.Errorf("contact ID is required")
		herosentry.CaptureException(spanContext, err, "Missing contact ID")
		return nil, err
	}
	if name == "" {
		err := fmt.Errorf("contact name is required")
		herosentry.CaptureException(spanContext, err, "Missing contact name")
		return nil, err
	}
	if phone == "" {
		err := fmt.Errorf("contact phone is required")
		herosentry.CaptureException(spanContext, err, "Missing contact phone")
		return nil, err
	}

	// Standardize and validate the phone number format
	standardizedPhone, err := utils.StandardizeUSPhoneNumber(phone)
	if err != nil {
		herosentry.CaptureException(spanContext, err, "Invalid phone number format")
		return nil, fmt.Errorf("invalid phone number format: %v", err)
	}

	// Get the existing contact
	existingContact, err := orgUseCase.orgRepo.GetContactFromContactBook(spanContext, nil, contactID)
	if err != nil {
		herosentry.CaptureException(spanContext, err, "Contact not found")
		return nil, fmt.Errorf("contact not found: %v", err)
	}

	// Update the contact fields
	existingContact.Name = name
	existingContact.Phone = standardizedPhone
	existingContact.UpdatedAt = timestamppb.New(time.Now())

	err = orgUseCase.orgRepo.UpdateContactInContactBook(spanContext, nil, existingContact)
	if err != nil {
		herosentry.CaptureException(spanContext, err, "Failed to update contact in repository")
		return nil, fmt.Errorf("failed to update contact: %v", err)
	}

	return existingContact, nil
}

// DeleteFromContactBook deletes a contact record from the organization's contact book
func (orgUseCase *OrgUseCase) DeleteFromContactBook(ctx context.Context, contactID string) error {
	spanContext, span, finish := herosentry.StartSpan(ctx, "OrgUseCase.DeleteFromContactBook")
	defer finish()
	span.SetTag("contact.id", contactID)

	if contactID == "" {
		err := fmt.Errorf("contact ID is required")
		herosentry.CaptureException(spanContext, err, "Missing contact ID")
		return err
	}

	// Verify the contact exists before deleting
	_, err := orgUseCase.orgRepo.GetContactFromContactBook(spanContext, nil, contactID)
	if err != nil {
		herosentry.CaptureException(spanContext, err, "Contact not found")
		return fmt.Errorf("contact not found: %v", err)
	}

	if err := orgUseCase.orgRepo.DeleteFromContactBook(spanContext, nil, contactID); err != nil {
		herosentry.CaptureException(spanContext, err, "Failed to delete contact from repository")
		return err
	}
	return nil
}

// GetContactFromContactBook retrieves a contact record by its ID from the organization's contact book
func (orgUseCase *OrgUseCase) GetContactFromContactBook(ctx context.Context, contactID string) (*orgs.ContactRecord, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "OrgUseCase.GetContactFromContactBook")
	defer finish()
	span.SetTag("contact.id", contactID)

	if contactID == "" {
		err := fmt.Errorf("contact ID is required")
		herosentry.CaptureException(spanContext, err, "Missing contact ID")
		return nil, err
	}

	contact, err := orgUseCase.orgRepo.GetContactFromContactBook(spanContext, nil, contactID)
	if err != nil {
		herosentry.CaptureException(spanContext, err, "Failed to get contact from repository")
		return nil, err
	}
	return contact, nil
}

// ListContactsInContactBook returns paginated contact records for an organization's contact book
func (orgUseCase *OrgUseCase) ListContactsInContactBook(ctx context.Context, orgID int32, pageToken string, pageSize int32) ([]*orgs.ContactRecord, string, int32, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "OrgUseCase.ListContactsInContactBook")
	defer finish()
	span.SetTag("org.id", fmt.Sprintf("%d", orgID))
	span.SetTag("page.size", fmt.Sprintf("%d", pageSize))

	// Verify the organization exists
	_, err := orgUseCase.orgRepo.GetOrg(spanContext, nil, orgID)
	if err != nil {
		herosentry.CaptureException(spanContext, err, "Organization not found")
		return nil, "", 0, fmt.Errorf("organization not found: %v", err)
	}

	contacts, nextToken, total, err := orgUseCase.orgRepo.ListContactsInContactBook(spanContext, nil, orgID, pageToken, pageSize)
	if err != nil {
		herosentry.CaptureException(spanContext, err, "Failed to list contacts from repository")
		return nil, "", 0, err
	}
	return contacts, nextToken, total, nil
}

// CreatePreRegistrationMapping creates a new pre-registration user mapping.
// If assetType is ASSET_TYPE_UNSPECIFIED (0), it defaults to ASSET_TYPE_RESPONDER.
func (orgUseCase *OrgUseCase) CreatePreRegistrationMapping(ctx context.Context, email, createdBy string, orgID int32, assetType assets.AssetType) (*orgs.PreRegistrationUserMapping, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "OrgUseCase.CreatePreRegistrationMapping")
	defer finish()
	span.SetTag("org.id", fmt.Sprintf("%d", orgID))

	if email == "" {
		err := fmt.Errorf("email is required")
		herosentry.CaptureException(spanContext, err, "Missing email for pre-registration mapping")
		return nil, err
	}

	// Default to RESPONDER if not specified
	if assetType == assets.AssetType_ASSET_TYPE_UNSPECIFIED {
		assetType = assets.AssetType_ASSET_TYPE_RESPONDER
	}

	mapping := &orgs.PreRegistrationUserMapping{
		Email:     email,
		OrgId:     orgID,
		AssetType: assetType,
		CreatedBy: createdBy,
	}

	result, err := orgUseCase.orgRepo.CreatePreRegistrationMapping(spanContext, nil, mapping)
	if err != nil {
		herosentry.CaptureException(spanContext, err, "Failed to create pre-registration mapping")
		return nil, err
	}
	return result, nil
}

// CreatePreRegistrationMappings creates multiple pre-registration user mappings atomically
func (orgUseCase *OrgUseCase) CreatePreRegistrationMappings(ctx context.Context, mappings []*orgs.CreatePreRegistrationMappingRequest) ([]*orgs.PreRegistrationUserMapping, []string, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "OrgUseCase.CreatePreRegistrationMappings")
	defer finish()
	span.SetTag("mappings.count", fmt.Sprintf("%d", len(mappings)))

	if len(mappings) == 0 {
		err := fmt.Errorf("no mappings provided")
		herosentry.CaptureException(spanContext, err, "No mappings provided")
		return nil, nil, err
	}

	// Start a transaction for atomic operation
	tx, err := orgUseCase.database.BeginTx(spanContext, nil)
	if err != nil {
		herosentry.CaptureException(spanContext, err, "Failed to begin transaction")
		return nil, nil, fmt.Errorf("failed to begin transaction: %v", err)
	}
	defer func() {
		if rollbackErr := tx.Rollback(); rollbackErr != nil && rollbackErr != sql.ErrTxDone {
			// Log the rollback error but don't override the main error
			// In a production system, you might want to use a proper logger here
			fmt.Printf("Warning: failed to rollback transaction: %v\n", rollbackErr)
		}
	}()

	var createdMappings []*orgs.PreRegistrationUserMapping
	var errors []string

	for i, req := range mappings {
		if req.Email == "" {
			errors = append(errors, fmt.Sprintf("mapping %d: email is required", i))
			continue
		}

		// Default to RESPONDER if not specified
		assetType := req.AssetType
		if assetType == assets.AssetType_ASSET_TYPE_UNSPECIFIED {
			assetType = assets.AssetType_ASSET_TYPE_RESPONDER
		}

		mapping := &orgs.PreRegistrationUserMapping{
			Email:     req.Email,
			OrgId:     req.OrgId,
			AssetType: assetType,
			CreatedBy: req.CreatedBy,
		}

		createdMapping, err := orgUseCase.orgRepo.CreatePreRegistrationMapping(spanContext, tx, mapping)
		if err != nil {
			errors = append(errors, fmt.Sprintf("mapping %d: failed to create: %v", i, err))
			continue
		}

		createdMappings = append(createdMappings, createdMapping)
	}

	// If there are any errors, rollback the transaction
	if len(errors) > 0 {
		return nil, errors, fmt.Errorf("failed to create %d mappings", len(errors))
	}

	// Commit the transaction
	if err := tx.Commit(); err != nil {
		return nil, nil, fmt.Errorf("failed to commit transaction: %v", err)
	}

	return createdMappings, nil, nil
}

// GetPreRegistrationMapping retrieves a pre-registration mapping by email and org ID
func (orgUseCase *OrgUseCase) GetPreRegistrationMapping(ctx context.Context, email string, orgID int32) (*orgs.PreRegistrationUserMapping, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "OrgUseCase.GetPreRegistrationMapping")
	defer finish()
	span.SetTag("org.id", fmt.Sprintf("%d", orgID))

	if email == "" {
		err := fmt.Errorf("email is required")
		herosentry.CaptureException(spanContext, err, "Missing email for pre-registration mapping")
		return nil, err
	}

	mapping, err := orgUseCase.orgRepo.GetPreRegistrationMapping(spanContext, nil, email, orgID)
	if err != nil {
		herosentry.CaptureException(spanContext, err, "Failed to get pre-registration mapping")
		return nil, err
	}
	return mapping, nil
}

// ListPreRegistrationMappings returns paginated pre-registration mappings for an organization
func (orgUseCase *OrgUseCase) ListPreRegistrationMappings(ctx context.Context, orgID int32, pageToken string, pageSize int32, includeUsed bool) ([]*orgs.PreRegistrationUserMapping, string, int32, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "OrgUseCase.ListPreRegistrationMappings")
	defer finish()
	span.SetTag("org.id", fmt.Sprintf("%d", orgID))
	span.SetTag("page.size", fmt.Sprintf("%d", pageSize))
	span.SetTag("include_used", fmt.Sprintf("%v", includeUsed))

	// Verify the organization exists
	_, err := orgUseCase.orgRepo.GetOrg(spanContext, nil, orgID)
	if err != nil {
		herosentry.CaptureException(spanContext, err, "Organization not found")
		return nil, "", 0, fmt.Errorf("organization not found: %v", err)
	}

	mappings, nextToken, total, err := orgUseCase.orgRepo.ListPreRegistrationMappings(spanContext, nil, orgID, pageToken, pageSize, includeUsed)
	if err != nil {
		herosentry.CaptureException(spanContext, err, "Failed to list pre-registration mappings")
		return nil, "", 0, err
	}
	return mappings, nextToken, total, nil
}

// UpdatePreRegistrationMapping updates an existing pre-registration mapping.
// AssetType must be explicitly specified and cannot be ASSET_TYPE_UNSPECIFIED.
func (orgUseCase *OrgUseCase) UpdatePreRegistrationMapping(ctx context.Context, mappingID string, assetType assets.AssetType) (*orgs.PreRegistrationUserMapping, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "OrgUseCase.UpdatePreRegistrationMapping")
	defer finish()
	span.SetTag("mapping.id", mappingID)
	span.SetTag("asset.type", assetType.String())

	if mappingID == "" {
		err := fmt.Errorf("mapping ID is required")
		herosentry.CaptureException(spanContext, err, "Missing mapping ID")
		return nil, err
	}

	// For updates, asset type must be explicitly specified
	if assetType == assets.AssetType_ASSET_TYPE_UNSPECIFIED {
		err := fmt.Errorf("asset type must be specified for updates")
		herosentry.CaptureException(spanContext, err, "Asset type not specified")
		return nil, err
	}

	mapping := &orgs.PreRegistrationUserMapping{
		Id:        mappingID,
		AssetType: assetType,
	}

	result, err := orgUseCase.orgRepo.UpdatePreRegistrationMapping(spanContext, nil, mapping)
	if err != nil {
		herosentry.CaptureException(spanContext, err, "Failed to update pre-registration mapping")
		return nil, err
	}
	return result, nil
}

// DeletePreRegistrationMapping deletes a pre-registration mapping
func (orgUseCase *OrgUseCase) DeletePreRegistrationMapping(ctx context.Context, mappingID string) error {
	spanContext, span, finish := herosentry.StartSpan(ctx, "OrgUseCase.DeletePreRegistrationMapping")
	defer finish()
	span.SetTag("mapping.id", mappingID)

	if mappingID == "" {
		err := fmt.Errorf("mapping ID is required")
		herosentry.CaptureException(spanContext, err, "Missing mapping ID")
		return err
	}

	if err := orgUseCase.orgRepo.DeletePreRegistrationMapping(spanContext, nil, mappingID); err != nil {
		herosentry.CaptureException(spanContext, err, "Failed to delete pre-registration mapping")
		return err
	}
	return nil
}

// MarkMappingAsUsed marks a pre-registration mapping as used
func (orgUseCase *OrgUseCase) MarkMappingAsUsed(ctx context.Context, mappingID string) error {
	spanContext, span, finish := herosentry.StartSpan(ctx, "OrgUseCase.MarkMappingAsUsed")
	defer finish()
	span.SetTag("mapping.id", mappingID)

	if mappingID == "" {
		err := fmt.Errorf("mapping ID is required")
		herosentry.CaptureException(spanContext, err, "Missing mapping ID")
		return err
	}

	if err := orgUseCase.orgRepo.MarkMappingAsUsed(spanContext, nil, mappingID); err != nil {
		herosentry.CaptureException(spanContext, err, "Failed to mark mapping as used")
		return err
	}
	return nil
}

// GetContactByPhoneNumber retrieves a contact record by phone number from the organization's contact book
func (orgUseCase *OrgUseCase) GetContactByPhoneNumber(ctx context.Context, orgID int32, phone string) (*orgs.ContactRecord, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "OrgUseCase.GetContactByPhoneNumber")
	defer finish()
	span.SetTag("org.id", fmt.Sprintf("%d", orgID))

	if phone == "" {
		err := fmt.Errorf("phone number is required")
		herosentry.CaptureException(spanContext, err, "Missing phone number")
		return nil, err
	}

	// Standardize the phone number for consistent lookup
	standardizedPhone, err := utils.StandardizeUSPhoneNumber(phone)
	if err != nil {
		herosentry.CaptureException(spanContext, err, "Invalid phone number format")
		return nil, fmt.Errorf("invalid phone number format: %v", err)
	}

	// Verify the organization exists
	_, err = orgUseCase.orgRepo.GetOrg(spanContext, nil, orgID)
	if err != nil {
		herosentry.CaptureException(spanContext, err, "Organization not found")
		return nil, fmt.Errorf("organization not found: %v", err)
	}

	contact, err := orgUseCase.orgRepo.GetContactByPhoneNumber(spanContext, nil, orgID, standardizedPhone)
	if err != nil {
		herosentry.CaptureException(spanContext, err, "Failed to get contact by phone number")
		return nil, err
	}
	return contact, nil
}
