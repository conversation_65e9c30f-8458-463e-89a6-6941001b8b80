package usecase

import (
	"context"
	"database/sql"
	"fmt"

	"common/herosentry"
	orderpb "proto/hero/orders/v2"
	reportpb "proto/hero/reports/v2"
	assetRepository "workflow/internal/assets/data"
	workflowUtils "workflow/internal/common/utils"
	orderRepository "workflow/internal/orders/data"
	reportRepository "workflow/internal/reports/data"

	"google.golang.org/protobuf/types/known/structpb"
)

const defaultPageSize = 100

// ReportUseCase groups business‑level operations for the report domain and
// delegates data persistence to a ReportRepository implementation.
type ReportUseCase struct {
	databaseConnection *sql.DB
	assetRepository    assetRepository.AssetRepository
	reportRepository   reportRepository.ReportRepository
	orderRepository    orderRepository.OrderRepository
}

// NewReportUseCase constructs a fully‑initialised ReportUseCase instance.
func NewReportUseCase(databaseConnection *sql.DB, assetRepository assetRepository.AssetRepository, reportRepository reportRepository.ReportRepository, orderRepository orderRepository.OrderRepository) (*ReportUseCase, error) {
	if databaseConnection == nil {
		return nil, fmt.Errorf("database connection is nil")
	}
	if assetRepository == nil {
		return nil, fmt.Errorf("asset repository must not be nil")
	}
	if reportRepository == nil {
		return nil, fmt.Errorf("report repository must not be nil")
	}
	if orderRepository == nil {
		return nil, fmt.Errorf("order repository must not be nil")
	}
	return &ReportUseCase{databaseConnection: databaseConnection, assetRepository: assetRepository, reportRepository: reportRepository, orderRepository: orderRepository}, nil
}

// executeInTx wraps a series of repository calls in a SQL transaction, ensuring
// commit on success and rollback on error or panic.
func (usecase *ReportUseCase) executeInTx(ctx context.Context, transactionalWork func(transaction *sql.Tx) error) error {
	transaction, transactionErr := usecase.databaseConnection.BeginTx(ctx, nil)
	if transactionErr != nil {
		return fmt.Errorf("begin transaction: %w", transactionErr)
	}
	defer func() {
		if recoveredPanic := recover(); recoveredPanic != nil {
			_ = transaction.Rollback()
			panic(recoveredPanic)
		}
	}()

	if workErr := transactionalWork(transaction); workErr != nil {
		_ = transaction.Rollback()
		return workErr
	}
	if commitErr := transaction.Commit(); commitErr != nil {
		return fmt.Errorf("commit transaction: %w", commitErr)
	}
	return nil
}

// -----------------------------------------------------------------------------
// Report CRUD
// -----------------------------------------------------------------------------

// CreateReport creates a report and enqueues a WRITE_REPORT order.
func (usecase *ReportUseCase) CreateReport(ctx context.Context, reportToCreate *reportpb.Report) (*reportpb.Report, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "ReportUseCase.CreateReport")
	defer finishSpan()

	// If the report is created with an empty author_asset_id, we need to find the first
	// completed ORDER_TYPE_ASSIST_MEMBER order for the situation and set it as the author.
	// We can only do this if the report is not already assigned to an author.
	// And SituationId is not empty.
	// And status is either unspecified or ASSIGNED (though you should not set status assigned without assigning anyone).
	if reportToCreate.AuthorAssetId == "" &&
		reportToCreate.SituationId != "" &&
		(reportToCreate.Status == reportpb.ReportStatus_REPORT_STATUS_UNSPECIFIED || reportToCreate.Status == reportpb.ReportStatus_REPORT_STATUS_ASSIGNED) {

		// Log that we're attempting to find an author for the report
		fmt.Printf("Attempting to find an author for report with situation ID: %s\n", reportToCreate.SituationId)

		var orders []*orderpb.Order
		for token := ""; ; {
			resp, err := usecase.orderRepository.ListOrdersForSituation(ctx, nil, reportToCreate.SituationId, defaultPageSize, token, 0)
			if err != nil {
				return nil, fmt.Errorf("list orders for situation: %w", err)
			}
			orders = append(orders, resp.Orders...)
			if token = resp.PageToken; token == "" {
				break
			}
		}

		for _, order := range orders {
			if order.Type == orderpb.OrderType_ORDER_TYPE_ASSIST_MEMBER &&
				order.AssetId != "" &&
				order.Status != orderpb.OrderStatus_ORDER_STATUS_REJECTED &&
				order.Status != orderpb.OrderStatus_ORDER_STATUS_CANCELLED {
				// We found the first not cancelled/rejected assist member order, set it as the author.
				reportToCreate.AuthorAssetId = order.AssetId
				if reportToCreate.Status == reportpb.ReportStatus_REPORT_STATUS_UNSPECIFIED {
					reportToCreate.Status = reportpb.ReportStatus_REPORT_STATUS_ASSIGNED
				}
				break
			}
		}
	}

	var createdReport *reportpb.Report
	createErr := usecase.executeInTx(spanContext, func(transaction *sql.Tx) error {
		var repositoryErr error
		createdReport, repositoryErr = usecase.reportRepository.CreateReport(spanContext, transaction, reportToCreate)
		if repositoryErr != nil {
			return repositoryErr
		}

		// Side-effects: on ASSIGNED status, create WRITE_REPORT order
		checker := &ReportSideEffectChecker{}
		executor := NewReportSideEffectExecutor()
		effects := checker.CheckReportStatusChangeSideEffect(createdReport)
		for _, effect := range effects {
			if execErr := executor.ExecuteSideEffect(spanContext, transaction, effect, createdReport, nil, usecase, ""); execErr != nil {
				return fmt.Errorf("CreateReport side-effect: %w", execErr)
			}
		}
		return nil
	})
	if createErr != nil {
		herosentry.CaptureException(spanContext, createErr, "Failed to create report")
		return nil, createErr
	}

	if createdReport != nil {
		span.SetTag("report.id", createdReport.Id)
		span.SetTag("report.status", createdReport.Status.String())
		span.SetTag("report.org_id", fmt.Sprintf("%d", createdReport.OrgId))
	}

	return createdReport, nil
}

// GetReport retrieves a report by its ID.
func (usecase *ReportUseCase) GetReport(ctx context.Context, reportID string) (*reportpb.Report, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "ReportUseCase.GetReport")
	defer finishSpan()

	span.SetTag("report.id", reportID)

	report, err := usecase.reportRepository.GetReport(spanContext, nil, reportID)
	if err != nil {
		herosentry.CaptureException(spanContext, err, fmt.Sprintf("Failed to get report %s", reportID))
		return nil, err
	}

	if report != nil {
		span.SetTag("report.status", report.Status.String())
		span.SetTag("report.org_id", fmt.Sprintf("%d", report.OrgId))
	}

	return report, nil
}

// UpdateReport updates an existing report.
func (usecase *ReportUseCase) UpdateReport(ctx context.Context, request *reportpb.UpdateReportRequest) (*reportpb.Report, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "ReportUseCase.UpdateReport")
	defer finishSpan()

	if request.GetReport() != nil {
		span.SetTag("report.id", request.GetReport().Id)
	}

	var updatedReport *reportpb.Report
	updateErr := usecase.executeInTx(spanContext, func(transaction *sql.Tx) error {
		// First get the existing report
		existingReport, err := usecase.reportRepository.GetReport(spanContext, transaction, request.GetReport().Id)
		if err != nil {
			return fmt.Errorf("failed to get existing report: %w", err)
		}

		// Create a merged report by preserving existing values when new ones aren't provided
		mergedReport := existingReport
		updateReport := request.GetReport()

		// Only update fields that are explicitly provided and supported by repository
		// Fields updated in this usecase: title, status, author_asset_id, situation_id, case_id, watcher_asset_ids, additional_info_json
		// Fields handled automatically by repository: updated_at, completed_at, version

		if updateReport.Title != "" {
			mergedReport.Title = updateReport.Title
		}
		if updateReport.Status != reportpb.ReportStatus_REPORT_STATUS_UNSPECIFIED {
			mergedReport.Status = updateReport.Status
		}
		if updateReport.AuthorAssetId != "" {
			mergedReport.AuthorAssetId = updateReport.AuthorAssetId
		}
		if updateReport.SituationId != "" {
			mergedReport.SituationId = updateReport.SituationId
		}
		if updateReport.CaseId != "" {
			mergedReport.CaseId = updateReport.CaseId
		}

		// Handle watcher asset IDs update - if provided in update request, use them; otherwise keep existing
		if updateReport.WatcherAssetIds != nil {
			mergedReport.WatcherAssetIds = updateReport.WatcherAssetIds
		}

		// For additional info, merge with existing if provided
		if updateReport.AdditionalInfoJson != nil {
			if mergedReport.AdditionalInfoJson == nil {
				mergedReport.AdditionalInfoJson = updateReport.AdditionalInfoJson
			} else {
				// Use recursive merge instead of simple overwrite
				existingMap := mergedReport.AdditionalInfoJson.AsMap()
				updateMap := updateReport.AdditionalInfoJson.AsMap()
				workflowUtils.MergeJSON(existingMap, updateMap)
				newStruct, err := structpb.NewStruct(existingMap)
				if err != nil {
					return fmt.Errorf("failed to merge additional info: %w", err)
				}
				mergedReport.AdditionalInfoJson = newStruct
			}
		}

		var repositoryErr error
		updatedReport, repositoryErr = usecase.reportRepository.UpdateReport(spanContext, transaction, mergedReport)
		return repositoryErr
	})
	if updateErr != nil {
		herosentry.CaptureException(spanContext, updateErr, fmt.Sprintf("Failed to update report %s", request.GetReport().Id))
		return nil, updateErr
	}

	// Track updated report version
	if updatedReport != nil {
		span.SetTag("report.updated_version", fmt.Sprintf("%d", updatedReport.Version))
	}

	return updatedReport, nil
}

// UpdateReportStatus updates the status of a report and handles side effects.
func (usecase *ReportUseCase) UpdateReportStatus(ctx context.Context, reportID string, newStatus reportpb.ReportStatus) (*reportpb.Report, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "ReportUseCase.UpdateReportStatus")
	defer finishSpan()

	span.SetTag("report.id", reportID)
	span.SetTag("report.new_status", newStatus.String())

	var updatedReport *reportpb.Report
	updateErr := usecase.executeInTx(spanContext, func(transaction *sql.Tx) error {
		var repositoryErr error

		// Update the status in the repository
		updatedReport, repositoryErr = usecase.reportRepository.UpdateReportStatus(spanContext, transaction, reportID, newStatus)
		if repositoryErr != nil {
			return repositoryErr // Already includes context
		}

		// Side-effects: Check based on the transition from originalReport.Status to updatedReport.Status
		checker := &ReportSideEffectChecker{}
		executor := NewReportSideEffectExecutor()
		effects := checker.CheckReportStatusChangeSideEffect(updatedReport) // Pass the *updated* report
		for _, effect := range effects {
			if execErr := executor.ExecuteSideEffect(spanContext, transaction, effect, updatedReport, nil, usecase, ""); execErr != nil {
				// Rollback transaction if side effect fails
				return fmt.Errorf("UpdateReportStatus side-effect execution failed: %w", execErr)
			}
		}
		return nil // Commit transaction
	})

	if updateErr != nil {
		herosentry.CaptureException(spanContext, updateErr, "Failed to update report status")
		return nil, updateErr
	}

	return updatedReport, nil
}

// ListReports returns a paginated list of reports.
func (usecase *ReportUseCase) ListReports(ctx context.Context, pageSize int32, pageToken string, statusFilter reportpb.ReportStatus, organizationID int32) ([]*reportpb.Report, string, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "ReportUseCase.ListReports")
	defer finishSpan()

	span.SetTag("report.page_size", fmt.Sprintf("%d", pageSize))
	span.SetTag("report.status_filter", statusFilter.String())
	span.SetTag("report.org_id", fmt.Sprintf("%d", organizationID))
	effectivePageSize := pageSize
	if effectivePageSize <= 0 {
		effectivePageSize = defaultPageSize
	}
	listResponse, listErr := usecase.reportRepository.ListReports(spanContext, nil, int(effectivePageSize), pageToken, statusFilter, organizationID)
	if listErr != nil {
		herosentry.CaptureException(spanContext, listErr, "Failed to list reports")
		return nil, "", listErr
	}

	span.SetTag("report.count", fmt.Sprintf("%d", len(listResponse.Reports)))

	return listResponse.Reports, listResponse.NextPageToken, nil
}

// ListReportsBySituationID returns a page of reports filtered by situation_id.
func (usecase *ReportUseCase) ListReportsBySituationID(
	ctx context.Context,
	situationID string,
	pageSize int32,
	pageToken string,
) ([]*reportpb.Report, string, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "ReportUseCase.ListReportsBySituationID")
	defer finishSpan()

	span.SetTag("report.situation_id", situationID)
	span.SetTag("report.page_size", fmt.Sprintf("%d", pageSize))
	if situationID == "" {
		return nil, "", fmt.Errorf("situationID must be provided")
	}
	effectivePageSize := pageSize
	if effectivePageSize <= 0 {
		effectivePageSize = defaultPageSize
	}
	resp, err := usecase.reportRepository.ListReportsBySituationID(
		spanContext, nil,
		situationID,
		int(effectivePageSize),
		pageToken,
	)
	if err != nil {
		herosentry.CaptureException(spanContext, err, fmt.Sprintf("Failed to list reports for situation %s", situationID))
		return nil, "", err
	}

	span.SetTag("report.count", fmt.Sprintf("%d", len(resp.Reports)))

	return resp.Reports, resp.NextPageToken, nil
}

// ListReportsByCaseID returns a page of reports filtered by case_id.
func (usecase *ReportUseCase) ListReportsByCaseID(
	ctx context.Context,
	caseID string,
	pageSize int32,
	pageToken string,
) ([]*reportpb.Report, string, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "ReportUseCase.ListReportsByCaseID")
	defer finishSpan()

	span.SetTag("report.case_id", caseID)
	span.SetTag("report.page_size", fmt.Sprintf("%d", pageSize))
	if caseID == "" {
		return nil, "", fmt.Errorf("caseID must be provided")
	}
	effectivePageSize := pageSize
	if effectivePageSize <= 0 {
		effectivePageSize = defaultPageSize
	}
	resp, err := usecase.reportRepository.ListReportsByCaseID(
		spanContext, nil,
		caseID,
		int(effectivePageSize),
		pageToken,
	)
	if err != nil {
		herosentry.CaptureException(spanContext, err, fmt.Sprintf("Failed to list reports for case %s", caseID))
		return nil, "", err
	}

	span.SetTag("report.count", fmt.Sprintf("%d", len(resp.Reports)))

	return resp.Reports, resp.NextPageToken, nil
}

// BatchGetReports retrieves multiple reports by their IDs.
func (usecase *ReportUseCase) BatchGetReports(ctx context.Context, reportIDs []string) ([]*reportpb.Report, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "ReportUseCase.BatchGetReports")
	defer finishSpan()

	span.SetTag("report.ids_count", fmt.Sprintf("%d", len(reportIDs)))
	if len(reportIDs) == 0 {
		return []*reportpb.Report{}, nil
	}

	reports, err := usecase.reportRepository.BatchGetReports(spanContext, nil, reportIDs)
	if err != nil {
		herosentry.CaptureException(spanContext, err, "Failed to batch get reports")
		return nil, err
	}

	span.SetTag("report.retrieved_count", fmt.Sprintf("%d", len(reports)))

	return reports, nil
}

// DeleteReport deletes a report by its ID.
func (usecase *ReportUseCase) DeleteReport(ctx context.Context, reportID string) error {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "ReportUseCase.DeleteReport")
	defer finishSpan()

	span.SetTag("report.id", reportID)
	err := usecase.executeInTx(spanContext, func(transaction *sql.Tx) error {
		return usecase.reportRepository.DeleteReport(spanContext, transaction, reportID)
	})
	if err != nil {
		herosentry.CaptureException(spanContext, err, fmt.Sprintf("Failed to delete report %s", reportID))
		return err
	}
	return nil
}

// -----------------------------------------------------------------------------
// Section CRUD
// -----------------------------------------------------------------------------

// CreateReportSection adds a new section to a report.
func (usecase *ReportUseCase) CreateReportSection(ctx context.Context, reportId string, sectionToCreate *reportpb.ReportSection) (*reportpb.ReportSection, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "ReportUseCase.CreateReportSection")
	defer finishSpan()

	span.SetTag("report.id", reportId)
	if sectionToCreate != nil {
		span.SetTag("section.type", sectionToCreate.Type.String())
	}
	// Validate section type before creation
	if err := validateSectionType(sectionToCreate); err != nil {
		return nil, fmt.Errorf("invalid section: %w", err)
	}

	var createdSection *reportpb.ReportSection
	createErr := usecase.executeInTx(spanContext, func(transaction *sql.Tx) error {
		var repositoryErr error
		createdSection, repositoryErr = usecase.reportRepository.CreateReportSection(spanContext, transaction, reportId, sectionToCreate)
		return repositoryErr
	})
	if createErr != nil {
		herosentry.CaptureException(spanContext, createErr, fmt.Sprintf("Failed to create section for report %s", reportId))
		return nil, createErr
	}

	if createdSection != nil {
		span.SetTag("section.id", createdSection.Id)
	}

	return createdSection, nil
}

// GetReportSection retrieves a single report section.
func (usecase *ReportUseCase) GetReportSection(ctx context.Context, reportID, sectionID string) (*reportpb.ReportSection, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "ReportUseCase.GetReportSection")
	defer finishSpan()

	span.SetTag("report.id", reportID)
	span.SetTag("section.id", sectionID)

	section, err := usecase.reportRepository.GetReportSection(spanContext, nil, reportID, sectionID)
	if err != nil {
		herosentry.CaptureException(spanContext, err, fmt.Sprintf("Failed to get section %s for report %s", sectionID, reportID))
		return nil, err
	}

	if section != nil {
		span.SetTag("section.type", section.Type.String())
	}

	return section, nil
}

// UpdateReportSection updates an existing report section.
func (usecase *ReportUseCase) UpdateReportSection(ctx context.Context, reportID string, sectionToUpdate *reportpb.ReportSection) (*reportpb.ReportSection, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "ReportUseCase.UpdateReportSection")
	defer finishSpan()

	span.SetTag("report.id", reportID)
	if sectionToUpdate != nil {
		span.SetTag("section.id", sectionToUpdate.Id)
		span.SetTag("section.type", sectionToUpdate.Type.String())
	}
	// Validate section type before update
	if err := validateSectionType(sectionToUpdate); err != nil {
		return nil, fmt.Errorf("invalid section: %w", err)
	}

	var updatedSection *reportpb.ReportSection
	updateErr := usecase.executeInTx(spanContext, func(transaction *sql.Tx) error {
		var repositoryErr error
		updatedSection, repositoryErr = usecase.reportRepository.UpdateReportSection(spanContext, transaction, reportID, sectionToUpdate)
		return repositoryErr
	})
	if updateErr != nil {
		herosentry.CaptureException(spanContext, updateErr, fmt.Sprintf("Failed to update section for report %s", reportID))
		return nil, updateErr
	}
	return updatedSection, nil
}

// DeleteReportSection removes a section from a report.
func (usecase *ReportUseCase) DeleteReportSection(ctx context.Context, reportID, sectionID string) error {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "ReportUseCase.DeleteReportSection")
	defer finishSpan()

	span.SetTag("report.id", reportID)
	span.SetTag("section.id", sectionID)
	err := usecase.executeInTx(spanContext, func(transaction *sql.Tx) error {
		return usecase.reportRepository.DeleteReportSection(spanContext, transaction, reportID, sectionID)
	})
	if err != nil {
		herosentry.CaptureException(spanContext, err, fmt.Sprintf("Failed to delete section %s from report %s", sectionID, reportID))
		return err
	}
	return nil
}

// ListReportSections lists all sections in a report.
func (usecase *ReportUseCase) ListReportSections(ctx context.Context, reportID string) ([]*reportpb.ReportSection, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "ReportUseCase.ListReportSections")
	defer finishSpan()

	span.SetTag("report.id", reportID)

	sections, err := usecase.reportRepository.ListReportSections(spanContext, nil, reportID)
	if err != nil {
		herosentry.CaptureException(spanContext, err, fmt.Sprintf("Failed to list sections for report %s", reportID))
		return nil, err
	}

	span.SetTag("sections.count", fmt.Sprintf("%d", len(sections)))

	return sections, nil
}

// -----------------------------------------------------------------------------
// Comments
// -----------------------------------------------------------------------------

// AddComment adds a comment to a report or section.
func (usecase *ReportUseCase) AddComment(ctx context.Context, commentRequest *reportpb.AddCommentRequest) (*reportpb.Comment, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "ReportUseCase.AddComment")
	defer finishSpan()

	if commentRequest != nil && commentRequest.Comment != nil {
		span.SetTag("report.id", commentRequest.Comment.ReportId)
		if commentRequest.Comment.SectionId != "" {
			span.SetTag("section.id", commentRequest.Comment.SectionId)
		}
	}
	if commentRequest.Comment == nil {
		return nil, fmt.Errorf("comment field is required in AddCommentRequest")
	}

	var createdComment *reportpb.Comment
	createErr := usecase.executeInTx(spanContext, func(transaction *sql.Tx) error {
		var repositoryErr error
		createdComment, repositoryErr = usecase.reportRepository.AddComment(spanContext, transaction, commentRequest)
		return repositoryErr
	})
	if createErr != nil {
		herosentry.CaptureException(spanContext, createErr, "Failed to add comment")
		return nil, createErr
	}

	if createdComment != nil {
		span.SetTag("comment.id", createdComment.Id)
	}

	return createdComment, nil
}

// GetComments retrieves comments for a report or section.
func (usecase *ReportUseCase) GetComments(ctx context.Context, commentsRequest *reportpb.GetCommentsRequest) ([]*reportpb.Comment, string, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "ReportUseCase.GetComments")
	defer finishSpan()

	if commentsRequest != nil {
		span.SetTag("report.id", commentsRequest.ReportId)
		if commentsRequest.SectionId != "" {
			span.SetTag("section.id", commentsRequest.SectionId)
		}
		span.SetTag("page_size", fmt.Sprintf("%d", commentsRequest.PageSize))
	}
	effectivePageSize := int(commentsRequest.PageSize)
	if effectivePageSize <= 0 {
		effectivePageSize = defaultPageSize
	}
	commentsResponse, commentsErr := usecase.reportRepository.GetComments(spanContext, nil, commentsRequest.ReportId, commentsRequest.SectionId, effectivePageSize, commentsRequest.PageToken)
	if commentsErr != nil {
		herosentry.CaptureException(spanContext, commentsErr, "Failed to get comments")
		return nil, "", commentsErr
	}

	span.SetTag("comments.count", fmt.Sprintf("%d", len(commentsResponse.Comments)))

	return commentsResponse.Comments, commentsResponse.NextPageToken, nil
}

// UpdateComment updates an existing comment.
func (usecase *ReportUseCase) UpdateComment(ctx context.Context, commentToUpdate *reportpb.Comment) (*reportpb.Comment, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "ReportUseCase.UpdateComment")
	defer finishSpan()

	if commentToUpdate != nil {
		span.SetTag("comment.id", commentToUpdate.Id)
	}
	var updatedComment *reportpb.Comment
	updateErr := usecase.executeInTx(spanContext, func(transaction *sql.Tx) error {
		var repositoryErr error
		updatedComment, repositoryErr = usecase.reportRepository.UpdateComment(spanContext, transaction, commentToUpdate)
		return repositoryErr
	})
	if updateErr != nil {
		herosentry.CaptureException(spanContext, updateErr, "Failed to update comment")
		return nil, updateErr
	}
	return updatedComment, nil
}

// DeleteComment removes a comment.
func (usecase *ReportUseCase) DeleteComment(ctx context.Context, commentID string) error {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "ReportUseCase.DeleteComment")
	defer finishSpan()

	span.SetTag("comment.id", commentID)
	err := usecase.executeInTx(spanContext, func(transaction *sql.Tx) error {
		return usecase.reportRepository.DeleteComment(spanContext, transaction, commentID)
	})
	if err != nil {
		herosentry.CaptureException(spanContext, err, fmt.Sprintf("Failed to delete comment %s", commentID))
		return err
	}
	return nil
}

// -----------------------------------------------------------------------------
// Review Workflow
// -----------------------------------------------------------------------------

// SubmitForReview sends a report for review and enqueues a REVIEW_REPORT order.
func (usecase *ReportUseCase) SubmitForReview(ctx context.Context, reportID, authorNote string) (*reportpb.Report, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "ReportUseCase.SubmitForReview")
	defer finishSpan()

	span.SetTag("report.id", reportID)
	var submittedReport *reportpb.Report
	submitErr := usecase.executeInTx(spanContext, func(transaction *sql.Tx) error {
		var repositoryErr error
		submittedReport, repositoryErr = usecase.reportRepository.SubmitForReview(spanContext, transaction, reportID, authorNote)
		if repositoryErr != nil {
			return repositoryErr
		}

		// Side-effects: on SUBMITTED_FOR_REVIEW create REVIEW_REPORT order (within the transaction)
		checker := &ReportSideEffectChecker{}
		executor := NewReportSideEffectExecutor()
		effects := checker.CheckReportStatusChangeSideEffect(submittedReport)
		for _, effect := range effects {
			if execErr := executor.ExecuteSideEffect(spanContext, transaction, effect, submittedReport, nil, usecase, authorNote); execErr != nil {
				return fmt.Errorf("SubmitForReview side-effect: %w", execErr)
			}
		}
		return nil
	})
	if submitErr != nil {
		herosentry.CaptureException(spanContext, submitErr, fmt.Sprintf("Failed to submit report %s for review", reportID))
		return nil, submitErr // Return the error from executeInTx (could be commit error or side-effect error)
	}

	if submittedReport != nil {
		span.SetTag("report.status", submittedReport.Status.String())
	}

	return submittedReport, nil
}

// AddReviewRound assigns a reviewer and enqueues a REVIEW_REPORT order.
func (usecase *ReportUseCase) AddReviewRound(ctx context.Context, assignmentRequest *reportpb.AddReviewRoundRequest) (*reportpb.ReviewRound, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "ReportUseCase.AddReviewRound")
	defer finishSpan()

	if assignmentRequest != nil && assignmentRequest.ReviewRound != nil {
		span.SetTag("report.id", assignmentRequest.ReviewRound.ReportId)
		span.SetTag("reviewer.asset_id", assignmentRequest.ReviewRound.ReviewerAssetId)
		span.SetTag("review.level", fmt.Sprintf("%d", assignmentRequest.ReviewRound.Level))
	}
	var createdReviewRound *reportpb.ReviewRound
	assignErr := usecase.executeInTx(spanContext, func(transaction *sql.Tx) error {
		var repositoryErr error
		createdReviewRound, repositoryErr = usecase.reportRepository.AddReviewRound(spanContext, transaction, assignmentRequest)
		if repositoryErr != nil {
			return repositoryErr // Return error to rollback transaction
		}

		// Side-effects: on new review round create REVIEW_REPORT order (within the transaction)
		checker := &ReportSideEffectChecker{}
		executor := NewReportSideEffectExecutor()
		effects := checker.CheckReviewRoundStatusChangeSideEffect(createdReviewRound)
		for _, effect := range effects {
			if execErr := executor.ExecuteSideEffect(spanContext, transaction, effect, nil, createdReviewRound, usecase, ""); execErr != nil {
				return fmt.Errorf("AddReviewRound side-effect: %w", execErr)
			}
		}
		return nil // Indicate success for this transaction block
	})
	if assignErr != nil {
		herosentry.CaptureException(spanContext, assignErr, "Failed to add review round")
		return nil, assignErr // Return the error from executeInTx
	}

	if createdReviewRound != nil {
		span.SetTag("review_round.id", createdReviewRound.Id)
		span.SetTag("review_round.status", createdReviewRound.Status.String())
	}

	return createdReviewRound, nil
}

// ApproveReviewRound marks a review as approved and may escalate or finalize.
func (usecase *ReportUseCase) ApproveReviewRound(ctx context.Context, approvalRequest *reportpb.ApproveReviewRoundRequest) (*reportpb.ReviewRound, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "ReportUseCase.ApproveReviewRound")
	defer finishSpan()

	if approvalRequest != nil {
		span.SetTag("review_round.id", approvalRequest.ReviewRoundId)
	}
	var approvedRound *reportpb.ReviewRound
	approveErr := usecase.executeInTx(spanContext, func(transaction *sql.Tx) error {
		var repositoryErr error
		approvedRound, repositoryErr = usecase.reportRepository.ApproveReviewRound(spanContext, transaction, approvalRequest.ReviewRoundId, approvalRequest.Note)
		if repositoryErr != nil {
			return repositoryErr // Return error to rollback transaction
		}

		// Side-effects: on APPROVED escalate or complete (within the transaction)
		checker := &ReportSideEffectChecker{}
		executor := NewReportSideEffectExecutor()
		effects := checker.CheckReviewRoundStatusChangeSideEffect(approvedRound)
		for _, effect := range effects {
			// Pass the transaction to ExecuteSideEffect
			if execErr := executor.ExecuteSideEffect(spanContext, transaction, effect, nil, approvedRound, usecase, ""); execErr != nil {
				return fmt.Errorf("ApproveReviewRound side-effect: %w", execErr) // Return error to rollback transaction
			}
		}
		return nil // Indicate success for this transaction block
	})
	if approveErr != nil {
		herosentry.CaptureException(spanContext, approveErr, "Failed to approve review round")
		return nil, approveErr // Return the error from executeInTx
	}

	if approvedRound != nil {
		span.SetTag("review_round.status", approvedRound.Status.String())
	}

	return approvedRound, nil
}

// RequestChanges marks a review as changes requested and enqueues a REVISE_REPORT order.
func (usecase *ReportUseCase) RequestChanges(ctx context.Context, changeRequest *reportpb.RequestChangesRequest) (*reportpb.ReviewRound, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "ReportUseCase.RequestChanges")
	defer finishSpan()

	if changeRequest != nil {
		span.SetTag("review_round.id", changeRequest.ReviewRoundId)
	}
	var reviewRoundWithChanges *reportpb.ReviewRound
	requestErr := usecase.executeInTx(spanContext, func(transaction *sql.Tx) error {
		var repositoryErr error
		reviewRoundWithChanges, repositoryErr = usecase.reportRepository.RequestChanges(spanContext, transaction, changeRequest)
		if repositoryErr != nil {
			return repositoryErr
		}

		// Side-effects: on CHANGES_REQUESTED create REVISE_REPORT order (within the transaction)
		checker := &ReportSideEffectChecker{}
		executor := NewReportSideEffectExecutor()
		effects := checker.CheckReviewRoundStatusChangeSideEffect(reviewRoundWithChanges)
		for _, effect := range effects {
			if execErr := executor.ExecuteSideEffect(spanContext, transaction, effect, nil, reviewRoundWithChanges, usecase, ""); execErr != nil {
				return fmt.Errorf("RequestChanges side-effect: %w", execErr)
			}
		}
		return nil
	})
	if requestErr != nil {
		herosentry.CaptureException(spanContext, requestErr, "Failed to request changes")
		return nil, requestErr
	}

	if reviewRoundWithChanges != nil {
		span.SetTag("review_round.status", reviewRoundWithChanges.Status.String())
	}

	return reviewRoundWithChanges, nil
}

// ListReviewRoundsForReport retrieves review rounds for a specific report.
func (usecase *ReportUseCase) ListReviewRoundsForReport(
	ctx context.Context,
	reportID string,
	pageSize int32,
	pageToken string,
) ([]*reportpb.ReviewRound, string, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "ReportUseCase.ListReviewRoundsForReport")
	defer finishSpan()

	span.SetTag("report.id", reportID)
	span.SetTag("page_size", fmt.Sprintf("%d", pageSize))
	effectivePageSize := pageSize
	if effectivePageSize <= 0 {
		effectivePageSize = defaultPageSize
	}
	resp, err := usecase.reportRepository.ListReviewRoundsForReport(
		spanContext, nil,
		reportID,
		int(effectivePageSize),
		pageToken,
	)
	if err != nil {
		herosentry.CaptureException(spanContext, err, fmt.Sprintf("Failed to list review rounds for report %s", reportID))
		return nil, "", fmt.Errorf("failed to list review rounds for report %s: %w", reportID, err)
	}

	span.SetTag("review_rounds.count", fmt.Sprintf("%d", len(resp.ReviewRounds)))

	return resp.ReviewRounds, resp.NextPageToken, nil
}

// GetReviewRound retrieves a single review round by its ID.
func (usecase *ReportUseCase) GetReviewRound(ctx context.Context, reviewRoundID string) (*reportpb.ReviewRound, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "ReportUseCase.GetReviewRound")
	defer finishSpan()

	span.SetTag("review_round.id", reviewRoundID)
	round, err := usecase.reportRepository.GetReviewRound(spanContext, nil, reviewRoundID)
	if err != nil {
		herosentry.CaptureException(spanContext, err, fmt.Sprintf("Failed to get review round %s", reviewRoundID))
		return nil, fmt.Errorf("failed to get review round %s: %w", reviewRoundID, err)
	}

	if round != nil {
		span.SetTag("review_round.status", round.Status.String())
		span.SetTag("review_round.level", fmt.Sprintf("%d", round.Level))
	}

	return round, nil
}

// UpdateReviewRound updates an existing review round.
// Note: This performs a general update. Status changes should use ApproveReviewRound/RequestChanges.
func (usecase *ReportUseCase) UpdateReviewRound(ctx context.Context, inputRound *reportpb.ReviewRound) (*reportpb.ReviewRound, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "ReportUseCase.UpdateReviewRound")
	defer finishSpan()

	if inputRound != nil {
		span.SetTag("review_round.id", inputRound.Id)
	}
	var finalUpdatedRound *reportpb.ReviewRound
	updateErr := usecase.executeInTx(spanContext, func(tx *sql.Tx) error {
		// 1. Fetch the existing review round to ensure it exists and to merge changes.
		existingRound, getErr := usecase.reportRepository.GetReviewRound(spanContext, tx, inputRound.Id)
		if getErr != nil {
			return fmt.Errorf("failed to fetch existing review round %s for update: %w", inputRound.Id, getErr)
		}

		// 2. Apply changes from inputRound to existingRound selectively.
		// We only update fields that are typically user-modifiable in a general update.
		// Status, timestamps related to status changes (requested_at, resolved_at), creator, report_id, snapshot_version are generally immutable or handled by specific actions.

		if inputRound.ReviewerAssetId != "" && inputRound.ReviewerAssetId != existingRound.ReviewerAssetId {
			existingRound.ReviewerAssetId = inputRound.ReviewerAssetId
		}
		if inputRound.Level != 0 && inputRound.Level != existingRound.Level { // Assuming 0 is not a valid level
			existingRound.Level = inputRound.Level
		}
		if inputRound.SentToLevel != 0 && inputRound.SentToLevel != existingRound.SentToLevel { // Assuming 0 is not a valid level
			existingRound.SentToLevel = inputRound.SentToLevel
		}
		if inputRound.SentToAssetId != "" && inputRound.SentToAssetId != existingRound.SentToAssetId {
			existingRound.SentToAssetId = inputRound.SentToAssetId
		}
		if inputRound.RoundNote != "" && inputRound.RoundNote != existingRound.RoundNote {
			existingRound.RoundNote = inputRound.RoundNote
		}
		if inputRound.DueAt != "" && inputRound.DueAt != existingRound.DueAt {
			existingRound.DueAt = inputRound.DueAt
		}
		if inputRound.NoteForReviewer != "" && inputRound.NoteForReviewer != existingRound.NoteForReviewer {
			existingRound.NoteForReviewer = inputRound.NoteForReviewer
		}

		if inputRound.Status != reportpb.ReviewStatus_REVIEW_STATUS_UNSPECIFIED && inputRound.Status != existingRound.Status {
			existingRound.Status = inputRound.Status
		}

		// 4. Pass the merged/updated existingRound to the repository for persistence.
		var repoErr error
		finalUpdatedRound, repoErr = usecase.reportRepository.UpdateReviewRound(spanContext, tx, existingRound) // Pass the modified existingRound
		if repoErr != nil {
			return repoErr // Rollback transaction
		}
		// No side effects expected for a general update, only for status changes.
		checker := &ReportSideEffectChecker{}
		executor := NewReportSideEffectExecutor()
		effects := checker.CheckReviewRoundStatusChangeSideEffect(finalUpdatedRound)
		for _, effect := range effects {
			if execErr := executor.ExecuteSideEffect(spanContext, tx, effect, nil, finalUpdatedRound, usecase, ""); execErr != nil {
				return fmt.Errorf("UpdateReviewRound side-effect: %w", execErr) // Return error to rollback transaction
			}
		}
		return nil // Indicate success for this transaction block
	})
	if updateErr != nil {
		herosentry.CaptureException(spanContext, updateErr, fmt.Sprintf("Failed to update review round %s", inputRound.Id))
		return nil, fmt.Errorf("failed to update review round %s: %w", inputRound.Id, updateErr)
	}
	return finalUpdatedRound, nil
}

// DeleteReviewRound deletes a review round by its ID.
func (usecase *ReportUseCase) DeleteReviewRound(ctx context.Context, reviewRoundID string) error {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "ReportUseCase.DeleteReviewRound")
	defer finishSpan()

	span.SetTag("review_round.id", reviewRoundID)
	deleteErr := usecase.executeInTx(spanContext, func(tx *sql.Tx) error {

		repoErr := usecase.reportRepository.DeleteReviewRound(spanContext, tx, reviewRoundID)
		if repoErr != nil {
			return repoErr
		}
		return nil
	})
	if deleteErr != nil {
		herosentry.CaptureException(spanContext, deleteErr, fmt.Sprintf("Failed to delete review round %s", reviewRoundID))
		return fmt.Errorf("failed to delete review round %s: %w", reviewRoundID, deleteErr)
	}
	return nil
}

// -----------------------------------------------------------------------------
// JSON Metadata
// -----------------------------------------------------------------------------

func (usecase *ReportUseCase) UpdateAdditionalInfoJSON(ctx context.Context, metadataRequest *reportpb.UpdateAdditionalInfoJsonRequest) (*reportpb.UpdateAdditionalInfoJsonResponse, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "ReportUseCase.UpdateAdditionalInfoJSON")
	defer finishSpan()

	if metadataRequest != nil {
		span.SetTag("report.id", metadataRequest.ReportId)
	}
	var metadataResponse *reportpb.UpdateAdditionalInfoJsonResponse
	updateErr := usecase.executeInTx(spanContext, func(transaction *sql.Tx) error {
		var repositoryErr error
		metadataResponse, repositoryErr = usecase.reportRepository.UpdateAdditionalInfoJSON(spanContext, transaction, metadataRequest)
		return repositoryErr
	})
	if updateErr != nil {
		herosentry.CaptureException(spanContext, updateErr, "Failed to update additional info JSON")
		return nil, updateErr
	}
	return metadataResponse, nil
}

func (usecase *ReportUseCase) GetAdditionalInfo(ctx context.Context, reportID string) (*reportpb.GetAdditionalInfoResponse, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "ReportUseCase.GetAdditionalInfo")
	defer finishSpan()

	span.SetTag("report.id", reportID)

	info, err := usecase.reportRepository.GetAdditionalInfo(spanContext, nil, reportID)
	if err != nil {
		herosentry.CaptureException(spanContext, err, fmt.Sprintf("Failed to get additional info for report %s", reportID))
		return nil, err
	}
	return info, nil
}

// -----------------------------------------------------------------------------
// Versioning
// -----------------------------------------------------------------------------

func (usecase *ReportUseCase) GetReportVersion(ctx context.Context, reportID string, versionNumber int32) (*reportpb.ReportSnapshot, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "ReportUseCase.GetReportVersion")
	defer finishSpan()

	span.SetTag("report.id", reportID)
	span.SetTag("report.version", fmt.Sprintf("%d", versionNumber))

	snapshot, err := usecase.reportRepository.GetReportVersion(spanContext, nil, reportID, versionNumber)
	if err != nil {
		herosentry.CaptureException(spanContext, err, fmt.Sprintf("Failed to get version %d of report %s", versionNumber, reportID))
		return nil, err
	}
	return snapshot, nil
}

func (usecase *ReportUseCase) ListReportVersions(ctx context.Context, reportID string) ([]int32, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "ReportUseCase.ListReportVersions")
	defer finishSpan()

	span.SetTag("report.id", reportID)

	versions, err := usecase.reportRepository.ListReportVersions(spanContext, nil, reportID)
	if err != nil {
		herosentry.CaptureException(spanContext, err, fmt.Sprintf("Failed to list versions for report %s", reportID))
		return nil, err
	}

	span.SetTag("versions.count", fmt.Sprintf("%d", len(versions)))

	return versions, nil
}

func (usecase *ReportUseCase) ResolveComment(ctx context.Context, resolveCommentRequest *reportpb.ResolveCommentRequest) (*reportpb.Comment, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "ReportUseCase.ResolveComment")
	defer finishSpan()

	if resolveCommentRequest != nil {
		span.SetTag("comment.id", resolveCommentRequest.CommentId)
	}

	comment, err := usecase.reportRepository.ResolveComment(spanContext, nil, resolveCommentRequest)
	if err != nil {
		herosentry.CaptureException(spanContext, err, "Failed to resolve comment")
		return nil, err
	}
	return comment, nil
}

// SearchReports provides rich search capabilities with multiple filtering options.
func (usecase *ReportUseCase) SearchReports(
	ctx context.Context,
	searchReq *reportpb.SearchReportsRequest,
) (*reportpb.SearchReportsResponse, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "ReportUseCase.SearchReports")
	defer finishSpan()

	if searchReq != nil {
		span.SetTag("search.page_size", fmt.Sprintf("%d", searchReq.PageSize))
		if searchReq.Query != "" {
			span.SetTag("search.has_query", "true")
		}
	}
	if searchReq.PageSize <= 0 {
		searchReq.PageSize = defaultPageSize
	}

	result, err := usecase.reportRepository.SearchReports(
		spanContext,
		nil, // no transaction
		searchReq,
	)
	if err != nil {
		herosentry.CaptureException(spanContext, err, "Failed to search reports")
		return nil, err
	}

	if result != nil {
		span.SetTag("search.results_count", fmt.Sprintf("%d", len(result.Reports)))
	}

	return result, nil
}

// -----------------------------------------------------------------------------
// Relations CRUD
// -----------------------------------------------------------------------------

// CreateRelation creates a new relation between a report and an object.
func (usecase *ReportUseCase) CreateRelation(ctx context.Context, request *reportpb.CreateRelationRequest) (*reportpb.Relation, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "ReportUseCase.CreateRelation")
	defer finishSpan()

	span.SetTag("report.id", request.Relation.ReportId)
	span.SetTag("relation.type", request.Relation.RelationType)
	var createdRelation *reportpb.Relation
	createErr := usecase.executeInTx(spanContext, func(transaction *sql.Tx) error {
		var repositoryErr error
		createdRelation, repositoryErr = usecase.reportRepository.CreateRelation(spanContext, transaction, request)
		return repositoryErr
	})
	if createErr != nil {
		herosentry.CaptureException(spanContext, createErr, "Failed to create relation")
		return nil, createErr
	}

	if createdRelation != nil {
		span.SetTag("relation.id", createdRelation.Id)
	}

	return createdRelation, nil
}

// GetRelation retrieves a relation by its ID.
func (usecase *ReportUseCase) GetRelation(ctx context.Context, request *reportpb.GetRelationRequest) (*reportpb.Relation, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "ReportUseCase.GetRelation")
	defer finishSpan()

	if request != nil {
		span.SetTag("relation.id", request.RelationId)
	}

	relation, err := usecase.reportRepository.GetRelation(spanContext, nil, request)
	if err != nil {
		herosentry.CaptureException(spanContext, err, "Failed to get relation")
		return nil, err
	}

	if relation != nil {
		span.SetTag("relation.type", relation.RelationType)
		span.SetTag("relation.id", relation.Id)
	}

	return relation, nil
}

// UpdateRelation updates an existing relation.
func (usecase *ReportUseCase) UpdateRelation(ctx context.Context, request *reportpb.UpdateRelationRequest) (*reportpb.Relation, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "ReportUseCase.UpdateRelation")
	defer finishSpan()

	if request != nil && request.Relation != nil {
		span.SetTag("relation.id", request.Relation.Id)
	}
	var updatedRelation *reportpb.Relation
	updateErr := usecase.executeInTx(spanContext, func(transaction *sql.Tx) error {
		var repositoryErr error
		updatedRelation, repositoryErr = usecase.reportRepository.UpdateRelation(spanContext, transaction, request)
		return repositoryErr
	})
	if updateErr != nil {
		herosentry.CaptureException(spanContext, updateErr, "Failed to update relation")
		return nil, updateErr
	}
	return updatedRelation, nil
}

// DeleteRelation deletes a relation by its ID.
func (usecase *ReportUseCase) DeleteRelation(ctx context.Context, request *reportpb.DeleteRelationRequest) error {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "ReportUseCase.DeleteRelation")
	defer finishSpan()

	if request != nil {
		span.SetTag("relation.id", request.RelationId)
	}
	err := usecase.executeInTx(spanContext, func(transaction *sql.Tx) error {
		return usecase.reportRepository.DeleteRelation(spanContext, transaction, request)
	})
	if err != nil {
		herosentry.CaptureException(spanContext, err, "Failed to delete relation")
		return err
	}
	return nil
}

// ListRelations returns a paginated list of relations for a report.
func (usecase *ReportUseCase) ListRelations(ctx context.Context, request *reportpb.ListRelationsRequest) (*reportpb.ListRelationsResponse, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "ReportUseCase.ListRelations")
	defer finishSpan()

	if request != nil {
		span.SetTag("report.id", request.ReportId)
		span.SetTag("page_size", fmt.Sprintf("%d", request.PageSize))
	}

	response, err := usecase.reportRepository.ListRelations(spanContext, nil, request)
	if err != nil {
		herosentry.CaptureException(spanContext, err, "Failed to list relations")
		return nil, err
	}

	if response != nil {
		span.SetTag("relations.count", fmt.Sprintf("%d", len(response.Relations)))
	}

	return response, nil
}

// -----------------------------------------------------------------------------
// Section Validation
// -----------------------------------------------------------------------------

// validateSectionType validates that a section has a valid type specified
func validateSectionType(section *reportpb.ReportSection) error {
	if section == nil {
		return fmt.Errorf("section cannot be nil")
	}

	if section.Type == reportpb.SectionType_SECTION_TYPE_UNSPECIFIED {
		return fmt.Errorf("section type must be specified")
	}

	// Validate that the section type is supported
	validTypes := map[reportpb.SectionType]bool{
		reportpb.SectionType_SECTION_TYPE_NARRATIVE:                 true,
		reportpb.SectionType_SECTION_TYPE_ENTITY_LIST_PEOPLE:        true,
		reportpb.SectionType_SECTION_TYPE_ENTITY_LIST_VEHICLE:       true,
		reportpb.SectionType_SECTION_TYPE_ENTITY_LIST_PROPERTIES:    true,
		reportpb.SectionType_SECTION_TYPE_ENTITY_LIST_ORGANIZATIONS: true,
		reportpb.SectionType_SECTION_TYPE_INCIDENT_DETAILS:          true,
		reportpb.SectionType_SECTION_TYPE_OFFENSE:                   true,
		reportpb.SectionType_SECTION_TYPE_ARREST:                    true,
		reportpb.SectionType_SECTION_TYPE_MEDIA:                     true,
		reportpb.SectionType_SECTION_TYPE_PROPERTY:                  true,
	}

	if !validTypes[section.Type] {
		return fmt.Errorf("unsupported section type: %v", section.Type)
	}

	return nil
}
