package repository

import (
	"context"
	"database/sql"
	"errors"

	property "proto/hero/property/v1"

	_ "github.com/lib/pq"
)

// ErrPropertyNotFound is returned when a property cannot be found.
var ErrPropertyNotFound = errors.New("property not found")

const FixedResourceTypeProperty = "PROPERTY"

type PaginatedProperties struct {
	Properties    []*property.Property
	NextPageToken string
}

// PropertyRepository defines the operations for managing properties.
type PropertyRepository interface {
	// CreateProperty stores a new property.
	CreateProperty(ctx context.Context, transaction *sql.Tx, property *property.Property) error
	// GetProperty returns a property by its ID.
	GetProperty(ctx context.Context, transaction *sql.Tx, propertyID string) (*property.Property, error)

	// ListProperties returns a paginated list of properties.
	ListProperties(ctx context.Context, transaction *sql.Tx, pageSize int, pageToken string, propertyType property.PropertyType, propertyStatus property.PropertyStatus, orderBy string) (*PaginatedProperties, error)
	// DeleteProperty physically deletes a property from the store.
	DeleteProperty(ctx context.Context, transaction *sql.Tx, propertyID string) error
	// UpdateProperty updates the property fields.
	UpdateProperty(ctx context.Context, transaction *sql.Tx, property *property.Property) (*property.Property, error)

	// SearchProperties performs advanced search on properties with text matching, filtering, and geographic queries.
	SearchProperties(ctx context.Context, transaction *sql.Tx, searchRequest *property.SearchPropertiesRequest) (*property.SearchPropertiesResponse, error)

	// BatchGetProperties retrieves multiple properties by their IDs in a single operation.
	BatchGetProperties(ctx context.Context, transaction *sql.Tx, propertyIDs []string) ([]*property.Property, error)

	// AddCustodyEvent adds a new custody event to a property's chain of custody.
	AddCustodyEvent(ctx context.Context, transaction *sql.Tx, propertyID string, custodyEvent *property.CustodyEvent) error

	// GetCustodyChain returns the complete chain of custody for a property.
	GetCustodyChain(ctx context.Context, transaction *sql.Tx, propertyID string) ([]*property.CustodyEvent, error)

	// ListPropertyFileAttachments lists all file attachments for a property.
	ListPropertyFileAttachments(ctx context.Context, transaction *sql.Tx, request *property.ListPropertyFileAttachmentsRequest) (*property.ListPropertyFileAttachmentsResponse, error)

	// AddPropertyFileAttachment adds a file attachment to a property.
	AddPropertyFileAttachment(ctx context.Context, transaction *sql.Tx, request *property.AddPropertyFileAttachmentRequest) (*property.AddPropertyFileAttachmentResponse, error)

	// RemovePropertyFileAttachment removes a file attachment from a property.
	RemovePropertyFileAttachment(ctx context.Context, transaction *sql.Tx, request *property.RemovePropertyFileAttachmentRequest) (*property.RemovePropertyFileAttachmentResponse, error)
}

// NewPropertyRepository returns a PropertyRepository based on the provided configuration.
func NewPropertyRepository(postgresDB *sql.DB) (PropertyRepository, *sql.DB, error) {
	if postgresDB == nil {
		return nil, nil, errors.New("database is nil: cannot initialize PropertyRepository")
	}
	return NewPostgresPropertyRepository(postgresDB), postgresDB, nil
}
