// Package v2 provides the typed report data extraction layer for the Hero ETL pipeline.
//
// This package replaces the previous generic map[string]interface{} approach with
// strongly typed protobuf-based extraction, providing complete visibility into the
// shape of extracted data while maintaining all hydration functionality.
//
// Architecture Overview:
//
//	┌─────────────────────────────────────────────────────────────┐
//	│                    ReportDataExtractor                      │
//	│  - Main entry point for extraction                          │
//	│  - Orchestrates parallel extraction by default              │
//	│  - Provides convenience methods for specific data types     │
//	└──────────────────────┬──────────────────────────────────────┘
//	                       │
//	┌──────────────────────▼──────────────────────────────────────┐
//	│            ParallelReportDataExtractor                      │
//	│  - Implements 7-phase parallel extraction pipeline          │
//	│  - Manages concurrent execution with dependencies           │
//	│  - Creates separate transactions for thread safety          │
//	└──────────────────────┬──────────────────────────────────────┘
//	                       │
//	┌──────────────────────▼──────────────────────────────────────┐
//	│                    Extractors Package                       │
//	│  - assets.go: Extracts assets with role context             │
//	│  - entities.go: Extracts entities with section references   │
//	│  - sections.go: Extracts sections with hydrated data        │
//	│  - situations.go: Extracts connected situations             │
//	│  - relationships.go: Extracts relationships with names      │
//	└─────────────────────────────────────────────────────────────┘
//
// Key Features:
//   - Type Safety: All data represented using protobuf-generated types
//   - Full Hydration: Related objects are embedded, not just referenced
//   - Parallel Execution: Maximum performance through concurrent phases
//   - Context Preservation: Maintains all relationship metadata
//   - Error Resilience: Continues extraction even if optional data fails
//
// Usage Example:
//
//	// Create extractor with required repositories
//	extractor, err := v2.NewReportDataExtractor(
//	    dbConnection,
//	    reportsRepo,
//	    situationsRepo,
//	    assetsRepo,
//	    entityRepo,
//	)
//	if err != nil {
//	    return err
//	}
//
//	// Extract all data for a report
//	result, err := extractor.Extract(ctx, reportID)
//	if err != nil {
//	    return err
//	}
//
//	// Access typed data
//	for _, asset := range result.Data.Assets {
//	    fmt.Printf("Asset %s has role %s\n", asset.Asset.Name, asset.Role)
//	}
//
//	for _, entity := range result.Data.Entities {
//	    fmt.Printf("Entity %s in section %s\n",
//	        entity.Entity.Id,
//	        entity.ReferencedInSectionType)
//	}
//
// Performance Considerations:
//   - Parallel extraction runs 5 phases concurrently after initial report load
//   - Each phase gets its own database transaction for thread safety
//   - Batch operations prevent N+1 query problems
//   - Read-only transactions optimize database performance
//
// Error Handling:
//   - Required data (reports) causes extraction to fail if not found
//   - Optional data (assets, entities, etc.) logs warnings but continues
//   - All errors are captured in ExtractionResult.Warnings
//   - Metrics track performance and item counts
package v2

import (
	"context"
	"database/sql"
	"fmt"

	"workflow/internal/etl/extraction/reports/v2/parallel"
	"workflow/internal/etl/extraction/types"

	assetsRepository "workflow/internal/assets/data"
	entityRepository "workflow/internal/entity/data"
	propertyRepository "workflow/internal/property/data"
	reportsRepository "workflow/internal/reports/data"
	situationsRepository "workflow/internal/situations/data"
)

// ReportDataExtractor provides extraction of report data using protobuf types.
//
// This extractor replaces the generic map[string]interface{} approach with fully typed
// protobuf structs, providing complete visibility into the shape of extracted data.
// It maintains all current hydration functionality while ensuring type safety.
//
// Key improvements over the original extractor:
//   - Full type safety with protobuf message types
//   - Complete visibility into data structure
//   - Maintains all hydration functionality
//   - Decoupled from transformation layer
//   - Better error handling and debugging
type ReportDataExtractor struct {
	// Core database connection for transaction management
	databaseConnection *sql.DB

	// Repository interfaces for accessing different data sources
	reportsRepository    reportsRepository.ReportRepository
	situationsRepository situationsRepository.SituationRepository
	assetsRepository     assetsRepository.AssetRepository
	entityRepository     entityRepository.EntityRepository

	// Parallel extractor for high-performance extraction
	parallelExtractor *parallel.ParallelReportDataExtractor
}

// NewReportDataExtractor creates a new report data extractor.
//
// Parameters:
//   - databaseConnection: SQL database connection for transactions
//   - reportsRepository: Repository interface for accessing report data
//   - situationsRepository: Repository interface for accessing situation data (can be nil)
//   - assetsRepository: Repository interface for accessing asset data (can be nil)
//   - entityRepository: Repository interface for accessing entity data (can be nil)
//
// Returns:
//   - *ReportDataExtractor: Ready-to-use extractor instance
//   - error: Validation errors
func NewReportDataExtractor(
	databaseConnection *sql.DB,
	reportsRepository reportsRepository.ReportRepository,
	situationsRepository situationsRepository.SituationRepository,
	assetsRepository assetsRepository.AssetRepository,
	entityRepository entityRepository.EntityRepository,
	propertyRepository propertyRepository.PropertyRepository,
) (*ReportDataExtractor, error) {
	// Validate required dependencies
	if databaseConnection == nil {
		return nil, fmt.Errorf("database connection is required")
	}
	if reportsRepository == nil {
		return nil, fmt.Errorf("reports repository is required")
	}

	// Note: other repositories are optional and will be handled gracefully

	// Initialize parallel extractor for high-performance extraction
	parallelExtractor, err := parallel.NewParallelReportDataExtractor(
		databaseConnection, reportsRepository, situationsRepository, assetsRepository, entityRepository, propertyRepository,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize parallel extractor: %w", err)
	}

	return &ReportDataExtractor{
		databaseConnection:   databaseConnection,
		reportsRepository:    reportsRepository,
		situationsRepository: situationsRepository,
		assetsRepository:     assetsRepository,
		entityRepository:     entityRepository,
		parallelExtractor:    parallelExtractor,
	}, nil
}

// Extract performs high-performance parallel extraction of report data.
//
// This method uses parallel execution by default for maximum performance.
// It extracts all data related to a specific report using fully typed
// protobuf structures with concurrent phase execution.
//
// Performance Features:
//   - Parallel execution of independent phases (2-6 run concurrently)
//   - Dependency-aware execution minimizes waiting time
//   - Batch operations eliminate N+1 query problems
//   - Complete type safety with protobuf structures
//
// Parameters:
//   - ctx: Context for cancellation and timeouts
//   - reportID: Unique identifier of the report to extract
//
// Returns:
//   - *types.ExtractionResult: Complete typed extraction result with metrics
//   - error: Database, repository, or extraction errors
func (extractor *ReportDataExtractor) Extract(
	ctx context.Context,
	reportID string,
) (*types.ExtractionResult, error) {
	// Use parallel extraction by default for maximum performance
	return extractor.parallelExtractor.Extract(ctx, reportID)
}

// ExtractReport extracts a single report with full hydration using parallel execution.
//
// This convenience method provides direct access to the hydrated report data without
// needing to navigate the full ExtractionResult structure. It's useful when you only
// need the report itself with its embedded assets and situation.
//
// The returned HydratedReport includes:
//   - Complete report protobuf with all sections and relations
//   - Fully hydrated author and creator assets
//   - Connected situation (if any)
//   - All watcher assets
//
// Parameters:
//   - ctx: Context for cancellation and timeouts
//   - reportID: Unique identifier of the report to extract
//
// Returns:
//   - *types.HydratedReport: Fully hydrated report with embedded data
//   - error: Not found error if report doesn't exist, or extraction errors
//
// Example:
//
//	report, err := extractor.ExtractReport(ctx, "report-123")
//	if err != nil {
//	    return fmt.Errorf("failed to extract report: %w", err)
//	}
//	fmt.Printf("Report %s by %s\n", report.Report.Title, report.AuthorAsset.Name)
func (extractor *ReportDataExtractor) ExtractReport(
	ctx context.Context,
	reportID string,
) (*types.HydratedReport, error) {
	result, err := extractor.Extract(ctx, reportID)
	if err != nil {
		return nil, err
	}

	if len(result.Data.Reports) == 0 {
		return nil, fmt.Errorf("no report found with ID %s", reportID)
	}

	return result.Data.Reports[0], nil
}

// ExtractAssets extracts all assets associated with a report, including their roles.
//
// This convenience method provides direct access to all assets related to a report,
// each tagged with their specific role (creator, author, watcher). It's useful for
// understanding who is involved with a report and in what capacity.
//
// Asset roles include:
//   - "creator": The asset that created the report
//   - "author": The asset that authored the report content
//   - "watcher": Assets watching/monitoring the report
//   - Additional roles from section references (e.g., "responder")
//
// Parameters:
//   - ctx: Context for cancellation and timeouts
//   - reportID: Unique identifier of the report
//
// Returns:
//   - []*types.HydratedAsset: All assets with their role context
//   - error: Extraction errors (returns empty slice if no assets found)
//
// Example:
//
//	assets, err := extractor.ExtractAssets(ctx, "report-123")
//	if err != nil {
//	    return fmt.Errorf("failed to extract assets: %w", err)
//	}
//	for _, asset := range assets {
//	    fmt.Printf("%s (%s)\n", asset.Asset.Name, asset.Role)
//	}
func (extractor *ReportDataExtractor) ExtractAssets(
	ctx context.Context,
	reportID string,
) ([]*types.HydratedAsset, error) {
	result, err := extractor.Extract(ctx, reportID)
	if err != nil {
		return nil, err
	}

	return result.Data.Assets, nil
}

// ExtractEntities extracts all entities referenced in a report with section context.
//
// This convenience method provides direct access to all entities (people, vehicles,
// organizations, properties) mentioned in a report, along with information about
// which sections reference them. It's useful for understanding all the objects
// involved in an incident.
//
// Entity types include:
//   - ENTITY_TYPE_PERSON: People (victims, suspects, witnesses, officers)
//   - ENTITY_TYPE_VEHICLE: Vehicles involved
//   - ENTITY_TYPE_ORGANIZATION: Banks, businesses, agencies
//   - ENTITY_TYPE_PROPERTY: Physical property or evidence
//
// Each entity includes:
//   - Complete entity data with all attributes
//   - Section ID and type where it's referenced
//   - Extraction metadata showing the entity's role
//
// Parameters:
//   - ctx: Context for cancellation and timeouts
//   - reportID: Unique identifier of the report
//
// Returns:
//   - []*types.HydratedEntity: All entities with section references
//   - error: Extraction errors (returns empty slice if no entities found)
//
// Example:
//
//	entities, err := extractor.ExtractEntities(ctx, "report-123")
//	if err != nil {
//	    return fmt.Errorf("failed to extract entities: %w", err)
//	}
//	for _, entity := range entities {
//	    fmt.Printf("%s (Type: %v) in section %s\n",
//	        entity.Entity.Id,
//	        entity.Entity.EntityType,
//	        entity.ReferencedInSectionType)
//	}
func (extractor *ReportDataExtractor) ExtractEntities(
	ctx context.Context,
	reportID string,
) ([]*types.HydratedEntity, error) {
	result, err := extractor.Extract(ctx, reportID)
	if err != nil {
		return nil, err
	}

	return result.Data.Entities, nil
}

// ExtractSections extracts all report sections with their referenced data hydrated.
//
// This convenience method provides direct access to all sections in a report,
// with any entities and assets they reference fully hydrated. It's useful for
// understanding the report structure and accessing section-specific data.
//
// Section types include:
//   - SECTION_TYPE_NARRATIVE: Free-text description
//   - SECTION_TYPE_INCIDENT_DETAILS: Structured incident info
//   - SECTION_TYPE_ENTITY_LIST_*: Lists of people/vehicles/orgs
//   - SECTION_TYPE_ARREST: Arrest information
//   - SECTION_TYPE_OFFENSE: Offense details
//   - SECTION_TYPE_MEDIA: Media attachments
//
// Each section includes:
//   - Complete section content based on type
//   - All entities referenced in the section
//   - All assets referenced in the section
//   - Extraction context metadata
//
// Parameters:
//   - ctx: Context for cancellation and timeouts
//   - reportID: Unique identifier of the report
//
// Returns:
//   - []*types.HydratedSection: All sections with hydrated references
//   - error: Extraction errors (returns empty slice if no sections found)
//
// Example:
//
//	sections, err := extractor.ExtractSections(ctx, "report-123")
//	if err != nil {
//	    return fmt.Errorf("failed to extract sections: %w", err)
//	}
//	for _, section := range sections {
//	    fmt.Printf("Section %s has %d entities and %d assets\n",
//	        section.Section.Type,
//	        len(section.ReferencedEntities),
//	        len(section.ReferencedAssets))
//	}
func (extractor *ReportDataExtractor) ExtractSections(
	ctx context.Context,
	reportID string,
) ([]*types.HydratedSection, error) {
	result, err := extractor.Extract(ctx, reportID)
	if err != nil {
		return nil, err
	}

	return result.Data.Sections, nil
}

// ExtractRelationships extracts all relationships with resolved object names.
//
// This convenience method provides direct access to all relationships defined
// in a report, with human-readable names resolved for both objects. It's useful
// for understanding connections between entities, assets, and situations.
//
// Common relationship types:
//   - "RELATION_TYPE_VICTIM_OFFENDER_ST": Victim-offender connection
//   - "worked_with": Professional relationship
//   - "used_in_crime": Object used in criminal activity
//   - "employs": Employment relationship
//   - "assigned_to": Assignment (e.g., detective to case)
//
// Each relationship includes:
//   - Complete relation data with type and description
//   - Resolved names for both objects (from/to)
//   - Object types for easy filtering
//   - Extraction context metadata
//
// Parameters:
//   - ctx: Context for cancellation and timeouts
//   - reportID: Unique identifier of the report
//
// Returns:
//   - []*types.HydratedRelationship: All relationships with names
//   - error: Extraction errors (returns empty slice if no relationships)
//
// Example:
//
//	relationships, err := extractor.ExtractRelationships(ctx, "report-123")
//	if err != nil {
//	    return fmt.Errorf("failed to extract relationships: %w", err)
//	}
//	for _, rel := range relationships {
//	    fmt.Printf("%s %s %s\n",
//	        rel.FromObjectName,
//	        rel.Relation.RelationType,
//	        rel.ToObjectName)
//	}
func (extractor *ReportDataExtractor) ExtractRelationships(
	ctx context.Context,
	reportID string,
) ([]*types.HydratedRelationship, error) {
	result, err := extractor.Extract(ctx, reportID)
	if err != nil {
		return nil, err
	}

	return result.Data.Relationships, nil
}
