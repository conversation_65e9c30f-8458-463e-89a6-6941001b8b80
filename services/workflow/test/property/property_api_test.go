package test

import (
	"context"
	"fmt"
	"math"
	"net/http"
	"strconv"
	"testing"
	"time"

	property "proto/hero/property/v1"
	propertyConnect "proto/hero/property/v1/propertyconnect"

	"sync"

	"connectrpc.com/connect"
	"google.golang.org/protobuf/types/known/structpb"
)

// Helper function to safely convert int to int32 without overflow
func clampToInt32(value int) int32 {
	if value > math.MaxInt32 {
		return math.MaxInt32
	}
	if value < math.MinInt32 {
		return math.MinInt32
	}
	return int32(value)
}

// TestPropertyAPI_CRUD tests the basic CRUD operations for properties
func TestPropertyAPI_CRUD(t *testing.T) {
	httpClient := http.DefaultClient
	AddAuthHeader(httpClient)
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	propertyClient := propertyConnect.NewPropertyServiceClient(httpClient, ServiceBaseURL)

	t.Logf("%s", <PERSON><PERSON>("🏠 Testing Property API CRUD Operations"))

	var createdPropertyID string

	// Test 1: CreateProperty
	t.Run("CreateProperty", func(t *testing.T) {
		t.Logf("%s", Subheader("Testing CreateProperty"))

		// Create a test property
		testProperty := &property.Property{
			OrgId:            1,
			PropertyNumber:   "TEST-PROP-001",
			PropertyStatus:   property.PropertyStatus_PROPERTY_STATUS_IN_CUSTODY,
			IsEvidence:       true,
			RetentionPeriod:  "30 days",
			DisposalType:     property.PropertyDisposalType_PROPERTY_DISPOSAL_TYPE_UNSPECIFIED,
			Notes:            "Test property created by API test",
			CurrentCustodian: "Officer Smith",
			CurrentLocation:  "Evidence Room A",
			PropertySchema: &property.PropertySchema{
				Description:  "Test evidence item",
				Quantity:     "1",
				Category:     "Weapon",
				Identifiers:  "Test Manufacturer Test Model",
				Owner:        "",
				Condition:    "Good",
				SerialNumber: "SN123456789",
				Value:        "1000.00",
				PropertyType: property.PropertyType_PROPERTY_TYPE_SEIZED, // Moved to schema
			},
		}

		req := &property.CreatePropertyRequest{
			Property: testProperty,
		}

		resp, err := propertyClient.CreateProperty(ctx, connect.NewRequest(req))
		if err != nil {
			t.Logf("%s", Failure("CreateProperty failed"))
			t.Logf("%s", Info("Error: "+err.Error()))
			t.Fatalf("CreateProperty failed: %v", err)
		}

		createdPropertyID = resp.Msg.Property.Id
		t.Logf("%s", Success("CreateProperty successful"))
		t.Logf("%s", Info(fmt.Sprintf("Created property ID: %s", createdPropertyID)))

		// Verify the created property has required fields
		if createdPropertyID == "" {
			t.Fatalf("Created property ID is empty")
		}
		if resp.Msg.Property.OrgId != testProperty.OrgId {
			t.Errorf("Expected OrgId %d, got %d", testProperty.OrgId, resp.Msg.Property.OrgId)
		}
		if resp.Msg.Property.PropertySchema.PropertyType != testProperty.PropertySchema.PropertyType {
			t.Errorf("Expected PropertyType %v, got %v", testProperty.PropertySchema.PropertyType, resp.Msg.Property.PropertySchema.PropertyType)
		}
	})

	// Test 2: GetProperty
	t.Run("GetProperty", func(t *testing.T) {
		t.Logf("%s", Subheader("Testing GetProperty"))

		if createdPropertyID == "" {
			t.Skip("Skipping GetProperty test - no property was created")
		}

		req := &property.GetPropertyRequest{
			Id: createdPropertyID,
		}

		resp, err := propertyClient.GetProperty(ctx, connect.NewRequest(req))
		if err != nil {
			t.Logf("%s", Failure("GetProperty failed"))
			t.Logf("%s", Info("Error: "+err.Error()))
			t.Fatalf("GetProperty failed: %v", err)
		}

		t.Logf("%s", Success("GetProperty successful"))
		t.Logf("%s", Info(fmt.Sprintf("Retrieved property ID: %s", resp.Msg.Property.Id)))

		// Verify the retrieved property
		if resp.Msg.Property.Id != createdPropertyID {
			t.Errorf("Expected property ID %s, got %s", createdPropertyID, resp.Msg.Property.Id)
		}
		if resp.Msg.Property.OrgId != 1 {
			t.Errorf("Expected OrgId 1, got %d", resp.Msg.Property.OrgId)
		}
	})

	// Test 3: UpdateProperty
	t.Run("UpdateProperty", func(t *testing.T) {
		t.Logf("%s", Subheader("Testing UpdateProperty"))

		if createdPropertyID == "" {
			t.Skip("Skipping UpdateProperty test - no property was created")
		}

		// First get the current property
		getReq := &property.GetPropertyRequest{
			Id: createdPropertyID,
		}
		getResp, err := propertyClient.GetProperty(ctx, connect.NewRequest(getReq))
		if err != nil {
			t.Fatalf("Failed to get property for update: %v", err)
		}

		// Update the property
		updatedProperty := getResp.Msg.Property
		updatedProperty.Notes = "Updated notes from API test"
		updatedProperty.CurrentLocation = "Evidence Room B"
		updatedProperty.PropertyStatus = property.PropertyStatus_PROPERTY_STATUS_CHECKED_OUT

		req := &property.UpdatePropertyRequest{
			Property: updatedProperty,
		}

		resp, err := propertyClient.UpdateProperty(ctx, connect.NewRequest(req))
		if err != nil {
			t.Logf("%s", Failure("UpdateProperty failed"))
			t.Logf("%s", Info("Error: "+err.Error()))
			t.Fatalf("UpdateProperty failed: %v", err)
		}

		t.Logf("%s", Success("UpdateProperty successful"))
		t.Logf("%s", Info(fmt.Sprintf("Updated property ID: %s", resp.Msg.Property.Id)))

		// Verify the update
		if resp.Msg.Property.Notes != "Updated notes from API test" {
			t.Errorf("Expected updated notes, got %s", resp.Msg.Property.Notes)
		}
		if resp.Msg.Property.CurrentLocation != "Evidence Room B" {
			t.Errorf("Expected updated location, got %s", resp.Msg.Property.CurrentLocation)
		}
		if resp.Msg.Property.PropertyStatus != property.PropertyStatus_PROPERTY_STATUS_CHECKED_OUT {
			t.Errorf("Expected updated status, got %v", resp.Msg.Property.PropertyStatus)
		}
	})

	// Test 4: DeleteProperty
	t.Run("DeleteProperty", func(t *testing.T) {
		t.Logf("%s", Subheader("Testing DeleteProperty"))

		if createdPropertyID == "" {
			t.Skip("Skipping DeleteProperty test - no property was created")
		}

		req := &property.DeletePropertyRequest{
			Id: createdPropertyID,
		}

		_, err := propertyClient.DeleteProperty(ctx, connect.NewRequest(req))
		if err != nil {
			t.Logf("%s", Failure("DeleteProperty failed"))
			t.Logf("%s", Info("Error: "+err.Error()))
			t.Fatalf("DeleteProperty failed: %v", err)
		}

		t.Logf("%s", Success("DeleteProperty successful"))

		// Verify the property is deleted by trying to get it
		getReq := &property.GetPropertyRequest{
			Id: createdPropertyID,
		}
		_, getErr := propertyClient.GetProperty(ctx, connect.NewRequest(getReq))
		if getErr == nil {
			t.Errorf("Property should not be found after deletion")
		} else {
			t.Logf("%s", Success("Property correctly not found after deletion"))
		}
	})

	t.Logf("%s", Success("All CRUD tests completed successfully"))
}

// TestPropertyAPI_ListAndSearch tests ListProperties and SearchProperties
func TestPropertyAPI_ListAndSearch(t *testing.T) {
	httpClient := http.DefaultClient
	AddAuthHeader(httpClient)
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	propertyClient := propertyConnect.NewPropertyServiceClient(httpClient, ServiceBaseURL)

	t.Logf("%s", Header("📋 Testing Property List and Search Operations"))

	// Test 1: ListProperties
	t.Run("ListProperties", func(t *testing.T) {
		t.Logf("%s", Subheader("Testing ListProperties"))

		req := &property.ListPropertiesRequest{
			PageSize:       10,
			PropertyType:   property.PropertyType_PROPERTY_TYPE_UNSPECIFIED,
			PropertyStatus: property.PropertyStatus_PROPERTY_STATUS_UNSPECIFIED,
			OrderBy:        "create_time DESC",
		}

		resp, err := propertyClient.ListProperties(ctx, connect.NewRequest(req))
		if err != nil {
			t.Logf("%s", Failure("ListProperties failed"))
			t.Logf("%s", Info("Error: "+err.Error()))
			t.Fatalf("ListProperties failed: %v", err)
		}

		t.Logf("%s", Success("ListProperties successful"))
		t.Logf("%s", Info(fmt.Sprintf("Found %d properties", len(resp.Msg.Properties))))

		// Verify response structure
		if resp.Msg.Properties == nil {
			t.Logf("%s", Warning("Properties slice is nil (expected if no properties exist)"))
		} else {
			t.Logf("%s", Success("Properties slice is properly initialized"))
		}
	})

	// Test 2: ListProperties with filters
	t.Run("ListPropertiesWithFilters", func(t *testing.T) {
		t.Logf("%s", Subheader("Testing ListProperties with filters"))

		req := &property.ListPropertiesRequest{
			PageSize:       5,
			PropertyType:   property.PropertyType_PROPERTY_TYPE_SEIZED,
			PropertyStatus: property.PropertyStatus_PROPERTY_STATUS_IN_CUSTODY,
			OrderBy:        "create_time DESC",
		}

		resp, err := propertyClient.ListProperties(ctx, connect.NewRequest(req))
		if err != nil {
			t.Logf("%s", Failure("ListProperties with filters failed"))
			t.Logf("%s", Info("Error: "+err.Error()))
			t.Fatalf("ListProperties with filters failed: %v", err)
		}

		t.Logf("%s", Success("ListProperties with filters successful"))
		t.Logf("%s", Info(fmt.Sprintf("Found %d properties with filters", len(resp.Msg.Properties))))
	})

	// Test 3: SearchProperties
	t.Run("SearchProperties", func(t *testing.T) {
		t.Logf("%s", Subheader("Testing SearchProperties"))

		req := &property.SearchPropertiesRequest{
			Query:    "evidence",
			PageSize: 10,
			OrderBy:  property.SearchOrderBy_SEARCH_ORDER_BY_CREATED_AT,
		}

		resp, err := propertyClient.SearchProperties(ctx, connect.NewRequest(req))
		if err != nil {
			t.Logf("%s", Failure("SearchProperties failed"))
			t.Logf("%s", Info("Error: "+err.Error()))
			t.Fatalf("SearchProperties failed: %v", err)
		}

		t.Logf("%s", Success("SearchProperties successful"))
		t.Logf("%s", Info(fmt.Sprintf("Found %d properties matching 'evidence'", len(resp.Msg.Properties))))
		t.Logf("%s", Info(fmt.Sprintf("Total count: %d", resp.Msg.TotalCount)))
	})

	// Test 4: SearchProperties with field queries
	t.Run("SearchPropertiesWithFieldQueries", func(t *testing.T) {
		t.Logf("%s", Subheader("Testing SearchProperties with field queries"))

		req := &property.SearchPropertiesRequest{
			Query: "",
			FieldQueries: []*property.FieldQuery{
				{
					Field: "property_type",
					Query: "SEIZED",
				},
			},
			PageSize: 5,
		}

		resp, err := propertyClient.SearchProperties(ctx, connect.NewRequest(req))
		if err != nil {
			t.Logf("%s", Failure("SearchProperties with field queries failed"))
			t.Logf("%s", Info("Error: "+err.Error()))
			t.Fatalf("SearchProperties with field queries failed: %v", err)
		}

		t.Logf("%s", Success("SearchProperties with field queries successful"))
		t.Logf("%s", Info(fmt.Sprintf("Found %d properties with field queries", len(resp.Msg.Properties))))
	})

	t.Logf("%s", Success("All list and search tests completed successfully"))
}

// TestPropertyAPI_BatchOperations tests BatchGetProperties
func TestPropertyAPI_BatchOperations(t *testing.T) {
	httpClient := http.DefaultClient
	AddAuthHeader(httpClient)
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	propertyClient := propertyConnect.NewPropertyServiceClient(httpClient, ServiceBaseURL)

	t.Logf("%s", Header("📦 Testing Property Batch Operations"))

	// Create test properties for batch operations
	var propertyIDs []string

	// Create a few test properties
	for i := 0; i < 3; i++ {
		testProperty := &property.Property{
			OrgId:           1,
			PropertyNumber:  fmt.Sprintf("BATCH-PROP-%03d", i+1),
			PropertyStatus:  property.PropertyStatus_PROPERTY_STATUS_IN_CUSTODY,
			IsEvidence:      true,
			RetentionPeriod: "30 days",
			DisposalType:    property.PropertyDisposalType_PROPERTY_DISPOSAL_TYPE_UNSPECIFIED,
			Notes:           fmt.Sprintf("Test property %d for batch operations", i+1),
			CurrentLocation: fmt.Sprintf("Evidence Room %c", 'A'+i),
			PropertySchema: &property.PropertySchema{
				Description:  fmt.Sprintf("Test evidence item %d", i+1),
				Quantity:     "1",
				Category:     "Weapon",
				Identifiers:  "Test Manufacturer",
				Owner:        "",
				Condition:    "Good",
				SerialNumber: fmt.Sprintf("SN%d", 123456789+i),
				Value:        strconv.FormatInt(int64(100000+i*10000), 10), // $1000.00 + $100.00 increments in cents
				PropertyType: property.PropertyType_PROPERTY_TYPE_SEIZED,   // Moved to schema
			},
		}

		req := &property.CreatePropertyRequest{
			Property: testProperty,
		}

		resp, err := propertyClient.CreateProperty(ctx, connect.NewRequest(req))
		if err != nil {
			t.Fatalf("Failed to create test property %d: %v", i+1, err)
		}

		propertyIDs = append(propertyIDs, resp.Msg.Property.Id)
	}

	// Test BatchGetProperties
	t.Run("BatchGetProperties", func(t *testing.T) {
		t.Logf("%s", Subheader("Testing BatchGetProperties"))

		if len(propertyIDs) == 0 {
			t.Skip("Skipping BatchGetProperties test - no properties were created")
		}

		req := &property.BatchGetPropertiesRequest{
			Ids: propertyIDs,
		}

		resp, err := propertyClient.BatchGetProperties(ctx, connect.NewRequest(req))
		if err != nil {
			t.Logf("%s", Failure("BatchGetProperties failed"))
			t.Logf("%s", Info("Error: "+err.Error()))
			t.Fatalf("BatchGetProperties failed: %v", err)
		}

		t.Logf("%s", Success("BatchGetProperties successful"))
		t.Logf("%s", Info(fmt.Sprintf("Retrieved %d properties", len(resp.Msg.Properties))))

		// Filter properties to only include those created by this test
		var testProperties []*property.Property
		expectedIDs := make(map[string]bool)
		for _, id := range propertyIDs {
			expectedIDs[id] = true
		}

		for _, prop := range resp.Msg.Properties {
			if expectedIDs[prop.Id] {
				testProperties = append(testProperties, prop)
			}
		}

		// Verify we got the expected number of test properties
		if len(testProperties) != len(propertyIDs) {
			t.Errorf("Expected %d test properties, got %d (total properties in response: %d)", len(propertyIDs), len(testProperties), len(resp.Msg.Properties))
		}

		// Verify each expected property ID is in the response
		foundIDs := make(map[string]bool)
		for _, prop := range testProperties {
			foundIDs[prop.Id] = true
		}

		for _, expectedID := range propertyIDs {
			if !foundIDs[expectedID] {
				t.Errorf("Expected property ID %s not found in batch response", expectedID)
			}
		}
	})

	// Clean up test properties
	t.Run("CleanupTestProperties", func(t *testing.T) {
		t.Logf("%s", Subheader("Cleaning up test properties"))

		for _, propertyID := range propertyIDs {
			req := &property.DeletePropertyRequest{
				Id: propertyID,
			}
			_, err := propertyClient.DeleteProperty(ctx, connect.NewRequest(req))
			if err != nil {
				t.Logf("%s", Warning(fmt.Sprintf("Failed to delete test property %s: %v", propertyID, err)))
			}
		}

		t.Logf("%s", Success("Cleanup completed"))
	})

	t.Logf("%s", Success("All batch operation tests completed successfully"))
}

// TestPropertyAPI_CustodyChain tests custody chain operations
func TestPropertyAPI_CustodyChain(t *testing.T) {
	httpClient := http.DefaultClient
	AddAuthHeader(httpClient)
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	propertyClient := propertyConnect.NewPropertyServiceClient(httpClient, ServiceBaseURL)

	t.Logf("%s", Header("🔗 Testing Property Custody Chain Operations"))

	var createdPropertyID string

	// Create a test property for custody chain tests
	t.Run("CreatePropertyForCustody", func(t *testing.T) {
		t.Logf("%s", Subheader("Creating test property for custody chain tests"))

		testProperty := &property.Property{
			OrgId:           1,
			PropertyNumber:  "CUSTODY-TEST-001",
			PropertyStatus:  property.PropertyStatus_PROPERTY_STATUS_IN_CUSTODY,
			IsEvidence:      true,
			RetentionPeriod: "30 days",
			DisposalType:    property.PropertyDisposalType_PROPERTY_DISPOSAL_TYPE_UNSPECIFIED,
			Notes:           "Test property for custody chain",
			CurrentLocation: "Evidence Room A",
			PropertySchema: &property.PropertySchema{
				Description:  "Test evidence for custody chain",
				Quantity:     "1",
				Category:     "Weapon",
				Identifiers:  "Test Manufacturer",
				Owner:        "",
				Condition:    "Good",
				SerialNumber: "SN123456789",
				Value:        "1500.00",
				PropertyType: property.PropertyType_PROPERTY_TYPE_SEIZED, // Moved to schema
			},
		}

		req := &property.CreatePropertyRequest{
			Property: testProperty,
		}

		resp, err := propertyClient.CreateProperty(ctx, connect.NewRequest(req))
		if err != nil {
			t.Fatalf("Failed to create test property for custody chain: %v", err)
		}

		createdPropertyID = resp.Msg.Property.Id
		t.Logf("%s", Success("Created test property for custody chain"))
		t.Logf("%s", Info(fmt.Sprintf("Property ID: %s", createdPropertyID)))
	})

	// Test AddCustodyEvent
	t.Run("AddCustodyEvent", func(t *testing.T) {
		t.Logf("%s", Subheader("Testing AddCustodyEvent"))

		if createdPropertyID == "" {
			t.Skip("Skipping AddCustodyEvent test - no property was created")
		}

		custodyEvent := &property.CustodyEvent{
			ActionType:         property.CustodyActionType_CUSTODY_ACTION_TYPE_COLLECTED,
			TransferringUserId: "officer-001",
			TransferringAgency: "Police Department",
			ReceivingUserId:    "officer-002",
			ReceivingAgency:    "Police Department",
			NewLocation:        "Evidence Room A",
			Timestamp:          time.Now().UTC().Format(time.RFC3339),
			Notes:              "Property received from crime scene",
			EvidenceNumber:     "EVD-001",
		}

		req := &property.AddCustodyEventRequest{
			PropertyId:   createdPropertyID,
			CustodyEvent: custodyEvent,
		}

		_, err := propertyClient.AddCustodyEvent(ctx, connect.NewRequest(req))
		if err != nil {
			t.Logf("%s", Failure("AddCustodyEvent failed"))
			t.Logf("%s", Info("Error: "+err.Error()))
			t.Fatalf("AddCustodyEvent failed: %v", err)
		}

		t.Logf("%s", Success("AddCustodyEvent successful"))
	})

	// Test GetCustodyChain
	t.Run("GetCustodyChain", func(t *testing.T) {
		t.Logf("%s", Subheader("Testing GetCustodyChain"))

		if createdPropertyID == "" {
			t.Skip("Skipping GetCustodyChain test - no property was created")
		}

		req := &property.GetCustodyChainRequest{
			PropertyId: createdPropertyID,
		}

		resp, err := propertyClient.GetCustodyChain(ctx, connect.NewRequest(req))
		if err != nil {
			t.Logf("%s", Failure("GetCustodyChain failed"))
			t.Logf("%s", Info("Error: "+err.Error()))
			t.Fatalf("GetCustodyChain failed: %v", err)
		}

		t.Logf("%s", Success("GetCustodyChain successful"))
		t.Logf("%s", Info(fmt.Sprintf("Found %d custody events", len(resp.Msg.CustodyChain))))

		// Verify we have at least one custody event
		if len(resp.Msg.CustodyChain) == 0 {
			t.Logf("%s", Warning("No custody events found (this might be expected)"))
		} else {
			t.Logf("%s", Success("Custody chain contains events"))
		}
	})

	// Clean up
	t.Run("CleanupCustodyProperty", func(t *testing.T) {
		t.Logf("%s", Subheader("Cleaning up custody test property"))

		if createdPropertyID != "" {
			req := &property.DeletePropertyRequest{
				Id: createdPropertyID,
			}
			_, err := propertyClient.DeleteProperty(ctx, connect.NewRequest(req))
			if err != nil {
				t.Logf("%s", Warning(fmt.Sprintf("Failed to delete custody test property: %v", err)))
			} else {
				t.Logf("%s", Success("Custody test property cleaned up"))
			}
		}
	})

	t.Logf("%s", Success("All custody chain tests completed successfully"))
}

// TestPropertyAPI_StatusAndPermissions tests status updates and permissions
func TestPropertyAPI_StatusAndPermissions(t *testing.T) {
	httpClient := http.DefaultClient
	AddAuthHeader(httpClient)
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	propertyClient := propertyConnect.NewPropertyServiceClient(httpClient, ServiceBaseURL)

	t.Logf("%s", Header("🔐 Testing Property Status and Permissions"))

	var createdPropertyID string

	// Create a test property for status and permissions tests
	t.Run("CreatePropertyForStatus", func(t *testing.T) {
		t.Logf("%s", Subheader("Creating test property for status and permissions tests"))

		testProperty := &property.Property{
			OrgId:            1,
			PropertyNumber:   "STATUS-TEST-001",
			PropertyStatus:   property.PropertyStatus_PROPERTY_STATUS_IN_CUSTODY,
			IsEvidence:       true,
			RetentionPeriod:  "30 days",
			DisposalType:     property.PropertyDisposalType_PROPERTY_DISPOSAL_TYPE_UNSPECIFIED,
			Notes:            "Test property for status and permissions",
			CurrentCustodian: "test-officer-status",
			CurrentLocation:  "Evidence Room A",
			PropertySchema: &property.PropertySchema{
				Description:  "Test evidence for status and permissions",
				Quantity:     "1",
				Category:     "Weapon",
				Identifiers:  "Test Manufacturer",
				Owner:        "",
				Condition:    "Good",
				SerialNumber: "SN123456789",
				Value:        "2000.00",
				PropertyType: property.PropertyType_PROPERTY_TYPE_SEIZED, // Moved to schema
			},
		}

		req := &property.CreatePropertyRequest{
			Property: testProperty,
		}

		resp, err := propertyClient.CreateProperty(ctx, connect.NewRequest(req))
		if err != nil {
			t.Fatalf("Failed to create test property for status and permissions: %v", err)
		}

		createdPropertyID = resp.Msg.Property.Id
		t.Logf("%s", Success("Created test property for status and permissions"))
		t.Logf("%s", Info(fmt.Sprintf("Property ID: %s", createdPropertyID)))
	})

	// Clean up
	t.Run("CleanupStatusProperty", func(t *testing.T) {
		t.Logf("%s", Subheader("Cleaning up status test property"))

		if createdPropertyID != "" {
			req := &property.DeletePropertyRequest{
				Id: createdPropertyID,
			}
			_, err := propertyClient.DeleteProperty(ctx, connect.NewRequest(req))
			if err != nil {
				t.Logf("%s", Warning(fmt.Sprintf("Failed to delete status test property: %v", err)))
			} else {
				t.Logf("%s", Success("Status test property cleaned up"))
			}
		}
	})

	t.Logf("%s", Success("All status and permissions tests completed successfully"))
}

// TestPropertyAPI_ErrorHandling tests error conditions
func TestPropertyAPI_ErrorHandling(t *testing.T) {
	httpClient := http.DefaultClient
	AddAuthHeader(httpClient)
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	propertyClient := propertyConnect.NewPropertyServiceClient(httpClient, ServiceBaseURL)

	t.Logf("%s", Header("⚠️ Testing Property API Error Handling"))

	// Test GetProperty with non-existent ID
	t.Run("GetPropertyNonExistent", func(t *testing.T) {
		t.Logf("%s", Subheader("Testing GetProperty with non-existent ID"))

		req := &property.GetPropertyRequest{
			Id: "non-existent-property-id",
		}

		_, err := propertyClient.GetProperty(ctx, connect.NewRequest(req))
		if err == nil {
			t.Fatalf("Expected error for non-existent property, got none")
		}

		t.Logf("%s", Success("GetProperty correctly returned error for non-existent property"))
		t.Logf("%s", Info("Error: "+err.Error()))
	})

	// Test DeleteProperty with non-existent ID
	t.Run("DeletePropertyNonExistent", func(t *testing.T) {
		t.Logf("%s", Subheader("Testing DeleteProperty with non-existent ID"))

		req := &property.DeletePropertyRequest{
			Id: "non-existent-property-id",
		}

		_, err := propertyClient.DeleteProperty(ctx, connect.NewRequest(req))
		if err == nil {
			t.Fatalf("Expected error for non-existent property deletion, got none")
		}

		t.Logf("%s", Success("DeleteProperty correctly returned error for non-existent property"))
		t.Logf("%s", Info("Error: "+err.Error()))
	})

	// Test CreateProperty with invalid data
	t.Run("CreatePropertyInvalidData", func(t *testing.T) {
		t.Logf("%s", Subheader("Testing CreateProperty with invalid data"))

		// Create property without required fields
		invalidProperty := &property.Property{
			// Missing OrgId, PropertyStatus (PropertyType now in schema)
			Notes: "Invalid property",
		}

		req := &property.CreatePropertyRequest{
			Property: invalidProperty,
		}

		_, err := propertyClient.CreateProperty(ctx, connect.NewRequest(req))
		if err == nil {
			t.Fatalf("Expected error for invalid property creation, got none")
		}

		t.Logf("%s", Success("CreateProperty correctly returned error for invalid data"))
		t.Logf("%s", Info("Error: "+err.Error()))
	})

	// Test UpdateProperty with non-existent ID
	t.Run("UpdatePropertyNonExistent", func(t *testing.T) {
		t.Logf("%s", Subheader("Testing UpdateProperty with non-existent ID"))

		nonExistentProperty := &property.Property{
			Id:             "non-existent-property-id",
			OrgId:          1,
			PropertyNumber: "NON-EXISTENT-001",
			PropertyStatus: property.PropertyStatus_PROPERTY_STATUS_IN_CUSTODY,
			Notes:          "This property doesn't exist",
			PropertySchema: &property.PropertySchema{
				PropertyType: property.PropertyType_PROPERTY_TYPE_SEIZED,
			},
		}

		req := &property.UpdatePropertyRequest{
			Property: nonExistentProperty,
		}

		_, err := propertyClient.UpdateProperty(ctx, connect.NewRequest(req))
		if err == nil {
			t.Fatalf("Expected error for updating non-existent property, got none")
		}

		t.Logf("%s", Success("UpdateProperty correctly returned error for non-existent property"))
		t.Logf("%s", Info("Error: "+err.Error()))
	})

	t.Logf("%s", Success("All error handling tests completed successfully"))
}

// TestPropertyAPI_ConcurrentOperations tests concurrent operations for thread safety
func TestPropertyAPI_ConcurrentOperations(t *testing.T) {
	httpClient := http.DefaultClient
	AddAuthHeader(httpClient)
	ctx, cancel := context.WithTimeout(context.Background(), 120*time.Second)
	defer cancel()

	propertyClient := propertyConnect.NewPropertyServiceClient(httpClient, ServiceBaseURL)

	t.Logf("%s", Header("🔄 Testing Property API Concurrent Operations"))

	// Track created properties for cleanup
	var createdPropertyIDs []string
	var propertyIDsMutex sync.Mutex

	// Helper function to safely add property ID
	addPropertyID := func(id string) {
		propertyIDsMutex.Lock()
		defer propertyIDsMutex.Unlock()
		createdPropertyIDs = append(createdPropertyIDs, id)
	}

	// Test concurrent property creation
	t.Run("ConcurrentCreate", func(t *testing.T) {
		t.Logf("%s", Subheader("Testing concurrent property creation"))

		const numGoroutines = 10
		var wg sync.WaitGroup
		var createErrors []error
		var createErrorsMutex sync.Mutex

		// Helper function to safely add error
		addError := func(err error) {
			createErrorsMutex.Lock()
			defer createErrorsMutex.Unlock()
			createErrors = append(createErrors, err)
		}

		// Launch concurrent create operations
		for i := 0; i < numGoroutines; i++ {
			wg.Add(1)
			go func(index int) {
				defer wg.Done()

				testProperty := &property.Property{
					OrgId:            1,
					PropertyNumber:   fmt.Sprintf("CONC-PROP-%03d", index+1),
					PropertyStatus:   property.PropertyStatus_PROPERTY_STATUS_IN_CUSTODY,
					IsEvidence:       true,
					RetentionPeriod:  "30 days",
					DisposalType:     property.PropertyDisposalType_PROPERTY_DISPOSAL_TYPE_UNSPECIFIED,
					Notes:            fmt.Sprintf("Concurrent test property %d", index+1),
					CurrentCustodian: fmt.Sprintf("officer-concurrent-%d", index+1),
					CurrentLocation:  fmt.Sprintf("Evidence Room %c", 'A'+index%5),
					PropertySchema: &property.PropertySchema{
						Description:  fmt.Sprintf("Concurrent test evidence %d", index+1),
						Quantity:     "1",
						Category:     "Weapon",
						Identifiers:  "Test Manufacturer",
						Owner:        "",
						Condition:    "Good",
						SerialNumber: fmt.Sprintf("SN-CONC-%d", 123456789+index),
						Value:        strconv.FormatInt(int64(100000+index*1000), 10), // $1000.00 + increments in cents
						PropertyType: property.PropertyType_PROPERTY_TYPE_SEIZED,      // Moved to schema
					},
				}

				req := &property.CreatePropertyRequest{
					Property: testProperty,
				}

				resp, err := propertyClient.CreateProperty(ctx, connect.NewRequest(req))
				if err != nil {
					addError(fmt.Errorf("concurrent create %d failed: %w", index+1, err))
					return
				}

				if resp.Msg.Property.Id == "" {
					addError(fmt.Errorf("concurrent create %d returned empty ID", index+1))
					return
				}

				addPropertyID(resp.Msg.Property.Id)
				t.Logf("%s", Info(fmt.Sprintf("Concurrent create %d successful: %s", index+1, resp.Msg.Property.Id)))
			}(i)
		}

		wg.Wait()

		// Report results
		if len(createErrors) > 0 {
			t.Logf("%s", Warning(fmt.Sprintf("Concurrent create completed with %d errors", len(createErrors))))
			for _, err := range createErrors {
				t.Logf("%s", Warning(fmt.Sprintf("Create error: %v", err)))
			}
		} else {
			t.Logf("%s", Success("All concurrent create operations completed successfully"))
		}

		t.Logf("%s", Info(fmt.Sprintf("Created %d properties concurrently", len(createdPropertyIDs))))
	})

	// Test concurrent property reads
	t.Run("ConcurrentRead", func(t *testing.T) {
		t.Logf("%s", Subheader("Testing concurrent property reads"))

		if len(createdPropertyIDs) == 0 {
			t.Skip("Skipping concurrent read test - no properties were created")
		}

		const numReads = 20
		var wg sync.WaitGroup
		var readErrors []error
		var readErrorsMutex sync.Mutex

		// Helper function to safely add error
		addError := func(err error) {
			readErrorsMutex.Lock()
			defer readErrorsMutex.Unlock()
			readErrors = append(readErrors, err)
		}

		// Launch concurrent read operations
		for i := 0; i < numReads; i++ {
			wg.Add(1)
			go func(index int) {
				defer wg.Done()

				// Select a property ID to read (cycle through available IDs)
				propertyID := createdPropertyIDs[index%len(createdPropertyIDs)]

				req := &property.GetPropertyRequest{
					Id: propertyID,
				}

				resp, err := propertyClient.GetProperty(ctx, connect.NewRequest(req))
				if err != nil {
					addError(fmt.Errorf("concurrent read %d failed for property %s: %w", index+1, propertyID, err))
					return
				}

				if resp.Msg.Property.Id != propertyID {
					addError(fmt.Errorf("concurrent read %d returned wrong property ID: expected %s, got %s", index+1, propertyID, resp.Msg.Property.Id))
					return
				}

				t.Logf("%s", Info(fmt.Sprintf("Concurrent read %d successful: %s", index+1, propertyID)))
			}(i)
		}

		wg.Wait()

		// Report results
		if len(readErrors) > 0 {
			t.Logf("%s", Warning(fmt.Sprintf("Concurrent read completed with %d errors", len(readErrors))))
			for _, err := range readErrors {
				t.Logf("%s", Warning(fmt.Sprintf("Read error: %v", err)))
			}
		} else {
			t.Logf("%s", Success("All concurrent read operations completed successfully"))
		}
	})

	// Test concurrent property updates
	t.Run("ConcurrentUpdate", func(t *testing.T) {
		t.Logf("%s", Subheader("Testing concurrent property updates"))

		if len(createdPropertyIDs) == 0 {
			t.Skip("Skipping concurrent update test - no properties were created")
		}

		const numUpdates = 15
		var wg sync.WaitGroup
		var updateErrors []error
		var updateErrorsMutex sync.Mutex

		// Helper function to safely add error
		addError := func(err error) {
			updateErrorsMutex.Lock()
			defer updateErrorsMutex.Unlock()
			updateErrors = append(updateErrors, err)
		}

		// Launch concurrent update operations
		for i := 0; i < numUpdates; i++ {
			wg.Add(1)
			go func(index int) {
				defer wg.Done()

				// Select a property ID to update (cycle through available IDs)
				propertyID := createdPropertyIDs[index%len(createdPropertyIDs)]

				// First get the current property
				getReq := &property.GetPropertyRequest{
					Id: propertyID,
				}
				getResp, err := propertyClient.GetProperty(ctx, connect.NewRequest(getReq))
				if err != nil {
					addError(fmt.Errorf("concurrent update %d failed to get property %s: %w", index+1, propertyID, err))
					return
				}

				// Update the property
				updatedProperty := getResp.Msg.Property
				updatedProperty.Notes = fmt.Sprintf("Updated by concurrent operation %d", index+1)
				updatedProperty.CurrentLocation = fmt.Sprintf("Updated Location %d", index+1)

				updateReq := &property.UpdatePropertyRequest{
					Property: updatedProperty,
				}

				updateResp, err := propertyClient.UpdateProperty(ctx, connect.NewRequest(updateReq))
				if err != nil {
					addError(fmt.Errorf("concurrent update %d failed for property %s: %w", index+1, propertyID, err))
					return
				}

				if updateResp.Msg.Property.Id != propertyID {
					addError(fmt.Errorf("concurrent update %d returned wrong property ID: expected %s, got %s", index+1, propertyID, updateResp.Msg.Property.Id))
					return
				}

				t.Logf("%s", Info(fmt.Sprintf("Concurrent update %d successful: %s", index+1, propertyID)))
			}(i)
		}

		wg.Wait()

		// Report results
		if len(updateErrors) > 0 {
			t.Logf("%s", Warning(fmt.Sprintf("Concurrent update completed with %d errors", len(updateErrors))))
			for _, err := range updateErrors {
				t.Logf("%s", Warning(fmt.Sprintf("Update error: %v", err)))
			}
		} else {
			t.Logf("%s", Success("All concurrent update operations completed successfully"))
		}
	})

	// Test concurrent search operations
	t.Run("ConcurrentSearch", func(t *testing.T) {
		t.Logf("%s", Subheader("Testing concurrent search operations"))

		const numSearches = 25
		var wg sync.WaitGroup
		var searchErrors []error
		var searchErrorsMutex sync.Mutex

		// Helper function to safely add error
		addError := func(err error) {
			searchErrorsMutex.Lock()
			defer searchErrorsMutex.Unlock()
			searchErrors = append(searchErrors, err)
		}

		// Launch concurrent search operations
		for i := 0; i < numSearches; i++ {
			wg.Add(1)
			go func(index int) {
				defer wg.Done()

				searchQueries := []string{
					"concurrent",
					"test",
					"evidence",
					"weapon",
					"officer",
				}

				query := searchQueries[index%len(searchQueries)]

				req := &property.SearchPropertiesRequest{
					Query:    query,
					PageSize: 10,
				}

				resp, err := propertyClient.SearchProperties(ctx, connect.NewRequest(req))
				if err != nil {
					addError(fmt.Errorf("concurrent search %d failed for query '%s': %w", index+1, query, err))
					return
				}

				// Verify response structure
				if resp.Msg.Properties == nil {
					addError(fmt.Errorf("concurrent search %d returned nil properties for query '%s'", index+1, query))
					return
				}

				// Filter properties to only count those created by this test
				var testProperties []*property.Property
				expectedIDs := make(map[string]bool)
				for _, id := range createdPropertyIDs {
					expectedIDs[id] = true
				}

				for _, prop := range resp.Msg.Properties {
					if expectedIDs[prop.Id] {
						testProperties = append(testProperties, prop)
					}
				}

				t.Logf("%s", Info(fmt.Sprintf("Concurrent search %d successful for '%s': found %d test properties (total: %d)", index+1, query, len(testProperties), len(resp.Msg.Properties))))
			}(i)
		}

		wg.Wait()

		// Report results
		if len(searchErrors) > 0 {
			t.Logf("%s", Warning(fmt.Sprintf("Concurrent search completed with %d errors", len(searchErrors))))
			for _, err := range searchErrors {
				t.Logf("%s", Warning(fmt.Sprintf("Search error: %v", err)))
			}
		} else {
			t.Logf("%s", Success("All concurrent search operations completed successfully"))
		}
	})

	// Clean up test properties
	t.Run("CleanupConcurrentProperties", func(t *testing.T) {
		t.Logf("%s", Subheader("Cleaning up concurrent test properties"))

		if len(createdPropertyIDs) == 0 {
			t.Logf("%s", Info("No properties to clean up"))
			return
		}

		var wg sync.WaitGroup
		var deleteErrors []error
		var deleteErrorsMutex sync.Mutex

		// Helper function to safely add error
		addError := func(err error) {
			deleteErrorsMutex.Lock()
			defer deleteErrorsMutex.Unlock()
			deleteErrors = append(deleteErrors, err)
		}

		// Launch concurrent delete operations
		for i, propertyID := range createdPropertyIDs {
			wg.Add(1)
			go func(index int, id string) {
				defer wg.Done()

				req := &property.DeletePropertyRequest{
					Id: id,
				}

				_, err := propertyClient.DeleteProperty(ctx, connect.NewRequest(req))
				if err != nil {
					addError(fmt.Errorf("concurrent delete %d failed for property %s: %w", index+1, id, err))
					return
				}

				t.Logf("%s", Info(fmt.Sprintf("Concurrent delete %d successful: %s", index+1, id)))
			}(i, propertyID)
		}

		wg.Wait()

		// Report cleanup results
		if len(deleteErrors) > 0 {
			t.Logf("%s", Warning(fmt.Sprintf("Concurrent cleanup completed with %d errors", len(deleteErrors))))
			for _, err := range deleteErrors {
				t.Logf("%s", Warning(fmt.Sprintf("Delete error: %v", err)))
			}
		} else {
			t.Logf("%s", Success("All concurrent cleanup operations completed successfully"))
		}
	})

	t.Logf("%s", Success("All concurrent operations tests completed successfully"))
}

// TestPropertyAPI_MediaOperations tests the file attachment operations for properties
func TestPropertyAPI_MediaOperations(t *testing.T) {
	httpClient := http.DefaultClient
	AddAuthHeader(httpClient)
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	propertyClient := propertyConnect.NewPropertyServiceClient(httpClient, ServiceBaseURL)

	t.Logf("%s", Header("📎 Testing Property Media Operations"))

	var createdPropertyID string
	var createdAttachmentID string

	// Test 1: Create a property for testing media operations
	t.Run("CreatePropertyForMedia", func(t *testing.T) {
		t.Logf("%s", Subheader("Creating test property for media operations"))

		testProperty := &property.Property{
			OrgId:            1,
			PropertyNumber:   "TEST-MEDIA-PROP-001",
			PropertyStatus:   property.PropertyStatus_PROPERTY_STATUS_IN_CUSTODY,
			IsEvidence:       true,
			RetentionPeriod:  "30 days",
			DisposalType:     property.PropertyDisposalType_PROPERTY_DISPOSAL_TYPE_UNSPECIFIED,
			Notes:            "Test property for media operations",
			CurrentCustodian: "Officer Smith",
			CurrentLocation:  "Evidence Room A",
			PropertySchema: &property.PropertySchema{
				Description:  "Test evidence item for media testing",
				Quantity:     "1",
				Category:     "Weapon",
				Identifiers:  "Test Manufacturer Test Model",
				Owner:        "",
				Condition:    "Good",
				SerialNumber: "SN123456789",
				Value:        "1000.00",
				PropertyType: property.PropertyType_PROPERTY_TYPE_SEIZED,
			},
		}

		req := &property.CreatePropertyRequest{
			Property: testProperty,
		}

		resp, err := propertyClient.CreateProperty(ctx, connect.NewRequest(req))
		if err != nil {
			t.Logf("%s", Failure("CreateProperty for media testing failed"))
			t.Logf("%s", Info("Error: "+err.Error()))
			t.Fatalf("CreateProperty for media testing failed: %v", err)
		}

		createdPropertyID = resp.Msg.Property.Id
		t.Logf("%s", Success("CreateProperty for media testing successful"))
		t.Logf("%s", Info(fmt.Sprintf("Created property ID: %s", createdPropertyID)))
	})

	// Test 2: List file attachments (should be empty initially)
	t.Run("ListPropertyFileAttachments_Empty", func(t *testing.T) {
		t.Logf("%s", Subheader("Testing ListPropertyFileAttachments with empty result"))

		if createdPropertyID == "" {
			t.Skip("Skipping ListPropertyFileAttachments test - no property was created")
		}

		req := &property.ListPropertyFileAttachmentsRequest{
			PropertyId: createdPropertyID,
			PageSize:   50,
		}

		resp, err := propertyClient.ListPropertyFileAttachments(ctx, connect.NewRequest(req))
		if err != nil {
			t.Logf("%s", Failure("ListPropertyFileAttachments failed"))
			t.Logf("%s", Info("Error: "+err.Error()))
			t.Fatalf("ListPropertyFileAttachments failed: %v", err)
		}

		t.Logf("%s", Success("ListPropertyFileAttachments successful"))
		t.Logf("%s", Info(fmt.Sprintf("Found %d file attachments", len(resp.Msg.FileAttachments))))

		// Should be empty initially
		if len(resp.Msg.FileAttachments) != 0 {
			t.Errorf("Expected 0 file attachments, got %d", len(resp.Msg.FileAttachments))
		}
	})

	// Test 3: Add file attachment
	t.Run("AddPropertyFileAttachment", func(t *testing.T) {
		t.Logf("%s", Subheader("Testing AddPropertyFileAttachment"))

		if createdPropertyID == "" {
			t.Skip("Skipping AddPropertyFileAttachment test - no property was created")
		}

		fileAttachment := &property.PropertyFileReference{
			FileId:       "test-file-id-001",
			Caption:      "Test photo of evidence",
			DisplayName:  "evidence_photo_001.jpg",
			DisplayOrder: 1,
			FileCategory: "photo",
			Metadata:     &structpb.Struct{}, // Use empty struct instead of nil
		}

		req := &property.AddPropertyFileAttachmentRequest{
			PropertyId:     createdPropertyID,
			FileAttachment: fileAttachment,
		}

		resp, err := propertyClient.AddPropertyFileAttachment(ctx, connect.NewRequest(req))
		if err != nil {
			t.Logf("%s", Failure("AddPropertyFileAttachment failed"))
			t.Logf("%s", Info("Error: "+err.Error()))
			t.Fatalf("AddPropertyFileAttachment failed: %v", err)
		}

		createdAttachmentID = resp.Msg.FileAttachment.Id
		t.Logf("%s", Success("AddPropertyFileAttachment successful"))
		t.Logf("%s", Info(fmt.Sprintf("Created attachment ID: %s", createdAttachmentID)))

		// Verify the created attachment has required fields
		if createdAttachmentID == "" {
			t.Fatalf("Created attachment ID is empty")
		}
		if resp.Msg.FileAttachment.PropertyId != createdPropertyID {
			t.Errorf("Expected PropertyId %s, got %s", createdPropertyID, resp.Msg.FileAttachment.PropertyId)
		}
		if resp.Msg.FileAttachment.FileId != fileAttachment.FileId {
			t.Errorf("Expected FileId %s, got %s", fileAttachment.FileId, resp.Msg.FileAttachment.FileId)
		}
		if resp.Msg.FileAttachment.Caption != fileAttachment.Caption {
			t.Errorf("Expected Caption %s, got %s", fileAttachment.Caption, resp.Msg.FileAttachment.Caption)
		}
	})

	// Test 4: List file attachments (should have one now)
	t.Run("ListPropertyFileAttachments_WithData", func(t *testing.T) {
		t.Logf("%s", Subheader("Testing ListPropertyFileAttachments with data"))

		if createdPropertyID == "" {
			t.Skip("Skipping ListPropertyFileAttachments test - no property was created")
		}

		req := &property.ListPropertyFileAttachmentsRequest{
			PropertyId: createdPropertyID,
			PageSize:   50,
		}

		resp, err := propertyClient.ListPropertyFileAttachments(ctx, connect.NewRequest(req))
		if err != nil {
			t.Logf("%s", Failure("ListPropertyFileAttachments failed"))
			t.Logf("%s", Info("Error: "+err.Error()))
			t.Fatalf("ListPropertyFileAttachments failed: %v", err)
		}

		t.Logf("%s", Success("ListPropertyFileAttachments successful"))
		t.Logf("%s", Info(fmt.Sprintf("Found %d file attachments", len(resp.Msg.FileAttachments))))

		// Should have one attachment now
		if len(resp.Msg.FileAttachments) != 1 {
			t.Errorf("Expected 1 file attachment, got %d", len(resp.Msg.FileAttachments))
		} else {
			attachment := resp.Msg.FileAttachments[0]
			if attachment.Id != createdAttachmentID {
				t.Errorf("Expected attachment ID %s, got %s", createdAttachmentID, attachment.Id)
			}
			if attachment.PropertyId != createdPropertyID {
				t.Errorf("Expected property ID %s, got %s", createdPropertyID, attachment.PropertyId)
			}
			if attachment.FileId != "test-file-id-001" {
				t.Errorf("Expected file ID %s, got %s", "test-file-id-001", attachment.FileId)
			}
		}
	})

	// Test 5: Add another file attachment with different category
	t.Run("AddPropertyFileAttachment_Second", func(t *testing.T) {
		t.Logf("%s", Subheader("Testing AddPropertyFileAttachment with second attachment"))

		if createdPropertyID == "" {
			t.Skip("Skipping AddPropertyFileAttachment test - no property was created")
		}

		fileAttachment := &property.PropertyFileReference{
			FileId:       "test-file-id-002",
			Caption:      "Test document scan",
			DisplayName:  "document_scan_001.pdf",
			DisplayOrder: 2,
			FileCategory: "document",
			Metadata:     &structpb.Struct{}, // Use empty struct instead of nil
		}

		req := &property.AddPropertyFileAttachmentRequest{
			PropertyId:     createdPropertyID,
			FileAttachment: fileAttachment,
		}

		resp, err := propertyClient.AddPropertyFileAttachment(ctx, connect.NewRequest(req))
		if err != nil {
			t.Logf("%s", Failure("AddPropertyFileAttachment failed"))
			t.Logf("%s", Info("Error: "+err.Error()))
			t.Fatalf("AddPropertyFileAttachment failed: %v", err)
		}

		t.Logf("%s", Success("AddPropertyFileAttachment successful"))
		t.Logf("%s", Info(fmt.Sprintf("Created second attachment ID: %s", resp.Msg.FileAttachment.Id)))

		// Verify the created attachment
		if resp.Msg.FileAttachment.Id == "" {
			t.Fatalf("Created attachment ID is empty")
		}
		if resp.Msg.FileAttachment.FileId != "test-file-id-002" {
			t.Errorf("Expected FileId %s, got %s", "test-file-id-002", resp.Msg.FileAttachment.FileId)
		}
	})

	// Test 6: List file attachments with category filter
	t.Run("ListPropertyFileAttachments_WithCategoryFilter", func(t *testing.T) {
		t.Logf("%s", Subheader("Testing ListPropertyFileAttachments with category filter"))

		if createdPropertyID == "" {
			t.Skip("Skipping ListPropertyFileAttachments test - no property was created")
		}

		// Test photo category
		req := &property.ListPropertyFileAttachmentsRequest{
			PropertyId:   createdPropertyID,
			FileCategory: "photo",
			PageSize:     50,
		}

		resp, err := propertyClient.ListPropertyFileAttachments(ctx, connect.NewRequest(req))
		if err != nil {
			t.Logf("%s", Failure("ListPropertyFileAttachments with photo filter failed"))
			t.Logf("%s", Info("Error: "+err.Error()))
			t.Fatalf("ListPropertyFileAttachments with photo filter failed: %v", err)
		}

		t.Logf("%s", Success("ListPropertyFileAttachments with photo filter successful"))
		t.Logf("%s", Info(fmt.Sprintf("Found %d photo attachments", len(resp.Msg.FileAttachments))))

		// Should have one photo attachment
		if len(resp.Msg.FileAttachments) != 1 {
			t.Errorf("Expected 1 photo attachment, got %d", len(resp.Msg.FileAttachments))
		} else {
			attachment := resp.Msg.FileAttachments[0]
			if attachment.FileCategory != "photo" {
				t.Errorf("Expected category 'photo', got %s", attachment.FileCategory)
			}
		}

		// Test document category
		req = &property.ListPropertyFileAttachmentsRequest{
			PropertyId:   createdPropertyID,
			FileCategory: "document",
			PageSize:     50,
		}

		resp, err = propertyClient.ListPropertyFileAttachments(ctx, connect.NewRequest(req))
		if err != nil {
			t.Logf("%s", Failure("ListPropertyFileAttachments with document filter failed"))
			t.Logf("%s", Info("Error: "+err.Error()))
			t.Fatalf("ListPropertyFileAttachments with document filter failed: %v", err)
		}

		t.Logf("%s", Success("ListPropertyFileAttachments with document filter successful"))
		t.Logf("%s", Info(fmt.Sprintf("Found %d document attachments", len(resp.Msg.FileAttachments))))

		// Should have one document attachment
		if len(resp.Msg.FileAttachments) != 1 {
			t.Errorf("Expected 1 document attachment, got %d", len(resp.Msg.FileAttachments))
		} else {
			attachment := resp.Msg.FileAttachments[0]
			if attachment.FileCategory != "document" {
				t.Errorf("Expected category 'document', got %s", attachment.FileCategory)
			}
		}
	})

	// Test 7: Remove file attachment
	t.Run("RemovePropertyFileAttachment", func(t *testing.T) {
		t.Logf("%s", Subheader("Testing RemovePropertyFileAttachment"))

		if createdPropertyID == "" || createdAttachmentID == "" {
			t.Skip("Skipping RemovePropertyFileAttachment test - no property or attachment was created")
		}

		req := &property.RemovePropertyFileAttachmentRequest{
			PropertyId:   createdPropertyID,
			AttachmentId: createdAttachmentID,
		}

		resp, err := propertyClient.RemovePropertyFileAttachment(ctx, connect.NewRequest(req))
		if err != nil {
			t.Logf("%s", Failure("RemovePropertyFileAttachment failed"))
			t.Logf("%s", Info("Error: "+err.Error()))
			t.Fatalf("RemovePropertyFileAttachment failed: %v", err)
		}

		t.Logf("%s", Success("RemovePropertyFileAttachment successful"))

		// Verify the response is not nil (empty response is expected)
		if resp == nil {
			t.Fatalf("RemovePropertyFileAttachment response is nil")
		}
	})

	// Test 8: List file attachments after removal (should have one less)
	t.Run("ListPropertyFileAttachments_AfterRemoval", func(t *testing.T) {
		t.Logf("%s", Subheader("Testing ListPropertyFileAttachments after removal"))

		if createdPropertyID == "" {
			t.Skip("Skipping ListPropertyFileAttachments test - no property was created")
		}

		req := &property.ListPropertyFileAttachmentsRequest{
			PropertyId: createdPropertyID,
			PageSize:   50,
		}

		resp, err := propertyClient.ListPropertyFileAttachments(ctx, connect.NewRequest(req))
		if err != nil {
			t.Logf("%s", Failure("ListPropertyFileAttachments failed"))
			t.Logf("%s", Info("Error: "+err.Error()))
			t.Fatalf("ListPropertyFileAttachments failed: %v", err)
		}

		t.Logf("%s", Success("ListPropertyFileAttachments successful"))
		t.Logf("%s", Info(fmt.Sprintf("Found %d file attachments after removal", len(resp.Msg.FileAttachments))))

		// Should have one attachment now (the document, photo was removed)
		if len(resp.Msg.FileAttachments) != 1 {
			t.Errorf("Expected 1 file attachment after removal, got %d", len(resp.Msg.FileAttachments))
		} else {
			attachment := resp.Msg.FileAttachments[0]
			if attachment.FileCategory != "document" {
				t.Errorf("Expected remaining attachment to be document, got %s", attachment.FileCategory)
			}
		}
	})

	// Test 9: Test pagination
	t.Run("ListPropertyFileAttachments_Pagination", func(t *testing.T) {
		t.Logf("%s", Subheader("Testing ListPropertyFileAttachments pagination"))

		if createdPropertyID == "" {
			t.Skip("Skipping ListPropertyFileAttachments pagination test - no property was created")
		}

		// Test with small page size
		req := &property.ListPropertyFileAttachmentsRequest{
			PropertyId: createdPropertyID,
			PageSize:   1,
		}

		resp, err := propertyClient.ListPropertyFileAttachments(ctx, connect.NewRequest(req))
		if err != nil {
			t.Logf("%s", Failure("ListPropertyFileAttachments with pagination failed"))
			t.Logf("%s", Info("Error: "+err.Error()))
			t.Fatalf("ListPropertyFileAttachments with pagination failed: %v", err)
		}

		t.Logf("%s", Success("ListPropertyFileAttachments with pagination successful"))
		t.Logf("%s", Info(fmt.Sprintf("Found %d file attachments, next page token: %s", len(resp.Msg.FileAttachments), resp.Msg.NextPageToken)))

		// Should have one attachment due to page size limit
		if len(resp.Msg.FileAttachments) != 1 {
			t.Errorf("Expected 1 file attachment due to page size, got %d", len(resp.Msg.FileAttachments))
		}
	})

	// Test 10: Test error handling for non-existent property
	t.Run("ListPropertyFileAttachments_NonExistentProperty", func(t *testing.T) {
		t.Logf("%s", Subheader("Testing ListPropertyFileAttachments with non-existent property"))

		req := &property.ListPropertyFileAttachmentsRequest{
			PropertyId: "non-existent-property-id",
			PageSize:   50,
		}

		resp, err := propertyClient.ListPropertyFileAttachments(ctx, connect.NewRequest(req))
		if err != nil {
			t.Logf("%s", Success("ListPropertyFileAttachments correctly returned error for non-existent property"))
			t.Logf("%s", Info("Error: "+err.Error()))
		} else {
			t.Logf("%s", Info("ListPropertyFileAttachments returned empty result for non-existent property"))
			// Should return empty result, not error
			if len(resp.Msg.FileAttachments) != 0 {
				t.Errorf("Expected 0 file attachments for non-existent property, got %d", len(resp.Msg.FileAttachments))
			}
		}
	})

	// Test 11: Test error handling for non-existent attachment
	t.Run("RemovePropertyFileAttachment_NonExistentAttachment", func(t *testing.T) {
		t.Logf("%s", Subheader("Testing RemovePropertyFileAttachment with non-existent attachment"))

		if createdPropertyID == "" {
			t.Skip("Skipping RemovePropertyFileAttachment test - no property was created")
		}

		req := &property.RemovePropertyFileAttachmentRequest{
			PropertyId:   createdPropertyID,
			AttachmentId: "non-existent-attachment-id",
		}

		_, err := propertyClient.RemovePropertyFileAttachment(ctx, connect.NewRequest(req))
		if err != nil {
			t.Logf("%s", Success("RemovePropertyFileAttachment correctly returned error for non-existent attachment"))
			t.Logf("%s", Info("Error: "+err.Error()))
		} else {
			t.Fatalf("RemovePropertyFileAttachment should have failed for non-existent attachment")
		}
	})
}

// TestPropertyAPI_ConcurrentMediaOperations tests concurrent file attachment operations
func TestPropertyAPI_ConcurrentMediaOperations(t *testing.T) {
	httpClient := http.DefaultClient
	AddAuthHeader(httpClient)
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	propertyClient := propertyConnect.NewPropertyServiceClient(httpClient, ServiceBaseURL)

	t.Logf("%s", Header("🔄 Testing Concurrent Property Media Operations"))

	var createdPropertyID string

	// Create a test property for concurrent operations
	t.Run("CreatePropertyForConcurrentMedia", func(t *testing.T) {
		t.Logf("%s", Subheader("Creating test property for concurrent media operations"))

		testProperty := &property.Property{
			OrgId:            1,
			PropertyNumber:   "TEST-CONCURRENT-MEDIA-PROP-001",
			PropertyStatus:   property.PropertyStatus_PROPERTY_STATUS_IN_CUSTODY,
			IsEvidence:       true,
			RetentionPeriod:  "30 days",
			DisposalType:     property.PropertyDisposalType_PROPERTY_DISPOSAL_TYPE_UNSPECIFIED,
			Notes:            "Test property for concurrent media operations",
			CurrentCustodian: "Officer Smith",
			CurrentLocation:  "Evidence Room A",
			PropertySchema: &property.PropertySchema{
				Description:  "Test evidence item for concurrent media testing",
				Quantity:     "1",
				Category:     "Weapon",
				Identifiers:  "Test Manufacturer Test Model",
				Owner:        "",
				Condition:    "Good",
				SerialNumber: "SN123456789",
				Value:        "1000.00",
				PropertyType: property.PropertyType_PROPERTY_TYPE_SEIZED,
			},
		}

		req := &property.CreatePropertyRequest{
			Property: testProperty,
		}

		resp, err := propertyClient.CreateProperty(ctx, connect.NewRequest(req))
		if err != nil {
			t.Logf("%s", Failure("CreateProperty for concurrent media testing failed"))
			t.Logf("%s", Info("Error: "+err.Error()))
			t.Fatalf("CreateProperty for concurrent media testing failed: %v", err)
		}

		createdPropertyID = resp.Msg.Property.Id
		t.Logf("%s", Success("CreateProperty for concurrent media testing successful"))
		t.Logf("%s", Info(fmt.Sprintf("Created property ID: %s", createdPropertyID)))
	})

	// Test concurrent add operations
	t.Run("ConcurrentAddPropertyFileAttachments", func(t *testing.T) {
		t.Logf("%s", Subheader("Testing concurrent AddPropertyFileAttachment operations"))

		if createdPropertyID == "" {
			t.Skip("Skipping concurrent media operations test - no property was created")
		}

		const numConcurrentAdds = 5
		var wg sync.WaitGroup
		attachmentIDs := make([]string, numConcurrentAdds)
		errors := make([]error, numConcurrentAdds)

		// Start concurrent add operations
		for i := 0; i < numConcurrentAdds; i++ {
			wg.Add(1)
			go func(index int) {
				defer wg.Done()

				fileAttachment := &property.PropertyFileReference{
					FileId:       fmt.Sprintf("concurrent-file-id-%03d", index+1),
					Caption:      fmt.Sprintf("Concurrent test file %d", index+1),
					DisplayName:  fmt.Sprintf("concurrent_file_%03d.jpg", index+1),
					DisplayOrder: clampToInt32(index % 1000), // Safe conversion with modulo to prevent overflow
					FileCategory: "photo",
					Metadata:     &structpb.Struct{}, // Use empty struct instead of nil
				}

				req := &property.AddPropertyFileAttachmentRequest{
					PropertyId:     createdPropertyID,
					FileAttachment: fileAttachment,
				}

				resp, err := propertyClient.AddPropertyFileAttachment(ctx, connect.NewRequest(req))
				if err != nil {
					errors[index] = err
				} else {
					attachmentIDs[index] = resp.Msg.FileAttachment.Id
				}
			}(i)
		}

		wg.Wait()

		// Check for errors
		for i, err := range errors {
			if err != nil {
				t.Logf("%s", Failure(fmt.Sprintf("Concurrent add operation %d failed: %v", i+1, err)))
			}
		}

		// Count successful adds
		successfulAdds := 0
		for _, id := range attachmentIDs {
			if id != "" {
				successfulAdds++
			}
		}

		t.Logf("%s", Success(fmt.Sprintf("Concurrent add operations completed: %d successful", successfulAdds)))

		// Verify all adds were successful
		if successfulAdds != numConcurrentAdds {
			t.Errorf("Expected %d successful concurrent adds, got %d", numConcurrentAdds, successfulAdds)
		}

		// Verify all attachments were created by listing them
		listReq := &property.ListPropertyFileAttachmentsRequest{
			PropertyId: createdPropertyID,
			PageSize:   100,
		}

		listResp, err := propertyClient.ListPropertyFileAttachments(ctx, connect.NewRequest(listReq))
		if err != nil {
			t.Logf("%s", Failure("Failed to list attachments after concurrent adds"))
			t.Logf("%s", Info("Error: "+err.Error()))
		} else {
			t.Logf("%s", Success(fmt.Sprintf("Found %d attachments after concurrent adds", len(listResp.Msg.FileAttachments))))
			if len(listResp.Msg.FileAttachments) != numConcurrentAdds {
				t.Errorf("Expected %d attachments after concurrent adds, got %d", numConcurrentAdds, len(listResp.Msg.FileAttachments))
			}
		}
	})

	// Test concurrent list operations
	t.Run("ConcurrentListPropertyFileAttachments", func(t *testing.T) {
		t.Logf("%s", Subheader("Testing concurrent ListPropertyFileAttachments operations"))

		if createdPropertyID == "" {
			t.Skip("Skipping concurrent list operations test - no property was created")
		}

		const numConcurrentLists = 10
		var wg sync.WaitGroup
		results := make([][]*property.PropertyFileReference, numConcurrentLists)
		errors := make([]error, numConcurrentLists)

		// Start concurrent list operations
		for i := 0; i < numConcurrentLists; i++ {
			wg.Add(1)
			go func(index int) {
				defer wg.Done()

				req := &property.ListPropertyFileAttachmentsRequest{
					PropertyId: createdPropertyID,
					PageSize:   50,
				}

				resp, err := propertyClient.ListPropertyFileAttachments(ctx, connect.NewRequest(req))
				if err != nil {
					errors[index] = err
				} else {
					results[index] = resp.Msg.FileAttachments
				}
			}(i)
		}

		wg.Wait()

		// Check for errors
		for i, err := range errors {
			if err != nil {
				t.Logf("%s", Failure(fmt.Sprintf("Concurrent list operation %d failed: %v", i+1, err)))
			}
		}

		// Verify all lists returned the same number of results
		expectedCount := 5 // From the previous concurrent add test
		successfulLists := 0
		for i, result := range results {
			if len(result) == expectedCount {
				successfulLists++
			} else {
				t.Logf("%s", Info(fmt.Sprintf("List operation %d returned %d attachments, expected %d", i+1, len(result), expectedCount)))
			}
		}

		t.Logf("%s", Success(fmt.Sprintf("Concurrent list operations completed: %d successful", successfulLists)))

		// All list operations should be successful
		if successfulLists != numConcurrentLists {
			t.Errorf("Expected %d successful concurrent lists, got %d", numConcurrentLists, successfulLists)
		}
	})
}
