# Hero Communications Service

The Hero Communications Service provides critical real-time communication capabilities for public safety operations, including **cellular/voice calls**, **Push-to-Talk (PTT)**, and legacy chat/video services.

**Primary Services:**

1. **🚨 CellularCallService** - Twilio-based voice calls with call queueing (MAIN SERVICE)
2. **📡 PTTService** - Push-to-Talk message history from Zello
3. **💬 Chat/VideoCallService** - Legacy Agora-based services (INACTIVE)

---

## 🚨 Cellular Call Service (Main Service)

The **CellularCallService** is the primary service, providing inbound/outbound voice calling via Twilio with advanced call queue management for dispatchers and first responders.

### Quick Start - Local Development

#### Complete Setup (Recommended)

```bash
1. make clean
2. make ready ## This will bootstrap your DB with the most up to date twilio queue, api user id, etc.
3. make dev-webhooks
```

This single command (make dev-webhooks):

- ✅ Tests all prerequisites (ngrok, Docker, database, Twilio credentials)
- ✅ Starts ngrok tunnel to expose your local service
- ✅ Updates Twilio webhook URLs to point to your tunnel
- ✅ Starts all services with webhook support



#### When You're Done

```bash
make stop-webhooks
```

### How Twilio Integration Works

1. **Incoming Calls**: Twilio → ngrok tunnel → your local communications service
2. **Outbound Calls**: Your service → Twilio API → destination phone
3. **Call Status**: Twilio → webhook → your service for real-time updates

The webhook automation handles all tunnel management and Twilio configuration automatically.

### Environment Variables (Twilio)

| Variable                     | Description                                     | Where to Get It                                  |
| ---------------------------- | ----------------------------------------------- | ------------------------------------------------ |
| `TWILIO_ACCOUNT_SID`         | Your main Twilio Account SID                    | AWS Secrets Manager: `local/services/env`        |
| `TWILIO_AUTH_TOKEN`          | Twilio auth token for API authentication        | AWS Secrets Manager: `local/services/env`        |
| `TWIML_APP_SID`              | TwiML Application SID for voice calls           | AWS Secrets Manager: `local/services/env`        |
| `TWILIO_API_KEY_SID`         | Twilio API Key SID for tokens                   | AWS Secrets Manager: `local/services/env`        |
| `TWILIO_API_KEY_SECRET`      | Twilio API Key Secret for tokens                | AWS Secrets Manager: `local/services/env`        |
| `BOT_BASIC_AUTH_LAMBDA_SECRET` | Bot authentication secret for service-to-service calls | AWS Secrets Manager: `local/services/env` |
| `COMMS_SERVER_PUBLIC_DOMAIN` | Public domain (auto-set by `make dev-webhooks`) | Automatically configured                         |

**Note:** All environment variables are now stored in the shared `services/.env` file and available to all services.

### Core CellularCallService APIs

#### Authentication & Tokens

**GetCellularCallAccessToken** - Generate Twilio access tokens for clients

- **Route**: `POST /hero.conversation.v1.CellularCallService/GetCellularCallAccessToken`
- **Use**: Client-side calling (web/mobile apps)

```json
// Request
{
  "identity": "dispatcher-123",
  "expire": 3600
}

// Response
{
  "token": "eyJ0eXAiOiJKV1QiLCJhbGci...",
  "identity": "dispatcher-123"
}
```

#### Call Handling

**HandleCall** - Primary webhook endpoint for Twilio

- **Route**: `POST /hero.conversation.v1.CellularCallService/HandleCall`
- **Use**: Twilio webhook (not called directly)
- **Function**: Routes incoming/outgoing calls and generates TwiML responses

#### Call Queue Management

The service provides sophisticated call queue management with state tracking and error recovery.

##### Basic Queue Operations

**QueueCall** - Place incoming calls in queue

```json
// Request
{
  "caller": "+15551234567",
  "caller_name": "John Doe",
  "attributes": {"priority": "high", "incident_id": "INC-001"},
  "priority": 1
}

// Response
{
  "queue_sid": "QU1234567890abcdef1234567890abcdef",
  "call_sid": "CA1234567890abcdef1234567890abcdef",
  "twiml": "<Response><Enqueue>hero-default-queue</Enqueue></Response>"
}
```

**DequeueCallBySid** - Connect dispatcher to specific call

- **Route**: `POST /hero.conversation.v1.CellularCallService/DequeueCallBySid`

```json
// Request
{
  "call_sid": "CA1234567890abcdef1234567890abcdef",
  "asset_id": "dispatcher-123"
}

// Response
{
  "success": true,
  "caller": "+15551234567",
  "caller_name": "John Doe",
  "attributes": {"priority": "high"},
  "queue_name": "hero-default-queue"
}
```

**DequeueCall (INACTIVE – Race Condition Issues)** - Connect dispatcher to the next call in queue

> ⚠️ **Deprecated** – This endpoint is disabled in production because it introduces race conditions when multiple dispatchers attempt to pick up calls simultaneously. Clients should call **`DequeueCallBySid`** instead, which performs a _selective pickup_ flow that avoids this problem.

```json
// Request
{
  "asset_id": "dispatcher-123"
}

// Response
{
  "success": true,
  "call_sid": "CA1234567890abcdef1234567890abcdef",
  "caller": "+15551234567",
  "caller_name": "John Doe",
  "attributes": {"priority": "high", "incident_id": "INC-001"},
  "queue_name": "hero-default-queue"
}
```

**GetQueueStatus** - View current queue state

```json
// Response
{
  "queue_size": 3,
  "hold_size": 1,
  "next_call": {
    "call_sid": "CA1234567890abcdef1234567890abcdef",
    "caller": "+15551234567",
    "caller_name": "John Doe",
    "wait_time": 120,
    "priority": 1
  },
  "waiting_calls": [
    {
      "call_sid": "CA1234567890abcdef1234567890abcdef",
      "caller": "+15551234567",
      "caller_name": "John Doe",
      "position": 1,
      "wait_time": 120,
      "priority": 1,
      "attributes": {"incident_id": "INC-001"}
    }
  ],
  "on_hold_calls": [...]
}
```

**ListCalls** - Retrieve paginated call history with filtering

- **Route**: `POST /hero.conversation.v1.CellularCallService/ListCalls`
- **Use**: Analytics, reporting, and call history views

```json
// Request
{
  "start_date": "2024-01-01T00:00:00Z",  // Optional - RFC3339 format
  "end_date": "2024-01-31T23:59:59Z",    // Optional - RFC3339 format
  "page": 1,                              // Page number (1-based)
  "page_size": 50,                        // Results per page (default: 50, max: 100)
  "sort_order": "desc",                   // "asc" or "desc" (default: "desc" - newest first)
  "state": "completed",                   // Optional - filter by call state
  "direction": "inbound"                  // Optional - "inbound" or "outbound"
}

// Response
{
  "calls": [
    {
      "call_sid": "CA1234567890abcdef1234567890abcdef",
      "caller": "+15551234567",
      "caller_name": "John Doe",
      "enqueue_time": "2024-01-15T10:30:00Z",
      "call_start_time": "2024-01-15T10:31:30Z",  // When connected to agent
      "call_end_time": "2024-01-15T10:45:00Z",
      "last_hold_start": null,
      "direction": "inbound",
      "priority": 1,
      "asset_id": "dispatcher-123",
      "situation_id": "SIT-001",
      "notes": "Resolved customer inquiry",
      "attributes": {"department": "sales"},
      "history": [
        {
          "timestamp": "2024-01-15T10:30:00Z",
          "event_type": "CALL_EVENT_TYPE_QUEUED",
          "asset_id": "",
          "notes": "Call entered queue"
        },
        {
          "timestamp": "2024-01-15T10:31:30Z",
          "event_type": "CALL_EVENT_TYPE_DEQUEUED",
          "asset_id": "dispatcher-123",
          "notes": "Call answered by dispatcher"
        }
      ]
    }
  ],
  "total_count": 150,
  "next_page": 2  // Present only if more pages exist
}
```

**Key Features:**
- Returns all call queue table information
- Default behavior: Returns latest calls when no date range specified
- Supports pagination with configurable page size
- Includes complete call lifecycle data (enqueue, start, end times)
- Tracks wait time (difference between enqueue_time and call_start_time)
- Includes call direction for inbound/outbound analysis

##### Call State Management

**HoldCall** - Place active call on hold

```json
// Request
{
  "call_sid": "CA1234567890abcdef1234567890abcdef",
  "asset_id": "dispatcher-123",
  "reason": "Taking another urgent call"
}

// Response
{
  "success": true
}
```

**ResumeCall** - Reconnect to held call

```json
// Request
{
  "call_sid": "CA1234567890abcdef1234567890abcdef",
  "asset_id": "dispatcher-123"
}

// Response
{
  "success": true
}
```

**GetAssetHeldCalls** - Get calls on hold for specific dispatcher

- **Route**: `POST /hero.conversation.v1.CellularCallService/GetAssetHeldCalls`

```json
// Request
{
  "asset_id": "dispatcher-123"
}

// Response
{
  "held_calls": [
    {
      "call_sid": "CA1234567890abcdef1234567890abcdef",
      "caller": "+15551234567",
      "caller_name": "John Doe",
      "hold_time": 300,
      "hold_reason": "Taking another urgent call",
      "attributes": {"incident_id": "INC-001"}
    }
  ]
}
```

**EndCall** - Terminate calls

```json
// Request
{
  "call_sid": "CA1234567890abcdef1234567890abcdef",
  "asset_id": "dispatcher-123",
  "reason": "Call completed"
}

// Response
{
  "success": true
}
```

##### Error Recovery

**RevertSelectiveClaim** - Recover stuck calls in queue

- **Route**: `POST /hero.conversation.v1.CellularCallService/RevertSelectiveClaim`
- **Use**: When agent assignment fails, revert call back to waiting state

```json
// Request
{
  "call_sid": "CA1234567890abcdef1234567890abcdef"
}

// Response
{
  "success": true,
  "reverted": true  // false if call was already waiting
}
```

**Call State Flow:**

1. `waiting` - Call in queue, available for pickup
2. `pending_selective_assignment` - Call being routed to specific agent
3. `active` - Call connected to agent
4. `held` - Call placed on hold by agent
5. `completed` - Call ended

Use `RevertSelectiveClaim` when calls get stuck in `pending_selective_assignment` due to failed redirections or agent errors.

### Outbound Calling

Outbound calls work through the Twilio Voice SDK:

1. Client gets access token via `GetCellularCallAccessToken`
2. Client initializes Twilio Device with token
3. Client calls `device.connect()` with destination number
4. Twilio routes to `HandleCall` webhook for processing

See `OutboundCallComponent` in hero-command-and-control-web for reference implementation.

### TwilioWebhookService (Direct Webhook Endpoints)

The service exposes direct webhook endpoints that Twilio calls for real-time call events.

#### /voice Webhook

- **Route**: `POST /hero.communications.v1.TwilioWebhookService/voice`
- **Called by**: Twilio when calls are initiated
- **Function**:
  - Routes incoming calls to appropriate queues
  - Handles outbound call setup
  - Generates TwiML responses for call flow
- **Authentication**: Basic auth using org API credentials

#### /callstatus Webhook

- **Route**: `POST /hero.communications.v1.TwilioWebhookService/callstatus`
- **Called by**: Twilio for call status updates
- **Function**:
  - Updates internal call state (ringing, answered, completed, failed)
  - Triggers notifications for call events
  - Maintains call history and metrics
- **Events**: `initiated`, `ringing`, `answered`, `completed`, `busy`, `failed`, `no-answer`

#### /twiml/connectAgent Webhook

- **Route**: `POST /hero.communications.v1.TwilioWebhookService/twiml/connectAgent`
- **Called by**: Twilio to connect an agent to a call.
- **Function**:
  - Generates TwiML to connect an agent.
  - **Used by**: `DequeueCallBySid` (a.k.a. _Selective Pickup_) to bridge the selected dispatcher to the queued caller.
- **Authentication**: Basic auth using org API credentials

#### /twilio/agent-dial-status Webhook

- **Route**: `POST /hero.communications.v1.TwilioWebhookService/twilio/agent-dial-status`
- **Called by**: Twilio to report the status of an agent dial attempt.
- **Function**:
  - Handles the status of an agent dial attempt.
  - **Used by**: `DequeueCallBySid` to track success/failure of the selective connect flow.
- **Authentication**: Basic auth using org API credentials

#### /waithold Webhook

- **Route**: `POST /hero.communications.v1.TwilioWebhookService/waithold`
- **Called by**: Twilio to handle call hold status.
- **Function**:
  - Manages the inbound flow state (music and messaging) of a call.
- **Authentication**: Basic auth using org API credentials

#### Webhook URL Format & Basic-Auth Credentials

Twilio calls each webhook using **HTTP Basic Authentication**. The `username` and `password` are the **Org API User credentials** — specifically the `api_key_id` (username) and `api_key_secret` (password) — stored in the `org_api_users` table. You create/manage these users in the Hero Admin Console (⚙️ Settings ➜ API Users) or via the `orgs` service CLI.  
When embedded in the URL the syntax is:

```
https://<api_key_id>:<api_key_secret>@<host>/<path>
```

> Example – Production

```
https://c2f35e6c-5513-48e1-902c-9773fac59c11:<EMAIL>/hero.communications.v1.TwilioWebhookService/voice
```

Below is the full list of production endpoints (replace the id/secret with your own credentials):

```
https://<api_key_id>:<api_key_secret>@communications.basic.api.gethero.com/hero.communications.v1.TwilioWebhookService/voice
https://<api_key_id>:<api_key_secret>@communications.basic.api.gethero.com/hero.communications.v1.TwilioWebhookService/callstatus
https://<api_key_id>:<api_key_secret>@communications.basic.api.gethero.com/hero.communications.v1.TwilioWebhookService/twiml/connectAgent
https://<api_key_id>:<api_key_secret>@communications.basic.api.gethero.com/hero.communications.v1.TwilioWebhookService/twilio/agent-dial-status
https://<api_key_id>:<api_key_secret>@communications.basic.api.gethero.com/hero.communications.v1.TwilioWebhookService/waithold
```

The local URLs are automatically generated and configured when you run `make dev-webhooks`, which spins up an ngrok tunnel and updates Twilio to point at your temporary domain.

### Internal Twilio Client Methods

#### RedirectQueueMember

Advanced queue management for selective call assignment:

```go
RedirectQueueMember(ctx context.Context, queueSid string, callSid string, redirectURL string) error
```

- **Function**: Redirects specific queued call to new TwiML endpoint
- **Use Cases**:
  - Selective agent assignment
  - Priority call routing
  - Error recovery for failed assignments
- **Twilio API**: `POST /Accounts/{AccountSid}/Queues/{QueueSid}/Members/{CallSid}.json`

**Example Usage**:

1. Call enters queue in `waiting` state
2. Dispatcher selects specific call for pickup
3. System calls `RedirectQueueMember` to route call to dispatcher's endpoint
4. Call moves to `pending_selective_assignment` state
5. If successful, call connects; if failed, use `RevertSelectiveClaim`

#### TwiML Generation

The service dynamically generates TwiML responses for:

- **Queueing**: `<Enqueue>` for incoming calls
- **Dialing**: `<Dial>` for outbound calls
- **Conferencing**: `<Conference>` for multi-party calls
- **Hold Music**: `<Play>` for calls on hold
- **Voicemail**: `<Record>` for missed calls

---

## 📡 PTT Service (Push-to-Talk)

The PTTService provides access to Push-to-Talk message history from Zello for reviewing communication logs.

### GetHistoryMetadata

**Route**: `POST /hero.conversation.v1.PTTService/GetHistoryMetadata`

```json
// Request
{
  "sender": "user123",
  "start_ts": *************,
  "end_ts": 1625184000000,
  "max": 50,
  "sort": "ts",
  "sort_order": "desc"
}

// Response
{
  "status": "success",
  "total": 25,
  "returned": 25,
  "messages": [
    {
      "id": 12345,
      "type": "audio",
      "ts": 1625180000000,
      "sender": {"id": "user123", "name": "John Doe"},
      "recipient": "channel456",
      "duration": 15,
      "transcription": "This is a test message"
    }
  ]
}
```

Available filters: `sender`, `recipient`, `via_channel`, `type`, `text`, `start_ts`, `end_ts`, `max`, `start`, `sort`, `sort_order`

---

## 💬 Legacy Services (INACTIVE)

**⚠️ NOTE: These services are currently inactive and maintained for legacy compatibility only.**

### VideoCallService (Agora)

**GetVideoCallAccessToken** - Generate Agora video call tokens

- **Route**: `POST /hero.conversation.v1.VideoCallService/GetVideoCallAccessToken`

### ChatService (Agora)

**GetChatUserToken** - Generate user chat tokens

- **Route**: `POST /hero.conversation.v1.ChatService/GetChatUserToken`

**GetChatAppToken** - Generate app-level chat tokens

- **Route**: `POST /hero.conversation.v1.ChatService/GetChatAppToken`

**CreateGroupChat** - Create group chats

- **Route**: `POST /hero.conversation.v1.ChatService/CreateGroupChat`

### Legacy Environment Variables

| Variable                | Description                  | Where to Get It                           |
| ----------------------- | ---------------------------- | ----------------------------------------- |
| `AGORA_APP_ID`          | Agora App ID for video calls | [Agora Console](https://console.agora.io) |
| `AGORA_APP_CERTIFICATE` | Agora App Certificate        | Agora Console → Project Security          |
| `AGORA_CHAT_APP_ID`     | Agora Chat App ID            | Agora Console → Chat section              |
| `AGORA_CHAT_HOST_URL`   | Agora Chat base URL          | Typically `https://api.agora.io`          |
| `AGORA_CHAT_APP_NAME`   | Agora Chat application name  | Agora Console → Chat configuration        |
| `AGORA_CHAT_ORG_NAME`   | Agora Chat organization name | Agora Console → Chat configuration        |

---

## 🔧 Basic Service Setup

### Prerequisites

- [Docker](https://www.docker.com/) and [Go](https://go.dev/)
- [ngrok](https://ngrok.com) (for webhook development)

### Start Service

```bash
# Start all services
make run

# Start only communications service
make run-service communications-service

# Service accessible at: http://localhost:9084
```

### Environment Setup

1. Credentials are shared across all services in `services/.env`
2. Copy credentials from AWS Secrets Manager: `local/services/env` 
3. Run `make run`

---

## 🔑 Security Notes

- **Keep all credentials secret** - Never commit to public repositories
- **Use AWS Secrets Manager** for production credentials
- **Rotate tokens regularly** according to security policies

## 📝 Important Notes

- **All `userId` fields must be alphanumeric only** (no special characters or spaces)
- **Service runs on port 9084** by default
- **Webhook automation handles all Twilio configuration** for local development

**Local Development (ngrok)**

```
https://<api_key_id>:<api_key_secret>@<random-subdomain>.ngrok-free.app/hero.communications.v1.TwilioWebhookService/voice
https://<api_key_id>:<api_key_secret>@<random-subdomain>.ngrok-free.app/hero.communications.v1.TwilioWebhookService/callstatus
https://<api_key_id>:<api_key_secret>@<random-subdomain>.ngrok-free.app/hero.communications.v1.TwilioWebhookService/twiml/connectAgent
https://<api_key_id>:<api_key_secret>@<random-subdomain>.ngrok-free.app/hero.communications.v1.TwilioWebhookService/twilio/agent-dial-status
https://<api_key_id>:<api_key_secret>@<random-subdomain>.ngrok-free.app/hero.communications.v1.TwilioWebhookService/waithold
```

The local URLs (including temporary `api_key_id`/`api_key_secret`) are automatically embedded when you run `make dev-webhooks`, which spins up an ngrok tunnel and calls Twilio's API to register the tunnel address.
