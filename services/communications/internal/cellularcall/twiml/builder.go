package twiml

import (
	"fmt"
	"strings"

	"github.com/twilio/twilio-go/twiml"
)

// QueueResponse generates TwiML to place a caller in a queue
func QueueResponse(queueName string, waitURL string, welcomeMessage string) (string, error) {
	enqueue := &twiml.VoiceEnqueue{
		Name:    queueName,
		WaitUrl: waitURL,
	}

	// Only include welcome message if it's not empty
	if welcomeMessage != "" {
		say := &twiml.VoiceSay{
			Message:  welcomeMessage,
			Voice:    "Polly.Matthew-Neural",
			Language: "en-US",
		}
		return twiml.Voice([]twiml.Element{say, enqueue})
	}

	return twiml.Voice([]twiml.Element{enqueue})
}

// DequeueResponse generates minimal TwiML to connect an agent to the next call in a queue
func DequeueResponse(queueName string) (string, error) {
	dial := &twiml.VoiceDial{}
	queueElement := twiml.VoiceQueue{
		Name: queueName,
	}
	dial.InnerElements = []twiml.Element{&queueElement}

	return twiml.Voice([]twiml.Element{dial})
}

// ClientDialResponse generates TwiML to connect a caller to a client device
func ClientDialResponse(clientId string, callerId string) (string, error) {
	dial := &twiml.VoiceDial{
		CallerId: callerId,
	}
	clientElement := twiml.VoiceClient{
		Identity: clientId,
	}
	dial.InnerElements = []twiml.Element{&clientElement}

	return twiml.Voice([]twiml.Element{dial})
}

// OutboundDialResponse generates TwiML to place an outbound call
func OutboundDialResponse(to string, callerId string) (string, error) {
	dial := &twiml.VoiceDial{
		CallerId: callerId,
	}
	numberElement := twiml.VoiceNumber{
		PhoneNumber: to,
	}
	dial.InnerElements = []twiml.Element{&numberElement}

	return twiml.Voice([]twiml.Element{dial})
}

// HoldResponse generates TwiML to place a call on hold with music. Currently unused (replaced by HoldWaitResponse)
func HoldResponse(message string) (string, error) {
	say := &twiml.VoiceSay{
		Message:  message,
		Voice:    "Polly.Matthew-Neural",
		Language: "en-US",
	}

	play := &twiml.VoicePlay{
		Url:  "http://com.twilio.music.classical.s3.amazonaws.com/ith_chopin-15-2.mp3",
		Loop: "0",
	}

	return twiml.Voice([]twiml.Element{say, play})
}

// EmptyResponse generates an empty TwiML response
func EmptyResponse() (string, error) {
	return twiml.Voice([]twiml.Element{})
}

// HangupResponse generates TwiML to hang up the call.
func HangupResponse() (string, error) {
	hangup := &twiml.VoiceHangup{}
	return twiml.Voice([]twiml.Element{hangup})
}

// AgentConnectTwiML generates TwiML to connect a caller to an asset with status callbacks
func AgentConnectTwiML(assetID string, customerCallSid string, callerID string, cbURL string) (string, error) {
	dial := &twiml.VoiceDial{
		CallerId: callerID,
	}

	client := &twiml.VoiceClient{
		Identity:             assetID,
		StatusCallback:       cbURL,
		StatusCallbackEvent:  "initiated ringing answered completed",
		StatusCallbackMethod: "POST",
	}
	param := &twiml.VoiceParameter{Name: "customerCallSid", Value: customerCallSid}
	client.InnerElements = []twiml.Element{param}
	dial.InnerElements = []twiml.Element{client}

	xml, err := twiml.Voice([]twiml.Element{dial})
	return strings.TrimSpace(xml), err
}

// EnqueueWaitResponse generates TwiML for smart enqueue wait messaging
func EnqueueWaitResponse(orgName string) (string, error) {
	// Dynamic message with org name, fallback to generic if empty
	var message string
	if orgName != "" {
		message = fmt.Sprintf("You have reached %s Safety. Your call is important to us and is being routed to the next available dispatcher. Please stay on the line—someone from our team will be with you shortly.", orgName)
	} else {
		message = "Your call is important to us and is being routed to the next available dispatcher. Please stay on the line—someone from our team will be with you shortly."
	}

	say := &twiml.VoiceSay{
		Message:  message,
		Voice:    "Polly.Matthew-Neural",
		Language: "en-US",
	}

	// 8 second pause before next loop
	pauseAfter := &twiml.VoicePause{Length: "8"}

	return twiml.Voice([]twiml.Element{say, pauseAfter})
}

// HoldWaitResponse generates TwiML for smart hold wait messaging with looping
func HoldWaitResponse(holdURL string) (string, error) {
	say := &twiml.VoiceSay{
		Message:  "Thank you for holding. Please stay on the line—someone will be with you shortly.",
		Voice:    "Polly.Matthew-Neural",
		Language: "en-US",
	}

	// 10 second pause before next loop
	pause := &twiml.VoicePause{Length: "10"}

	// Redirect back to the same endpoint to create looping behavior
	redirect := &twiml.VoiceRedirect{Url: holdURL}

	return twiml.Voice([]twiml.Element{say, pause, redirect})
}

// RedirectResponse generates TwiML to redirect a call to a new URL
func RedirectResponse(url string) (string, error) {
	redirect := &twiml.VoiceRedirect{Url: url}
	return twiml.Voice([]twiml.Element{redirect})
}
