package usecase

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"strings"
	"time"

	cmncontext "common/context"
	"common/herosentry"

	"connectrpc.com/connect"

	"communications/internal/cellularcall/data"
	"communications/internal/cellularcall/twiml"

	conversationv1 "proto/hero/communications/v1"
	orgs "proto/hero/orgs/v1"
)

// tryHandleCallForwarding determines if the incoming call should be forwarded and, if so,
// generates and writes the forwarding TwiML. Returns true if the request was fully handled.
func (uc *cellularCallUsecase) tryHandleCallForwarding(
	ctx context.Context,
	w http.ResponseWriter,
	r *http.Request,
	orgTwilioDetails data.OrgTwilioDetails,
	callSID string,
	from string,
) bool {
	// Loop prevention
	if r.FormValue("forwardAttempted") == "true" {
		return false
	}

	if !orgTwilioDetails.IsCallForwardingEnabled || orgTwilioDetails.ForwardToNumber == "" {
		return false
	}

	spanCtx, _, finish := herosentry.StartSpan(ctx, "HandleVoiceRequest.Forwarding")
	defer finish()

	// Build action URL with basic auth (required for our webhook)
	// Lookup org API user credentials
	orgUserLookupSpanCtx, orgUserLookupSpan, orgUserLookupFinish := herosentry.StartSpan(spanCtx, "communications.lookup_org_api_user")
	orgUserLookupSpan.SetTag("user_id", orgTwilioDetails.TwilioApiUserId)
	getOrgAPIUserPrivateRequest := &orgs.GetOrgAPIUserPrivateByIdRequest{UserId: orgTwilioDetails.TwilioApiUserId}
	basicTwilioUserAuth, userErr := uc.orgClient.GetOrgAPIUserPrivateById(orgUserLookupSpanCtx, connect.NewRequest(getOrgAPIUserPrivateRequest))
	orgUserLookupFinish()
	if userErr != nil || basicTwilioUserAuth == nil || basicTwilioUserAuth.Msg == nil || basicTwilioUserAuth.Msg.OrgApiUser == nil {
		log.Printf("ERROR: Failed to get org API user for forward action URL: %v", userErr)
		return false
	}
	basicUserAuth := basicTwilioUserAuth.Msg.OrgApiUser.Id + ":" + basicTwilioUserAuth.Msg.OrgApiUser.RawPassword
	forwardActionURL := fmt.Sprintf("https://%s@%s/hero.communications.v1.TwilioWebhookService/twiml/forward-action?forwardAttempted=true", basicUserAuth, uc.commsServerPublicDomain)

	// Best-effort: log initial event
	if callSID != "" && from != "" {
		_ = uc.callQueueRepo.CreateCallForwardAttempt(spanCtx, &data.CallForwardEvent{
			CallSid:    callSID,
			OrgID:      int(cmncontext.GetOrgId(spanCtx)),
			FromNumber: from,
			ToNumber:   orgTwilioDetails.ForwardToNumber,
			Status:     "initiated",
		})
	}

	// Create a default situation for this forwarded call (observability aligned with queue flow)
	if from != "" {
		_, situationSpan, situationFinish := herosentry.StartSpan(spanCtx, "communications.create_default_situation")
		situationSpan.SetTag("caller", from)
		situationSpan.SetTag("call_sid", callSID)
		situationSpan.SetTag("flow", "forward")

		// Minimal call context used for situation creation
		queuedCall := data.QueuedCall{
			CallSID:     callSID,
			Caller:      from,
			CallerName:  "",
			EnqueueTime: time.Now(),
			State:       data.CallStateWaiting,
			Attributes:  map[string]string{"forward": "true"},
		}

		if situationID, cerr := uc.createDefaultSituationForCall(spanCtx, queuedCall); cerr != nil {
			situationSpan.SetTag("error", "true")
			herosentry.CaptureException(spanCtx, cerr, "Failed to create situation for forwarded call")
			log.Printf("Warning: Failed to create situation for forwarded call %s: %v", callSID, cerr)
		} else {
			situationSpan.SetTag("situation_id", situationID)
			// Persist situation linkage on the forward event row (best-effort)
			if callSID != "" {
				u := data.CallForwardAnalyticsUpdate{SituationID: &situationID}
				_ = uc.callQueueRepo.MergeCallForwardAnalytics(spanCtx, callSID, u)
			}
		}
		situationFinish()
	}

	// Build status callback URL for child leg events and generate TwiML
	forwardStatusURL := fmt.Sprintf("https://%s@%s/hero.communications.v1.TwilioWebhookService/callstatus?eventType=forward", basicUserAuth, uc.commsServerPublicDomain)
	forwardXML, ferr := twiml.ForwardDialResponse(
		orgTwilioDetails.ForwardToNumber,
		orgTwilioDetails.TwilioNumber,
		forwardActionURL,
		forwardStatusURL,
		uc.callForwardTimeoutSeconds,
	)
	if ferr != nil {
		log.Printf("Forwarding TwiML generation failed, falling back to queue: %v", ferr)
		return false
	}

	w.Header().Set("Content-Type", "text/xml")
	_, _ = w.Write([]byte(forwardXML))
	return true
}

// HandleVoiceRequest processes all Twilio voice webhook requests and routes them appropriately
func (uc *cellularCallUsecase) HandleVoiceRequest(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	// Extract key parameters first for span tagging
	callSID := r.FormValue("CallSid")
	from := r.FormValue("From")
	to := r.FormValue("To")
	action := r.FormValue("action")

	// herosentry.HTTPMiddleware should have already created a transaction
	// so we just create a child span
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "HandleVoiceRequest")
	defer finishSpan()

	span.SetTag("call_sid", callSID)
	span.SetTag("from", from)
	span.SetTag("to", to)
	span.SetTag("action", action)

	orgTwilioDetails, err := uc.callQueueRepo.GetOrgTwilioDetails(spanContext)
	if err != nil {
		span.SetTag("error", "true")
		herosentry.CaptureException(spanContext, err, "Failed to get org twilio details")
		log.Printf("Error getting org twilio details: %v", err)
		http.Error(w, "Error getting org twilio details", http.StatusInternalServerError)
		return
	}

	// Early call forwarding check (loop prevention via forwardAttempted flag)
	if uc.tryHandleCallForwarding(spanContext, w, r, orgTwilioDetails, callSID, from) {
		return
	}

	orgQueueName, err := uc.callQueueRepo.GetTwilioQueueName(spanContext)
	if err != nil {
		span.SetTag("error", "true")
		herosentry.CaptureException(spanContext, err, "Failed to get Twilio queue name")
		log.Printf("Error getting Twilio queue name: %v", err)
		http.Error(w, "Error getting Twilio queue name", http.StatusInternalServerError)
		return
	}

	log.Printf("Voice request: From=%s, To=%s, Action=%s, CallSid=%s",
		from, to, action, callSID)

	// 1. Handle dequeue operation (agent connecting to caller in queue)
	if action == "dequeue" {
		queueName := orgQueueName
		log.Printf("Handling dequeue action for queue: %s", queueName)

		twimlResponse, err := twiml.DequeueResponse(queueName)
		if err != nil {
			log.Printf("Error generating dequeue TwiML: %v", err)
			http.Error(w, "Error generating TwiML", http.StatusInternalServerError)
			return
		}

		w.Header().Set("Content-Type", "text/xml")
		fmt.Fprint(w, twimlResponse)
		return
	}

	// 2. Handle outbound calls from browser
	if action == "outbound" {
		assetID := r.FormValue("assetId")
		destination := r.FormValue("To")

		log.Printf("Handling outbound call request: AssetId=%s, Destination=%s, CallSid=%s",
			assetID, destination, callSID)

		// Create a call record for the outbound call
		outboundCall := data.QueuedCall{
			CallSID:     callSID,
			Caller:      orgTwilioDetails.TwilioNumber,
			CallerName:  "Outbound Call",
			EnqueueTime: time.Now(),
			AssetID:     assetID,
			State:       data.CallStateActive,
			Direction:   data.CallDirectionOutbound,
			Attributes: map[string]string{
				"destination": destination,
				"CallSid":     callSID,
			},
		}

		// Store the outbound call
		if err := uc.callQueueRepo.StoreActiveCall(ctx, outboundCall); err != nil {
			log.Printf("Warning: Failed to store outbound call: %v", err)
		}

		handleReq := &conversationv1.HandleCallRequest{
			Caller:  "client:" + assetID,
			To:      destination,
			Flow:    "outbound",
			CallSid: callSID,
			Attributes: map[string]string{
				"assetId": assetID,
			},
		}

		handleResp, err := uc.HandleCall(ctx, handleReq)
		if err != nil {
			log.Printf("ERROR: Failed to handle outbound call: %v", err)
			fmt.Fprintf(w, "<Response><Say>Error making call: %s</Say></Response>", err.Error())
			return
		}

		log.Printf("Generated TwiML for outbound call; length=%d", len(handleResp.Twiml))
		w.Header().Set("Content-Type", "text/xml")
		fmt.Fprint(w, handleResp.Twiml)
		return
	}

	// 3. Handle inbound calls to queue
	if to == orgTwilioDetails.TwilioNumber {
		log.Printf("Handling inbound call from %s to queue", from)

		queueReq := &conversationv1.QueueCallRequest{
			Caller:     from,
			CallerName: r.FormValue("CallerName"),
			Attributes: map[string]string{"CallSid": callSID},
		}

		queueResp, err := uc.QueueCall(ctx, queueReq)
		if err != nil {
			log.Printf("Error queueing call: %v", err)
			fmt.Fprintf(w, "<Response><Say>Error: %s</Say></Response>", err.Error())
			return
		}

		w.Header().Set("Content-Type", "text/xml")
		fmt.Fprint(w, queueResp.Twiml)
		return
	}

	// 4. Handle client-to-client calls (if implemented)
	if strings.HasPrefix(from, "client:") && strings.HasPrefix(to, "client:") {
		log.Printf("Handling client-to-client call: %s to %s", from, to)

		handleReq := &conversationv1.HandleCallRequest{
			Caller: from,
			To:     to,
			Flow:   "client-inbound",
		}

		handleResp, err := uc.HandleCall(ctx, handleReq)
		if err != nil {
			log.Printf("Error handling client-to-client call: %v", err)
			fmt.Fprintf(w, "<Response><Say>Error connecting call</Say></Response>")
			return
		}

		w.Header().Set("Content-Type", "text/xml")
		fmt.Fprint(w, handleResp.Twiml)
		return
	}

	// 5. Default case - unrecognized call type
	log.Printf("Unrecognized call type, using default queue handler")

	// Default to queue behavior for backward compatibility
	queueReq := &conversationv1.QueueCallRequest{
		Caller:     from,
		CallerName: r.FormValue("CallerName"),
		Attributes: map[string]string{"CallSid": callSID},
	}

	queueResp, err := uc.QueueCall(ctx, queueReq)
	if err != nil {
		log.Printf("Error in default queue handling: %v", err)
		fmt.Fprintf(w, "<Response><Say>We couldn't process your call at this time.</Say></Response>")
		return
	}

	w.Header().Set("Content-Type", "text/xml")
	fmt.Fprint(w, queueResp.Twiml)
	span.SetTag("http.status_code", "200")
}

func (uc *cellularCallUsecase) HandleCallStatusRequest(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	// herosentry.HTTPMiddleware should have already created a transaction
	// so we just create a child span
	_, span, finishSpan := herosentry.StartSpan(ctx, "HandleCallStatusRequest")
	defer finishSpan()

	// Call forwarding status logging via eventType=forward (non-breaking) to track child leg status for forward attempts
	if r.URL != nil {
		eventType := r.URL.Query().Get("eventType")
		if eventType == "forward" {
			uc.processForwardStatusEvent(ctx, w, r)
			return
		}
	}

	callSid := r.FormValue("CallSid")
	callStatus := r.FormValue("CallStatus")
	direction := r.FormValue("Direction")

	span.SetTag("call_sid", callSid)
	span.SetTag("call_status", callStatus)
	span.SetTag("direction", direction)

	log.Printf("CALLBACK: Call status update for CallSid=%s, Status=%s, Direction=%s",
		callSid, callStatus, direction)

	// For terminated calls, clean up the queue if the call exists (idempotent)
	if callStatus == TwilioCallStatusCompleted || callStatus == TwilioCallStatusCanceled ||
		callStatus == TwilioCallStatusFailed || callStatus == TwilioCallStatusBusy || callStatus == TwilioCallStatusNoAnswer {

		if callSid != "" {
			if _, found, _ := uc.callQueueRepo.GetCallByCallSID(ctx, callSid); found {
				if err := uc.callQueueRepo.EndCall(ctx, callSid); err != nil {
					log.Printf("Warning: Failed to end call %s in repository: %v", callSid, err)
				} else {
					log.Printf("Call %s marked as ended due to status: %s", callSid, callStatus)
				}
			} else {
				// No-op if not found
				log.Printf("CALLBACK: Parent call %s not found; end-call skipped (forward/non-queue path)", callSid)
			}
		}
	}

	span.SetTag("http.status_code", "200")
	w.WriteHeader(http.StatusOK)
}

// processForwardStatusEvent handles Twilio callstatus webhooks when eventType=forward is provided.
// It updates or creates a call_forward_events record based on DialCallStatus and identifiers.
func (uc *cellularCallUsecase) processForwardStatusEvent(ctx context.Context, w http.ResponseWriter, r *http.Request) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "HandleCallStatusRequest.ForwardEvent")
	defer finishSpan()

	// Parse form to ensure we can log and access all fields
	_ = r.ParseForm()
	dialCallStatus := r.FormValue("DialCallStatus")
	parentCallSid := r.FormValue("ParentCallSid")
	callSid := r.FormValue("CallSid")
	from := r.FormValue("From")
	to := r.FormValue("To")
	callStatus := r.FormValue("CallStatus")
	callDuration := r.FormValue("CallDuration")
	sipCode := r.FormValue("SipResponseCode")
	sequence := r.FormValue("SequenceNumber")
	timestamp := r.FormValue("Timestamp")

	span.SetTag("event_type", "forward")
	span.SetTag("dial_call_status", dialCallStatus)
	span.SetTag("parent_call_sid", parentCallSid)
	span.SetTag("call_sid", callSid)
	span.SetTag("from", from)
	span.SetTag("to", to)
	span.SetTag("call_status", callStatus)
	span.SetTag("call_duration", callDuration)
	span.SetTag("sip_code", sipCode)
	span.SetTag("sequence", sequence)
	span.SetTag("timestamp", timestamp)

	// Console logs to discover available fields from Twilio for forwarded child leg
	log.Printf("CALLSTATUS(FORWARD): ParentCallSid=%s, CallSid=%s, DialCallStatus=%s, CallStatus=%s, Duration=%s, SipResponseCode=%s, Timestamp=%s, Sequence=%s",
		parentCallSid, callSid, dialCallStatus, callStatus, callDuration, sipCode, timestamp, sequence)

	// Determine the original call SID that our events table uses
	origSid := parentCallSid
	if origSid == "" {
		origSid = callSid
	}

	if origSid == "" {
		// Nothing we can do; still 200 OK per Twilio best practices
		log.Printf("CALLSTATUS(FORWARD): missing ParentCallSid/CallSid, skipping event update")
		w.WriteHeader(http.StatusOK)
		return
	}

	// Map child leg status to simplified event status
	// Prefer CallStatus when DialCallStatus is empty (status callbacks), otherwise use DialCallStatus (forward-action)
	effective := dialCallStatus
	if effective == "" {
		effective = callStatus
	}

	mappedStatus := "pending"
	switch effective {
	case TwilioCallStatusInitiated, TwilioCallStatusRinging:
		mappedStatus = "pending"
	case TwilioCallStatusInProgress, TwilioCallStatusAnswered:
		mappedStatus = "answered"
	case TwilioCallStatusCompleted:
		mappedStatus = "completed"
	case TwilioCallStatusFailed, TwilioCallStatusCanceled:
		mappedStatus = TwilioCallStatusFailed
	case TwilioCallStatusBusy, TwilioCallStatusNoAnswer:
		mappedStatus = "missed"
	default:
		// leave as pending for unknown/intermediate
	}

	// Build analytics update payload and merge
	var dialPtr *string
	if dialCallStatus != "" {
		v := dialCallStatus
		dialPtr = &v
	}

	updates := data.CallForwardAnalyticsUpdate{}
	if mappedStatus != "" {
		updates.Status = &mappedStatus
	}
	if mappedStatus == TwilioCallStatusFailed || mappedStatus == "missed" {
		reason := ""
		switch effective {
		case TwilioCallStatusBusy:
			reason = "busy"
		case TwilioCallStatusNoAnswer:
			reason = "no-answer"
		case TwilioCallStatusCanceled:
			reason = "canceled"
		case TwilioCallStatusFailed:
			reason = TwilioCallStatusFailed
		}
		if reason != "" {
			updates.FailureReason = &reason
		}
	}
	if callSid != "" {
		updates.ChildCallSid = &callSid
	}
	if timestamp != "" {
		if t, perr := time.Parse(time.RFC1123Z, timestamp); perr == nil {
			switch effective {
			case TwilioCallStatusInitiated:
				updates.InitiatedAt = &t
			case TwilioCallStatusRinging:
				updates.RingingAt = &t
			case TwilioCallStatusInProgress, TwilioCallStatusAnswered:
				updates.AnsweredAt = &t
			case TwilioCallStatusCompleted, TwilioCallStatusBusy, TwilioCallStatusNoAnswer, TwilioCallStatusFailed, TwilioCallStatusCanceled:
				updates.CompletedAt = &t
			}
		}
	}
	// Compute ring_to_answer_sec when both timestamps known (best-effort, server-side derive keeps dto simple)
	if updates.AnsweredAt != nil {
		// Prefer exact ring_to_answer from ringing_at if present
		if updates.RingingAt != nil {
			if d := int(updates.AnsweredAt.Sub(*updates.RingingAt).Seconds()); d >= 0 {
				updates.RingToAnswerSec = &d
			}
		} else if updates.InitiatedAt != nil {
			// Fallback: derive from initiated_at if ringing_at missing
			if d := int(updates.AnsweredAt.Sub(*updates.InitiatedAt).Seconds()); d >= 0 {
				updates.RingToAnswerSec = &d
			}
		}
	}
	// Compute answer_to_complete_sec from CallDuration if provided, else derive from timestamps
	if callDuration != "" {
		if d, derr := strconv.Atoi(callDuration); derr == nil && d > 0 {
			updates.AnswerToCompleteSec = &d
		}
	} else if updates.AnsweredAt != nil && updates.CompletedAt != nil {
		if d := int(updates.CompletedAt.Sub(*updates.AnsweredAt).Seconds()); d >= 0 {
			updates.AnswerToCompleteSec = &d
		}
	}
	if sipCode != "" {
		if c, cerr := strconv.Atoi(sipCode); cerr == nil {
			updates.SipResponseCode = &c
		}
	}
	if s := r.FormValue("StirStatus"); s != "" {
		updates.StirStatus = &s
	}

	if err := uc.callQueueRepo.MergeCallForwardAnalytics(spanContext, origSid, updates); err != nil {
		if err == data.ErrCallForwardEventNotFound {
			orgID := cmncontext.GetOrgId(spanContext)
			evt := &data.CallForwardEvent{
				CallSid:        origSid,
				OrgID:          int(orgID),
				FromNumber:     from,
				ToNumber:       to,
				Status:         mappedStatus,
				DialCallStatus: dialPtr,
				CreatedAt:      time.Now(),
			}
			if createErr := uc.callQueueRepo.CreateCallForwardAttempt(spanContext, evt); createErr != nil {
				span.SetTag("error", "true")
				herosentry.CaptureException(spanContext, createErr, "Failed to create forward event for merge")
				log.Printf("CALLSTATUS(FORWARD): failed to create event for %s: %v", origSid, createErr)
			} else {
				_ = uc.callQueueRepo.MergeCallForwardAnalytics(spanContext, origSid, updates)
			}
		} else {
			span.SetTag("error", "true")
			herosentry.CaptureException(spanContext, err, "Failed to merge forward analytics")
			log.Printf("CALLSTATUS(FORWARD): failed to merge analytics for %s: %v", origSid, err)
		}
	}

	// Twilio expects 200 OK and no TwiML content
	w.WriteHeader(http.StatusOK)
}

// HandleAgentDialStatusRequest processes Twilio status callbacks for the agent leg of calls
func (uc *cellularCallUsecase) HandleAgentDialStatusRequest(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	// herosentry.HTTPMiddleware should have already created a transaction
	// so we just create a child span
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "HandleAgentDialStatusRequest")
	defer finishSpan()

	// Parse form data
	err := r.ParseForm()
	if err != nil {
		span.SetTag("error", "true")
		herosentry.CaptureException(spanContext, err, "Failed to parse form data")
		log.Printf("DEBUG: Error parsing form: %v", err)
	}

	// Extract key parameters
	agentLegSid := r.FormValue("CallSid") // This is the agent leg SID
	callStatus := r.FormValue("CallStatus")
	customerSid := r.URL.Query().Get("customerSid")     // This is the customer leg SID
	sessionSuffix := r.URL.Query().Get("sessionSuffix") // Session suffix for identity consistency
	callDuration := r.FormValue("CallDuration")

	span.SetTag("agent_leg_sid", agentLegSid)
	span.SetTag("call_status", callStatus)
	span.SetTag("customer_sid", customerSid)
	span.SetTag("session_suffix", sessionSuffix)
	span.SetTag("call_duration", callDuration)

	log.Printf("AGENT DIAL STATUS: AgentLegSid=%s, Status=%s, CustomerSid=%s, SessionSuffix=%s, Duration=%s",
		agentLegSid, callStatus, customerSid, sessionSuffix, callDuration)

	// Validate required parameters
	if customerSid == "" {
		err := errors.New("missing customerSid parameter")
		span.SetTag("error", "true")
		herosentry.CaptureException(spanContext, err, "Missing customerSid in agent dial status callback")
		log.Printf("Error: Missing customerSid in agent dial status callback")
		http.Error(w, "Missing customerSid parameter", http.StatusBadRequest)
		return
	}

	if agentLegSid == "" || callStatus == "" {
		err := errors.New("missing required parameters")
		span.SetTag("error", "true")
		herosentry.CaptureException(spanContext, err, "Missing required parameters in agent dial status callback")
		log.Printf("Error: `Missing required parameters` in agent dial status callback")
		http.Error(w, "Missing required parameters", http.StatusBadRequest)
		return
	}

	// Retrieve the call record using the customer SID
	call, found, err := uc.callQueueRepo.GetCallByCallSID(spanContext, customerSid)
	if err != nil {
		span.SetTag("error", "true")
		herosentry.CaptureException(spanContext, err, "Failed to retrieve call record")
		log.Printf("Error retrieving call with SID %s: %v", customerSid, err)
		http.Error(w, "Error retrieving call record", http.StatusInternalServerError)
		return
	}

	if !found {
		err := fmt.Errorf("call with SID %s not found", customerSid)
		span.SetTag("error", "true")
		span.SetTag("call_not_found", "true")
		herosentry.CaptureException(spanContext, err, "Call not found in agent dial status callback")
		log.Printf("Call with SID %s not found", customerSid)
		http.Error(w, "Call not found", http.StatusNotFound)
		return
	}

	// Log the current state before processing
	log.Printf("Call %s current state: %s", customerSid, call.State)

	// Process based on status
	switch callStatus {
	case TwilioCallStatusInitiated, TwilioCallStatusRinging:
		// No state change for pending reach
		log.Printf("AGENT DIAL STATUS: Agent leg %s for customer call %s is %s.", agentLegSid, customerSid, callStatus)

	case TwilioCallStatusInProgress: // Agent leg answered
		log.Printf("AGENT DIAL STATUS: Agent leg %s for customer call %s is now 'in-progress' (answered). Current customer call state: %s", agentLegSid, customerSid, call.State)

		// Debug log the call details
		attributesJSON, _ := json.Marshal(call.Attributes)
		log.Printf("DEBUG: Call details before transition: SID=%s, State=%s, AssetID=%s, Attributes=%s",
			call.CallSID, call.State, call.AssetID, string(attributesJSON))

		if call.State == data.CallStatePendingSelectiveAssign {
			// Transition the customer call to active
			now := time.Now()
			call.CallStartTime = &now
			call.State = data.CallStateActive
			if call.Attributes == nil {
				call.Attributes = make(map[string]string)
			}
			call.Attributes["agentLegSid"] = agentLegSid

			// Store the session suffix from the status callback URL for resume operations
			if sessionSuffix != "" {
				call.Attributes["sessionSuffix"] = sessionSuffix
				log.Printf("Stored session suffix '%s' for call %s", sessionSuffix, customerSid)
			}

			// Debug log the call details before storing
			attributesJSON2, _ := json.Marshal(call.Attributes)
			log.Printf("DEBUG: Call details after state change, before store: SID=%s, State=%s, AssetID=%s, Attributes=%s",
				call.CallSID, call.State, call.AssetID, string(attributesJSON2))

			if err := uc.callQueueRepo.StoreActiveCall(ctx, call); err != nil {
				log.Printf("Error updating customer call %s to active after agent leg answered: %v", customerSid, err)
				http.Error(w, "Error updating call state", http.StatusInternalServerError)
				return
			}

			// Verify the call was updated by fetching it again
			updatedCall, found, err := uc.callQueueRepo.GetCallByCallSID(ctx, customerSid)
			if err != nil || !found {
				log.Printf("DEBUG: Failed to verify call update: err=%v, found=%v", err, found)
			} else {
				log.Printf("DEBUG: Call after update: SID=%s, State=%s, AssetID=%s",
					updatedCall.CallSID, updatedCall.State, updatedCall.AssetID)
			}

			log.Printf("Customer call %s successfully updated to active, connected to agent leg %s.", customerSid, agentLegSid)
		} else {
			// Unexpected agent leg; hang it up to maintain state consistency
			log.Printf("AGENT DIAL STATUS: Customer call %s is in state '%s', not '%s'. Agent leg %s (%s) answered unexpectedly. Terminating this agent leg.",
				customerSid, call.State, data.CallStatePendingSelectiveAssign, agentLegSid, callStatus)

			hangupTwiML, twimlErr := twiml.HangupResponse()
			if twimlErr != nil {
				log.Printf("Error generating hangup TwiML for unexpected agent leg %s: %v", agentLegSid, twimlErr)
			} else {
				if err := uc.twilioClient.ModifyCall(ctx, agentLegSid, hangupTwiML); err != nil {
					log.Printf("Error sending hangup command to unexpected agent leg %s: %v", agentLegSid, err)
				} else {
					log.Printf("Successfully sent hangup command to unexpected agent leg %s.", agentLegSid)
				}
			}
		}

	case TwilioCallStatusCompleted, TwilioCallStatusBusy, TwilioCallStatusNoAnswer, TwilioCallStatusFailed, TwilioCallStatusCanceled:
		// Terminal statuses for the agent leg
		log.Printf("AGENT DIAL STATUS: Agent leg %s for customer call %s received terminal status: %s. Current customer call state: %s", agentLegSid, customerSid, callStatus, call.State)

		if call.State == data.CallStateHold {
			log.Printf("AGENT DIAL STATUS: Agent leg %s terminated with status %s, but customer call %s is on hold. Customer call will remain on hold.", agentLegSid, callStatus, customerSid)
			if call.Attributes == nil {
				call.Attributes = make(map[string]string)
			}
			call.Attributes[fmt.Sprintf("agentLeg_%s_status", agentLegSid)] = callStatus
			if callDuration != "" {
				call.Attributes[fmt.Sprintf("agentLeg_%s_duration", agentLegSid)] = callDuration
			}
			if err := uc.callQueueRepo.StoreActiveCall(ctx, call); err != nil {
				log.Printf("Error updating attributes for on-hold call %s after agent leg %s terminated: %v", customerSid, agentLegSid, err)
			}
			w.WriteHeader(http.StatusOK)
			return
		}

		if call.State != data.CallStateEnded {
			if call.CallEndTime == nil {
				now := time.Now()
				call.CallEndTime = &now
			}
			if call.Attributes == nil {
				call.Attributes = make(map[string]string)
			}
			if callDuration != "" {
				call.Attributes["twilioCallDuration"] = callDuration
			}
			errorCode := r.FormValue("ErrorCode")
			if errorCode != "" {
				call.Attributes["twilioErrorCode"] = errorCode
				call.Attributes["twilioErrorMessage"] = r.FormValue("ErrorMessage")
			}
			call.Attributes["twilioCallStatus"] = callStatus
			if err := uc.callQueueRepo.EndCall(ctx, customerSid); err != nil {
				log.Printf("Error ending call %s: %v", customerSid, err)
				http.Error(w, "Error updating call state", http.StatusInternalServerError)
				return
			}
			log.Printf("Call %s ended with status %s", customerSid, callStatus)
		} else {
			log.Printf("Call %s already in ended state", customerSid)
		}

	default:
		log.Printf("Unhandled call status '%s' for call %s", callStatus, customerSid)
	}

	// Return 200 OK to acknowledge receipt
	span.SetTag("http.status_code", "200")
	w.WriteHeader(http.StatusOK)
}

// HandleConnectAgentTwiMLRequest generates TwiML to connect an agent to a specific customer call
func (uc *cellularCallUsecase) HandleConnectAgentTwiMLRequest(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	// herosentry.HTTPMiddleware should have already created a transaction
	// so we just create a child span
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "HandleConnectAgentTwiMLRequest")
	defer finishSpan()

	log.Printf("HandleConnectAgentTwiMLRequest: Received request. Method: %s, URL: %s", r.Method, r.URL.String())
	parsedQueryParams := r.URL.Query()

	// Retrieve parameters using the correct key "agentId"
	agentID := parsedQueryParams.Get("agentId")
	customerCallSID := parsedQueryParams.Get("customerSid")
	sessionSuffix := parsedQueryParams.Get("sessionSuffix")

	span.SetTag("agent_id", agentID)
	span.SetTag("customer_sid", customerCallSID)
	span.SetTag("session_suffix", sessionSuffix)

	log.Printf("HandleConnectAgentTwiMLRequest: Retrieved agentId: '%s', customerSid: '%s', sessionSuffix: '%s'", agentID, customerCallSID, sessionSuffix)

	if agentID == "" || customerCallSID == "" {
		err := errors.New("missing required parameters")
		span.SetTag("error", "true")
		herosentry.CaptureException(spanContext, err, "Missing required parameters in connectAgent TwiML request")
		log.Printf("HandleConnectAgentTwiMLRequest: Missing required parameters: agentId_is_empty=%t, customerSid_is_empty=%t. Values: agentId='%s', customerSid='%s'", agentID == "", customerCallSID == "", agentID, customerCallSID)
		http.Error(w, "Missing required parameters", http.StatusBadRequest)
		return
	}

	// Get org Twilio details for caller ID
	orgTwilioDetails, err := uc.callQueueRepo.GetOrgTwilioDetails(spanContext)
	if err != nil {
		log.Printf("Error getting org Twilio details: %v", err)
		http.Error(w, "Error generating TwiML", http.StatusInternalServerError)
		return
	}

	// Look up org twilio users
	getOrgAPIUserPrivateRequest := &orgs.GetOrgAPIUserPrivateByIdRequest{
		UserId: orgTwilioDetails.TwilioApiUserId,
	}
	basicTwilioUserAuth, err := uc.orgClient.GetOrgAPIUserPrivateById(spanContext, connect.NewRequest(getOrgAPIUserPrivateRequest))
	if err != nil {
		log.Printf("Error getting org API user: %v", err)
		http.Error(w, "Error generating TwiML", http.StatusInternalServerError)
		return
	}

	// Build the status callback URL with the customer SID and session suffix
	publicCommunicationsServiceURL := uc.commsServerPublicDomain
	basicUserAuth := basicTwilioUserAuth.Msg.OrgApiUser.Id + ":" + basicTwilioUserAuth.Msg.OrgApiUser.RawPassword
	statusCallbackURL := fmt.Sprintf("https://%s@%s/hero.communications.v1.TwilioWebhookService/twilio/agent-dial-status?customerSid=%s&sessionSuffix=%s", basicUserAuth, publicCommunicationsServiceURL, customerCallSID, sessionSuffix)

	// Format the agentID using the same logic as token generation to match client identity
	// Include session suffix if provided to ensure consistent identity formatting
	_, agentIdentitySpan, agentIdentityFinishSpan := herosentry.StartSpan(spanContext, "communications.format_agent_identity")
	agentIdentitySpan.SetTag("agent_id", agentID)
	agentIdentitySpan.SetTag("session_suffix", sessionSuffix)
	formattedAgentID, err := uc.formatAgentIdentityWithSession(spanContext, agentID, sessionSuffix)
	agentIdentityFinishSpan()
	if err != nil {
		agentIdentitySpan.SetTag("error", "true")
		agentIdentitySpan.SetTag("fallback_used", "true")
		herosentry.CaptureException(spanContext, err, "Failed to format agent identity")
		log.Printf("Warning: Failed to format agent identity for %s, using raw ID: %v", agentID, err)
		formattedAgentID = agentID // Fallback to raw ID
	} else {
		agentIdentitySpan.SetTag("formatted_identity", formattedAgentID)
	}

	// Generate the TwiML
	_, twimlGenerationSpan, twimlFinishSpan := herosentry.StartSpan(spanContext, "communications.generate_agent_connect_twiml")
	twimlGenerationSpan.SetTag("agent_identity", formattedAgentID)
	twimlGenerationSpan.SetTag("customer_sid", customerCallSID)
	// Use formatted agent ID to match the client identity from token generation
	twimlXML, err := twiml.AgentConnectTwiML(formattedAgentID, customerCallSID, orgTwilioDetails.TwilioNumber, statusCallbackURL)
	twimlFinishSpan()
	if err != nil {
		twimlGenerationSpan.SetTag("error", "true")
		span.SetTag("error", "true")
		herosentry.CaptureException(spanContext, err, "Failed to generate agent connect TwiML")
		log.Printf("Error generating TwiML: %v", err)
		http.Error(w, "Error generating TwiML", http.StatusInternalServerError)
		return
	} else {
		twimlGenerationSpan.SetTag("twiml_length", fmt.Sprintf("%d", len(twimlXML)))
	}

	// Set content type and write response
	w.Header().Set("Content-Type", "text/xml")
	_, err = w.Write([]byte(twimlXML))
	if err != nil {
		span.SetTag("error", "true")
		herosentry.CaptureException(spanContext, err, "Failed to write TwiML response")
		log.Printf("Error writing TwiML response: %v", err)
		return
	}
	span.SetTag("http.status_code", "200")
}

// HandleWaitHoldRequest generates smart TwiML for queue wait and hold messaging
func (uc *cellularCallUsecase) HandleWaitHoldRequest(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	action := r.URL.Query().Get("action") // "enqueue" or "hold"

	// herosentry.HTTPMiddleware should have already created a transaction
	// so we just create a child span
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "HandleWaitHoldRequest")
	defer finishSpan()

	span.SetTag("action", action)
	span.SetTag("http.method", r.Method)
	span.SetTag("http.url", r.URL.String())

	log.Printf("HandleWaitHoldRequest: action=%s", action)

	var twimlResponse string
	var err error

	switch action {
	case "enqueue":
		// Get org name for dynamic messaging from existing Twilio details
		orgTwilioDetails, orgErr := uc.callQueueRepo.GetOrgTwilioDetails(ctx)
		var orgName string
		if orgErr != nil {
			log.Printf("Warning: Failed to get org details for dynamic messaging: %v", orgErr)
			orgName = "" // Will use fallback message
		} else {
			orgName = orgTwilioDetails.OrgName
		}
		twimlResponse, err = twiml.EnqueueWaitResponse(orgName)
	case "hold":
		// Construct the hold URL for looping (same URL that called this endpoint)
		holdURL := fmt.Sprintf("https://%s%s", uc.commsServerPublicDomain, r.URL.String())
		twimlResponse, err = twiml.HoldWaitResponse(holdURL)
	default:
		err := fmt.Errorf("invalid action parameter: %s", action)
		span.SetTag("error", "true")
		herosentry.CaptureException(spanContext, err, "Invalid action parameter in wait/hold request")
		log.Printf("Invalid action parameter: %s", action)
		http.Error(w, "Invalid action parameter", http.StatusBadRequest)
		return
	}

	if err != nil {
		span.SetTag("error", "true")
		herosentry.CaptureException(spanContext, err, "Failed to generate wait/hold TwiML")
		log.Printf("Error generating wait/hold TwiML: %v", err)
		http.Error(w, "Error generating TwiML", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "text/xml")
	fmt.Fprint(w, twimlResponse)
	span.SetTag("http.status_code", "200")
}

// HandleForwardActionRequest processes Dial action callbacks from call forwarding attempts
func (uc *cellularCallUsecase) HandleForwardActionRequest(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	// Extract Twilio webhook parameters for span tagging
	dialCallStatus := r.FormValue("DialCallStatus")
	dialCallSid := r.FormValue("DialCallSid")
	callSid := r.FormValue("CallSid")         // Original call SID
	from := r.FormValue("From")               // Original caller
	to := r.FormValue("To")                   // Forwarded destination
	dialBridged := r.FormValue("DialBridged") // "true" if connected

	// herosentry.HTTPMiddleware should have already created a transaction
	// so we just create a child span
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "HandleForwardActionRequest")
	defer finishSpan()

	// Tag the span with key parameters for observability
	span.SetTag("dial_call_status", dialCallStatus)
	span.SetTag("dial_call_sid", dialCallSid)
	span.SetTag("call_sid", callSid)
	span.SetTag("from", from)
	span.SetTag("to", to)
	span.SetTag("dial_bridged", dialBridged)
	span.SetTag("http.method", r.Method)
	span.SetTag("http.url", r.URL.String())

	log.Printf("FORWARD_ACTION: CallSid=%s, DialCallStatus=%s, DialCallSid=%s, From=%s, To=%s, DialBridged=%s",
		callSid, dialCallStatus, dialCallSid, from, to, dialBridged)

	// Validate required parameters
	if callSid == "" {
		err := fmt.Errorf("CallSid parameter is required")
		span.SetTag("error", "true")
		herosentry.CaptureException(spanContext, err, "Missing required CallSid parameter")
		log.Printf("ERROR: Missing CallSid parameter in forward action request")
		http.Error(w, "Missing CallSid parameter", http.StatusBadRequest)
		return
	}

	if dialCallStatus == "" {
		err := fmt.Errorf("DialCallStatus parameter is required")
		span.SetTag("error", "true")
		herosentry.CaptureException(spanContext, err, "Missing required DialCallStatus parameter")
		log.Printf("ERROR: Missing DialCallStatus parameter in forward action request")
		http.Error(w, "Missing DialCallStatus parameter", http.StatusBadRequest)
		return
	}

	// Validate DialCallStatus against known constants
	validStatuses := []string{
		TwilioCallStatusCompleted,
		TwilioCallStatusAnswered,
		TwilioCallStatusBusy,
		TwilioCallStatusNoAnswer,
		TwilioCallStatusFailed,
		TwilioCallStatusCanceled,
	}
	isValidStatus := false
	for _, status := range validStatuses {
		if dialCallStatus == status {
			isValidStatus = true
			break
		}
	}

	if !isValidStatus {
		err := fmt.Errorf("invalid DialCallStatus: %s", dialCallStatus)
		span.SetTag("error", "true")
		herosentry.CaptureException(spanContext, err, "Invalid DialCallStatus parameter")
		log.Printf("ERROR: Invalid DialCallStatus: %s", dialCallStatus)
		http.Error(w, "Invalid DialCallStatus parameter", http.StatusBadRequest)
		return
	}

	log.Printf("FORWARD_ACTION: Successfully parsed and validated webhook parameters")
	span.SetTag("validation_passed", "true")

	// Get org ID from context for event logging
	orgID := cmncontext.GetOrgId(spanContext)
	span.SetTag("org_id", fmt.Sprintf("%d", orgID))

	// Map Twilio dial status to event status
	mappedStatus := TwilioCallStatusFailed
	if dialBridged == "true" || dialCallStatus == TwilioCallStatusAnswered || dialCallStatus == TwilioCallStatusCompleted {
		mappedStatus = "completed"
	}

	// Merge analytics from forward-action as finalizer
	updateSpanCtx, updateSpan, _ := herosentry.StartSpan(spanContext, "HandleForwardActionRequest.UpdateForwardEvent")
	updateSpan.SetTag("call_sid", callSid)
	updateSpan.SetTag("mapped_status", mappedStatus)
	updates := data.CallForwardAnalyticsUpdate{}
	// Status as finalizer
	updates.Status = &mappedStatus
	// Correlation & connection outcome
	if dialCallSid != "" {
		updates.ChildCallSid = &dialCallSid
	}
	bridgedBool := (strings.ToLower(dialBridged) == "true")
	updates.Bridged = &bridgedBool
	// Talk time from DialCallDuration maps to answer_to_complete_sec
	if s := r.FormValue("DialCallDuration"); s != "" {
		if d, derr := strconv.Atoi(s); derr == nil && d >= 0 {
			updates.AnswerToCompleteSec = &d
		}
	}
	// Diagnostics
	if s := r.FormValue("SipResponseCode"); s != "" {
		if c, cerr := strconv.Atoi(s); cerr == nil {
			updates.SipResponseCode = &c
		}
	}
	if s := r.FormValue("StirStatus"); s != "" {
		updates.StirStatus = &s
	}

	if err := uc.callQueueRepo.MergeCallForwardAnalytics(updateSpanCtx, callSid, updates); err != nil {
		if err == data.ErrCallForwardEventNotFound {
			// Create and retry merge
			createSpanCtx, createSpan, createFinish := herosentry.StartSpan(spanContext, "HandleForwardActionRequest.CreateForwardEventFallback")
			createSpan.SetTag("call_sid", callSid)
			fallbackEvent := &data.CallForwardEvent{
				CallSid:        callSid,
				OrgID:          int(orgID),
				FromNumber:     from,
				ToNumber:       to,
				Status:         mappedStatus,
				DialCallStatus: &dialCallStatus,
				CreatedAt:      time.Now(),
			}
			if createErr := uc.callQueueRepo.CreateCallForwardAttempt(createSpanCtx, fallbackEvent); createErr != nil {
				createSpan.SetTag("error", "true")
				herosentry.CaptureException(createSpanCtx, createErr, "Failed to create fallback call forward event")
				log.Printf("WARNING: Failed to create fallback forward event for CallSid=%s: %v", callSid, createErr)
				span.SetTag("forward_event_logged", "false")
			} else {
				_ = uc.callQueueRepo.MergeCallForwardAnalytics(updateSpanCtx, callSid, updates)
				span.SetTag("forward_event_logged", "true")
			}
			createFinish()
		} else {
			updateSpan.SetTag("error", "true")
			herosentry.CaptureException(updateSpanCtx, err, "Failed to merge call forward analytics")
			log.Printf("WARNING: Failed to merge forward analytics for CallSid=%s: %v", callSid, err)
			span.SetTag("forward_event_logged", "false")
		}
	} else {
		span.SetTag("forward_event_logged", "true")
	}

	// Return empty TwiML response for all statuses (basic implementation)
	w.Header().Set("Content-Type", "text/xml")
	fmt.Fprint(w, `<?xml version="1.0" encoding="UTF-8"?><Response></Response>`)
	span.SetTag("http.status_code", "200")
}
